import { defineConfig } from '@ice/app';
import request from '@ice/plugin-request';
import store from '@ice/plugin-store';
import auth from '@ice/plugin-auth';
import def from '@ali/ice-plugin-def';

// The project config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/config
const minify = process.env.NODE_ENV === 'production' ? 'swc' : false;
export default defineConfig(() => ({
  ssg: false,
  minify,
  codeSplitting: 'page-vendors',
  plugins: [request(), store(), auth(), def()],
  compileDependencies: false,
  mock: {
    exclude: ['orange-be/HomeController'],
  },
  // 配置 webpack 处理 SVG
  webpack: config => {
    // 确保 module 和 rules 存在
    if (!config.module) {
      config.module = { rules: [] };
    }
    if (!config.module.rules) {
      config.module.rules = [];
    }

    // 移除默认的 SVG 处理规则
    config.module.rules = config.module.rules.filter(rule => {
      if (rule && typeof rule === 'object' && 'test' in rule && rule.test) {
        const testStr = rule.test.toString();
        return !testStr.includes('svg');
      }
      return true;
    });

    // 添加 SVG 作为 React 组件的处理规则，禁用 SVGO 的路径数据转换
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: {
                      convertPathData: false,
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    });

    return config;
  },
}));

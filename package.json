{"name": "orange view next", "version": "0.0.1", "description": "新 Orange 控制台", "dependencies": {"@ali/aes-tracker": "^3.3.9", "@ali/aes-tracker-plugin-api": "^3.1.3", "@ali/aes-tracker-plugin-autolog": "^3.0.13", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-perf": "^3.1.0", "@ali/aes-tracker-plugin-pv": "^3.0.6", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ali/mc-uikit": "0.6.21-beta.9", "@ali/mtl-themes": "1.1.0-beta.1", "@ant-design/charts": "^2.2.0", "@ant-design/icons": "^4.7.0", "@ant-design/pro-chat": "^1.15.3", "@ant-design/pro-components": "^2.7.19", "@ant-design/pro-form": "^2.2.8", "@ant-design/pro-layout": "^7.1.6", "@ant-design/pro-table": "^3.0.17", "@ice/app": "3.6.0-canary-588e09e-20241205093545", "@ice/jsx-runtime": "0.3.1", "@ice/runtime": "^1.5.1", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-slot": "^1.1.0", "antd": "^5.22.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dompurify": "^3.1.7", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.452.0", "moment": "^2.30.1", "qs": "^6.13.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-use": "^17.5.1", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.2", "zustand": "^5.0.7"}, "devDependencies": {"@ali/easyapi-sdk": "^0.1.3", "@ali/eslint-config-att": "^1.0.6", "@ali/ice-plugin-def": "^1.2.4", "@applint/spec": "^1.2.3", "@ice/plugin-auth": "^1.0.0", "@ice/plugin-request": "^1.0.0", "@ice/plugin-store": "^1.0.0", "@svgr/webpack": "^8.1.0", "@types/dompurify": "^3.0.5", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.35.0", "globby": "^14.1.0", "monaco-editor-webpack-plugin": "^7.1.0", "stylelint": "^15.2.0", "tailwindcss": "^3.4.13", "typescript": "^4.8.4"}, "scripts": {"start": "ice start --speedup", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix", "easyapi": "tnpm run services && tnpm run mock", "services": "easy-api services", "mock": "easy-api mock"}, "overrides": {"@ctrl/tinycolor": "4.1.0", "@ice/app": "3.6.0-canary-588e09e-20241205093545"}, "publishConfig": {"access": "public"}, "repository": "**************:ice-lab/react-materials.git"}
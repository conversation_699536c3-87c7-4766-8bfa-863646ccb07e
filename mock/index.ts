export default {
  "/api/version/queryResourceDetail.json": {
    errorCode: "",
    errorMsg: "",
    model: {
      appKey: "21380790",
      arg1: "bb029dea92da48e1837f402b83b77370",
      arg2: "*",
      data: '{"appKey":"21380790","appVersion":"*","content":{"key1":"value1","key10":"{\\"a\\":\\"a1\\",\\"中文\\":\\"中文\\"}","key2":"key2","key3":"value3","中文key":"中文value"},"createTime":"1728441145187","id":"bb029dea92da48e1837f402b83b77370","loadLevel":"DEFAULT","name":"ruanying_test","resourceId":"ns2b7ce28997cd4e73ab09195e50f0629e.json","type":"STANDARD","version":"2120241009103225187"}',
      gmtCreate: 1728441145000,
      gmtCreateTime: "2024-10-09 10:32:25",
      gmtModified: 1728441145000,
      gmtModifiedTime: "2024-10-09 10:32:25",
      id: 36570943,
      isAvailable: "y",
      md5: "4d3869e9358f037067299fa297fd90f5",
      resourceId: "ns2b7ce28997cd4e73ab09195e50f0629e.json",
      srcContent:
        '# 以下是一个配置例子,content是一个标准的.properties格式文件,UTF-8编码\n# 这是一行注释\nkey1=value1\n\n# 值为空的配置\nkey2=key2\n\n\n# 有值的配置\nkey3=value3\n\n# 中文的key 中文的value\n中文key=中文value\n\n# 值是一个json串的例子\nkey10={"a":"a1","中文":"中文"}',
      type: 3,
      valid: true,
    },
    success: true,
  },
  "/api/namespace/getNamespaceDetail.json": {
    errorCode: "",
    errorMsg: "",
    model: {
      appBO: {
        appDetail: "淘宝iPhone客户端",
        appId: 2017031500124,
        appKey: "21380790",
        appMark: "taobao4iphone",
        appName: "淘宝iPhone客户端-ios",
        basicAppKey: "12087020",
        gmtCreate: 1359597872000,
        isAvailable: "y",
        motuAppId: "12087020@iphoneos",
        mtlId: "6",
        osType: 1,
        valid: true,
      },
      changeBO: {
        appKey: "21380790",
        changeVersion: "3120241008174814547",
        gmtCreate: 1728367387000,
        gmtCreateTime: "2024-10-08 14:03:07",
        gmtModified: 1728380894000,
        gmtModifiedTime: "2024-10-08 17:48:14",
        id: 1004621,
        isAvailable: "y",
        loadLevel: 0,
        metas:
          '{"appVersion":"*","candidates":[{"match":"app_ver>10.40.20","md5":"a988e0732d9f1fe50b27244ff5469ab7","resourceId":"nsce3ccc727e6640c3b8322fb457f1cbcd.json","version":"2120241008140306996"},{"match":"app_ver=10.40.21","md5":"8684efc4951ed67f27d57577b4d248c1","resourceId":"nsd132567aadd847e395181bbf21c7856d.json","version":"2120241002141835322"},{"match":"app_ver>=10.40.21","md5":"80a524f6f113f009e11726e3c09fbfd2","resourceId":"ns70ae71dc8884486e8cbfc9d412f4736e.json","version":"2120240929181118523"},{"match":"app_ver=10.22.30.5411","md5":"3e48faeeebf3e2774c11417401aa69c4","resourceId":"nsbcf3945ace2e479fb00bcff1636e3ea3.json","version":"2120230426102407858"},{"match":"app_ver=10.14.15.2","md5":"6908f510995aac20db0db79be5c21c0d","resourceId":"ns0d19e4d899da47eebe82b5407f565ff9.json","version":"2120220729170459747"},{"match":"app_ver=10.14.10.8571","md5":"bcd50229f00aa93a7863439981116461","resourceId":"ns9fc20262fb3f4b7ca87043647fa9aefb.json","version":"2120220727112020475"}],"changeVersion":"3120241008174814547","highLazy":1,"loadLevel":"DEFAULT","md5":"4b467d2c3ef78774b6f8d222b67cace7","name":"launch_link_passive","resourceId":"nsdee8f921d6fd4ca48b7ff2c719fccdff.json","type":"STANDARD","version":"2120220725203315486"}',
        name: "launch_link_passive",
        namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
        operator: "149016",
        status: 200,
        type: 1,
        valid: true,
        versionVersion: "2120241008140306996",
        versions:
          "2120241008140306996,2120241002141835322,2120240929181118523,2120230426102407858,2120220729170459747,2120220727112020475,2120220725203315486",
      },
      list: [
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "149016",
          gmtCreate: 1728367387000,
          gmtCreateTime: "2024-10-08 14:03:07",
          gmtModified: 1728380894000,
          gmtModifiedTime: "2024-10-08 17:48:14",
          gmtPublish: 1728380894000,
          gmtPublishTime: "2024-10-08 17:48:14",
          grayVersions:
            '[{"strategyType":"POSITIVE","version":"2120241008140306996"},{"strategyType":"UNCHANGED","version":"2120241002141835322"},{"strategyType":"UNCHANGED","version":"2120240929181118523"},{"strategyType":"UNCHANGED","version":"2120230426102407858"},{"strategyType":"UNCHANGED","version":"2120220729170459747"},{"strategyType":"UNCHANGED","version":"2120220727112020475"},{"strategyType":"UNCHANGED","version":"2120220725203315486"}]',
          id: 1632002,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "a988e0732d9f1fe50b27244ff5469ab7",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          previousResourceId: "nsd132567aadd847e395181bbf21c7856d.json",
          resourceId: "nsce3ccc727e6640c3b8322fb457f1cbcd.json",
          reviewer: "149016",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver>10.40.20&app_ver<=10.40.21&app_ver<9.40.21",
          type: 1,
          valid: true,
          version: "2120241008140306996",
          versions:
            "-,2120241002141835322,2120240929181118523,2120230426102407858,2120220729170459747,2120220727112020475,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "180856",
          gmtCreate: 1727849915000,
          gmtCreateTime: "2024-10-02 14:18:35",
          gmtModified: 1727923528000,
          gmtModifiedTime: "2024-10-03 10:45:28",
          gmtPublish: 1727923528000,
          gmtPublishTime: "2024-10-03 10:45:28",
          grayVersions:
            '[{"strategyType":"POSITIVE","version":"2120241002141835322"},{"strategyType":"UNCHANGED","version":"2120240929181118523"},{"strategyType":"UNCHANGED","version":"2120230426102407858"},{"strategyType":"UNCHANGED","version":"2120220729170459747"},{"strategyType":"UNCHANGED","version":"2120220727112020475"},{"strategyType":"UNCHANGED","version":"2120220725203315486"}]',
          id: 1631363,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "8684efc4951ed67f27d57577b4d248c1",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          previousResourceId: "ns70ae71dc8884486e8cbfc9d412f4736e.json",
          resourceId: "nsd132567aadd847e395181bbf21c7856d.json",
          reviewer: "149016",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver=10.40.21",
          type: 1,
          valid: true,
          version: "2120241002141835322",
          versions:
            "-,2120240929181118523,2120230426102407858,2120220729170459747,2120220727112020475,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "180856",
          gmtCreate: 1727604678000,
          gmtCreateTime: "2024-09-29 18:11:18",
          gmtModified: 1727614752000,
          gmtModifiedTime: "2024-09-29 20:59:12",
          gmtPublish: 1727614752000,
          gmtPublishTime: "2024-09-29 20:59:12",
          grayVersions:
            '[{"strategyType":"POSITIVE","version":"2120240929181118523"},{"strategyType":"UNCHANGED","version":"2120230426102407858"},{"strategyType":"UNCHANGED","version":"2120220729170459747"},{"strategyType":"UNCHANGED","version":"2120220727112020475"},{"strategyType":"UNCHANGED","version":"2120220725203315486"}]',
          id: 1630105,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "80a524f6f113f009e11726e3c09fbfd2",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          previousResourceId: "nsbcf3945ace2e479fb00bcff1636e3ea3.json",
          resourceId: "ns70ae71dc8884486e8cbfc9d412f4736e.json",
          reviewer: "180856",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver>=10.40.21",
          type: 1,
          valid: true,
          version: "2120240929181118523",
          versions:
            "-,2120230426102407858,2120220729170459747,2120220727112020475,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "324600",
          gmtCreate: 1682475847000,
          gmtCreateTime: "2023-04-26 10:24:07",
          gmtModified: 1682476375000,
          gmtModifiedTime: "2023-04-26 10:32:55",
          gmtPublish: 1682476375000,
          gmtPublishTime: "2023-04-26 10:32:55",
          id: 1225953,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "3e48faeeebf3e2774c11417401aa69c4",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          previousResourceId: "ns0d19e4d899da47eebe82b5407f565ff9.json",
          resourceId: "nsbcf3945ace2e479fb00bcff1636e3ea3.json",
          reviewer: "324600",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver=10.22.30.5411",
          type: 1,
          valid: true,
          version: "2120230426102407858",
          versions:
            "-,2120220729170459747,2120220727112020475,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "324600",
          gmtCreate: 1659085499000,
          gmtCreateTime: "2022-07-29 17:04:59",
          gmtModified: 1659086700000,
          gmtModifiedTime: "2022-07-29 17:25:00",
          gmtPublish: 1659086700000,
          gmtPublishTime: "2022-07-29 17:25:00",
          id: 979048,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "6908f510995aac20db0db79be5c21c0d",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: '{"2120220729141611856":4}',
          overwriteStrategyVersions: "2120220729141611856",
          previousResourceId: "ns3be3e4e05fbf42fc9f2b21c2fe2cb0ee.json",
          resourceId: "ns0d19e4d899da47eebe82b5407f565ff9.json",
          reviewer: "324600",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver=10.14.15.2",
          type: 1,
          valid: true,
          version: "2120220729170459747",
          versions: "-,2120220727112020475,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "324600",
          gmtCreate: 1658892020000,
          gmtCreateTime: "2022-07-27 11:20:20",
          gmtModified: 1658903327000,
          gmtModifiedTime: "2022-07-27 14:28:47",
          gmtPublish: 1658903327000,
          gmtPublishTime: "2022-07-27 14:28:47",
          id: 977613,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "bcd50229f00aa93a7863439981116461",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: '{"2120220726160727205":3}',
          previousResourceId: "ns778eaa4900184dcaa18b94369f2ca80a.json",
          resourceId: "ns9fc20262fb3f4b7ca87043647fa9aefb.json",
          reviewer: "324600",
          source: 0,
          sourceData: "",
          status: 200,
          strategy: "app_ver=10.14.10.8571",
          type: 1,
          valid: true,
          version: "2120220727112020475",
          versions: "-,2120220725203315486",
        },
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "324600",
          gmtCreate: 1658752395000,
          gmtCreateTime: "2022-07-25 20:33:15",
          gmtModified: 1658807785000,
          gmtModifiedTime: "2022-07-26 11:56:25",
          gmtPublish: 1658807785000,
          gmtPublishTime: "2022-07-26 11:56:25",
          id: 976952,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "4b467d2c3ef78774b6f8d222b67cace7",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          resourceId: "nsdee8f921d6fd4ca48b7ff2c719fccdff.json",
          reviewer: "324600",
          source: 0,
          sourceData: "",
          status: 200,
          type: 1,
          valid: true,
          version: "2120220725203315486",
          versions: "-",
        },
      ],
      namespaceBO: {
        appKeyOrGroup: "21380790",
        auditingFlag: "free",
        creator: "324600",
        detail: "启动外链相关配置",
        gmtCreate: 1658752035000,
        gmtCreateTime: "2022-07-25 20:27:15",
        gmtModified: 1727604609000,
        gmtModifiedTime: "2024-09-29 18:10:09",
        id: 8914,
        isAvailable: "y",
        loadLevel: 0,
        modules: "",
        name: "launch_link_passive",
        namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
        owners: "324600,67343,146282,320484,246919,180856,149016",
        testers: "324600,111402,366948,180856,149016",
        type: 1,
        valid: true,
      },
      noStrategyList: [
        {
          appKey: "21380790",
          appVersion: "*",
          creator: "324600",
          gmtCreate: 1658752395000,
          gmtCreateTime: "2022-07-25 20:33:15",
          gmtModified: 1658807785000,
          gmtModifiedTime: "2022-07-26 11:56:25",
          gmtPublish: 1658807785000,
          gmtPublishTime: "2022-07-26 11:56:25",
          id: 976952,
          isAvailable: "y",
          isEmergent: "n",
          loadLevel: 0,
          md5: "4b467d2c3ef78774b6f8d222b67cace7",
          name: "launch_link_passive",
          namespaceId: "59859d97960147e0bfa6fb99e740a2b2",
          offlines: "{}",
          resourceId: "nsdee8f921d6fd4ca48b7ff2c719fccdff.json",
          reviewer: "324600",
          source: 0,
          sourceData: "",
          status: 200,
          type: 1,
          valid: true,
          version: "2120220725203315486",
          versions: "-",
        },
      ],
      userMap: {
        "320484": {
          displayName: "风辞",
          emailAddr: "<EMAIL>",
          emailPrefix: "cifeng.lb",
          empId: "320484",
          hrStatus: "A",
          id: 2197670,
          name: "刘斌",
          nickNameCn: "风辞",
          supervisorEmpId: "076177",
        },
        "324600": {
          displayName: "知慕",
          emailAddr: "<EMAIL>",
          emailPrefix: "xiechuanji.xcj",
          empId: "324600",
          hrStatus: "A",
          id: 2222956,
          name: "谢传纪",
          nickNameCn: "知慕",
          supervisorEmpId: "076177",
        },
        "111402": {
          displayName: "行周",
          emailAddr: "<EMAIL>",
          emailPrefix: "xuyang.xuyang",
          empId: "111402",
          hrStatus: "A",
          id: 798684,
          name: "徐杨",
          nickNameCn: "行周",
          supervisorEmpId: "108288",
        },
        "366948": {
          displayName: "初厦",
          emailAddr: "<EMAIL>",
          emailPrefix: "chuxia.lwy",
          empId: "366948",
          hrStatus: "A",
          id: 2601668,
          name: "刘文苑",
          nickNameCn: "初厦",
          supervisorEmpId: "108288",
        },
        "67343": {
          displayName: "巴格",
          emailAddr: "<EMAIL>",
          emailPrefix: "yutang.pyt",
          empId: "67343",
          hrStatus: "A",
          id: 168489,
          name: "彭玉堂",
          nickNameCn: "巴格",
          supervisorEmpId: "064610",
        },
        "246919": {
          displayName: "柘剑",
          emailAddr: "<EMAIL>",
          emailPrefix: "zhejian.wzj",
          empId: "246919",
          hrStatus: "A",
          id: 1693700,
          name: "王浙剑",
          nickNameCn: "柘剑",
          supervisorEmpId: "207511",
        },
        "180856": {
          displayName: "家愿",
          emailAddr: "<EMAIL>",
          emailPrefix: "jialei.zjl",
          empId: "180856",
          hrStatus: "A",
          id: 1210876,
          name: "祝佳磊",
          nickNameCn: "家愿",
          supervisorEmpId: "024523",
        },
        "146282": {
          displayName: "静谦",
          emailAddr: "<EMAIL>",
          emailPrefix: "jingqian.zjj",
          empId: "146282",
          hrStatus: "A",
          id: 976075,
          name: "诸佳俊",
          nickNameCn: "静谦",
          supervisorEmpId: "076177",
        },
        "149016": {
          displayName: "阮萤",
          emailAddr: "<EMAIL>",
          emailPrefix: "guoqi.guoqi",
          empId: "149016",
          hrStatus: "A",
          id: 1028062,
          name: "郭琪",
          nickNameCn: "阮萤",
          supervisorEmpId: "024523",
        },
      },
      viewConfig: {
        oneStepSkip: false,
        ratioGray: false,
        rollback: false,
        rollbackAny: false,
        showReport: true,
        wholeProcess: true,
      },
      waitList: [],
    },
    success: true,
  },
};

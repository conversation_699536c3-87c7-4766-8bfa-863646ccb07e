/* eslint-disable */
const Mock = require('mockjs');

// 自动生成的 Mock 代码会覆盖本地文件，如需持久化修改 Mock 数据请移步官网

const dist =
  // 接收 req 参数, 用于传递 req.query 或 req.body 等参数
  (req) => ({
    default: {
      statusCode: 200,
      headers: {},
      data: {
        // 生成一个永远为 true 的布尔值
        success: '@boolean(0, 10, false)',
        // 生成长度在 3~6 之间的中文字符
        code: '@cword(3,6)',
        // 生成长度在 3~6 之间的中文字符
        message: '@cword(3,6)',
        'data|1-3': [
          {
            // 生成大小在 0~10000 之间的整数
            id: '@integer(0, 10000000)',
            // 生成长度在 3~6 之间的中文字符
            parameterId: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            appKey: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            namespaceId: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            parameterKey: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            valueType: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            description: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            status: '@cword(3,6)',
            gmtCreate: 'string("lower", 0, 32)',
            gmtModified: 'string("lower", 0, 32)',
            // 生成长度在 3~6 之间的中文字符
            creator: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            modifier: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            conditionsOrder: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            releaseVersion: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            previousReleaseVersion: '@cword(3,6)',
            'parameterConditions|1-3': [
              {
                // 生成长度在 3~6 之间的中文字符
                parameterId: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                parameterKey: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                conditionId: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                conditionName: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                conditionColor: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                releaseVersion: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                previousReleaseVersion: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                value: '@cword(3,6)',
                gmtCreate: 'string("lower", 0, 32)',
                gmtModified: 'string("lower", 0, 32)',
                // 生成长度在 3~6 之间的中文字符
                creator: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                modifier: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                status: '@cword(3,6)',
                // 生成长度在 3~6 之间的中文字符
                changeType: '@cword(3,6)',
              },
            ],
            inPublishReleaseOrder: {
              // 生成大小在 0~10000 之间的整数
              id: '@integer(0, 10000000)',
              // 生成长度在 3~6 之间的中文字符
              releaseVersion: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              appKey: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              namespaceId: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              bizType: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              bizId: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              releaseType: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              description: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              status: '@cword(3,6)',
              // 生成大小在 0~10000 之间的整数
              grayRatio: '@integer(0, 10000)',
              // 生成大小在 0~10000 之间的整数
              tigaTaskId: '@integer(0, 10000000)',
              // 生成长度在 3~6 之间的中文字符
              currentStageType: '@cword(3,6)',
              gmtCreate: 'string("lower", 0, 32)',
              gmtModified: 'string("lower", 0, 32)',
              // 生成长度在 3~6 之间的中文字符
              creator: '@cword(3,6)',
              // 生成长度在 3~6 之间的中文字符
              modifier: '@cword(3,6)',
            },
            // 生成长度在 3~6 之间的中文字符
            changeType: '@cword(3,6)',
          },
        ],
        // 生成大小在 0~10000 之间的整数
        total: '@integer(0, 10000000)',
        // 生成大小在 0~10000 之间的整数
        size: '@integer(0, 10000000)',
        // 生成大小在 0~10000 之间的整数
        current: '@integer(0, 10000000)',
      },
    },
  });

module.exports = {
  'GET /api/parameters': (req, res) => {
    const response = Mock.mock(dist(req)['default']).data;
    setTimeout(() => {
      res.status(200).send(response);
    }, 1);
  },
};

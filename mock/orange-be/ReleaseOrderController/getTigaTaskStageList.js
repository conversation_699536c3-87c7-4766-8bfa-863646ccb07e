/* eslint-disable */
const Mock = require('mockjs');

// 自动生成的 Mock 代码会覆盖本地文件，如需持久化修改 Mock 数据请移步官网

const dist =
  // 接收 req 参数, 用于传递 req.query 或 req.body 等参数
  (req) => ({
    default: {
      statusCode: 200,
      headers: {},
      data: {
        // 生成一个永远为 true 的布尔值
        success: '@boolean(0, 10, false)',
        // 生成长度在 3~6 之间的中文字符
        code: '@cword(3,6)',
        // 生成长度在 3~6 之间的中文字符
        message: '@cword(3,6)',
        data: {},
      },
    },
  });

module.exports = {
  'PUT /api/release-orders/:releaseVersion/tiga-task-stage-list': (req, res) => {
    const response = Mock.mock(dist(req)['default']).data;
    setTimeout(() => {
      res.status(200).send(response);
    }, 1);
  },
};

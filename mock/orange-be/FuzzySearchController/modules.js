/* eslint-disable */
const Mock = require('mockjs');

// 自动生成的 Mock 代码会覆盖本地文件，如需持久化修改 Mock 数据请移步官网

const dist =
  // 接收 req 参数, 用于传递 req.query 或 req.body 等参数
  (req) => ({
    default: {
      statusCode: 200,
      headers: {},
      data: {
        // 生成一个永远为 true 的布尔值
        success: '@boolean(0, 10, false)',
        // 生成长度在 3~6 之间的中文字符
        code: '@cword(3,6)',
        // 生成长度在 3~6 之间的中文字符
        message: '@cword(3,6)',
        'data|1-3': [
          {
            // 生成长度在 3~6 之间的中文字符
            name: '@cword(3,6)',
            // 生成大小在 0~10000 之间的整数
            moduleId: '@integer(0, 10000000)',
            // 生成长度在 3~6 之间的中文字符
            identifier: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            description: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            moduleType: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            platformType: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            owner: '@cword(3,6)',
            // 生成一个永远为 true 的布尔值
            hasStandard: '@boolean(0, 10, false)',
            // 生成一个永远为 true 的布尔值
            hasIntegrateToTB: '@boolean(0, 10, false)',
            // 生成一个永远为 true 的布尔值
            hasDynamic: '@boolean(0, 10, false)',
            // 生成长度在 3~6 之间的中文字符
            ds: '@cword(3,6)',
            'tags|1-3': ['@cword(3,6)'],
            // 生成长度在 3~6 之间的中文字符
            codeLibraryAddress: '@cword(3,6)',
            // 生成一个永远为 true 的布尔值
            hasLatestUtRecordPass: '@boolean(0, 10, false)',
            // 生成长度在 3~6 之间的中文字符
            latestStandardVersion: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            latestVersion: '@cword(3,6)',
            // 生成一个永远为 true 的布尔值
            hasUtRecord: '@boolean(0, 10, false)',
            // 生成长度在 3~6 之间的中文字符
            latestVersionCommitNumber: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            integrationBranchName: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            activeLevel: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            bundleName: '@cword(3,6)',
            // 生成长度在 3~6 之间的中文字符
            depKey: '@cword(3,6)',
            'admins|1-3': ['@cword(3,6)'],
            'developers|1-3': ['@cword(3,6)'],
            'testers|1-3': ['@cword(3,6)'],
            // 生成长度在 3~6 之间的中文字符
            mtlAddress: '@cword(3,6)',
          },
        ],
        // 生成大小在 0~10000 之间的整数
        total: '@integer(0, 10000000)',
        // 生成大小在 0~10000 之间的整数
        size: '@integer(0, 10000000)',
        // 生成大小在 0~10000 之间的整数
        current: '@integer(0, 10000000)',
      },
    },
  });

module.exports = {
  'GET /api/fuzzy-search/modules': (req, res) => {
    const response = Mock.mock(dist(req)['default']).data;
    setTimeout(() => {
      res.status(200).send(response);
    }, 1);
  },
};

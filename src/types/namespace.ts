import { UserMap } from '@/types/user';
import { ChangeBO } from '@/types/version';

export interface AppBO {
  appDetail: string;
  appId: number;
  appKey: string;
  appMark: string;
  appName: string;
  basicAppKey: string;
  gmtCreate: number;
  isAvailable: string;
  motuAppId: string;
  mtlId: string;
  osType: number;
  valid: boolean;
}

export interface ListItem {
  appKey: string;
  appVersion: string;
  creator: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  gmtPublish: number;
  gmtPublishTime: string;
  id: number;
  isAvailable: string;
  isEmergent: string;
  loadLevel: number;
  md5: string;
  strategy: string | null;
  name: string;
  namespaceId: string;
  offlines: string; // Assuming it's a JSON string
  previousResourceId: string;
  resourceId: string;
  reviewer: string;
  source: number;
  sourceData: string;
  status: number;
  type: number;
  valid: boolean;
  version: string;
  versions: string;
}

export interface NamespaceBO {
  appKeyOrGroup: string;
  auditingFlag: string;
  creator: string;
  detail: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  id: number;
  isAvailable: string;
  loadLevel: number;
  modules: string;
  name: string;
  namespaceId: string;
  owners: string;
  testers: string;
  type: number;
  valid: boolean;
}

export interface ViewConfig {
  oneStepSkip: boolean;
  ratioGray: boolean;
  smallGray: boolean;
  rollback: boolean;
  rollbackAny: boolean;
  showReport: boolean;
  wholeProcess: boolean;
}

export interface NamespaceDetail {
  appBO: AppBO;
  changeBO: ChangeBO;
  list: ListItem[];
  namespaceBO: NamespaceBO;
  noStrategyList: ListItem[];
  userMap: UserMap;
  viewConfig: ViewConfig;
  waitList: any[]; // Assuming waitList can contain any structure
}

export interface MtlModule {
  moduleId: string;
  name: string;
  mtlAddress?: string;
}

export type SwitchNamespace = API.NamespaceDTO;

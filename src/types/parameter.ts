// 参数条件值接口

import ParameterConditionDTO = API.ParameterConditionDTO;

export type ParameterValueType = 'BOOLEAN' | 'STRING' | 'JSON' | 'DOUBLE' | 'LONG';
export type ParameterConditionChangeType = 'CREATE' | 'UPDATE' | 'DELETE';

// 参数项接口
export interface Parameter extends API.ParameterDetailDTO {
  // 有值代表被修改过
  latestModifyTime?: string;
  latestPublishUser?: string;
  latestPublishTime?: string;
  parameterConditions?: ParameterCondition[];
  changeType?: ParameterConditionChangeType;
  valueType?: ParameterValueType;
}

export interface ParameterCondition extends ParameterConditionDTO {
  changeType?: ParameterConditionChangeType;
  latestModifyTime?: string;
  latestPublishUser?: string;
}

// 分页接口
export interface PaginationState {
  current: number;
  size: number;
  total: number;
}

export interface ParameterEditDrawerProps {
  visible: boolean;
  onClose: () => void;
  editingParameter?: Parameter | null;
  onSave: (parameter: Parameter) => void;
  availableConditions: API.ConditionDetailDTO[];
  mode: 'create' | 'edit';
  handleAddNewCondition?: (condition: API.ConditionDTO) => void;
  namespaceId?: string;
}

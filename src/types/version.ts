import { AppBO, NamespaceBO, ViewConfig } from '@/types/namespace';
import { UserMap } from '@/types/user';

export interface NamespaceVersion {
  /**
   * 命名空间的唯一id
   */
  namespaceId: string;

  /**
   * 命名空间的名字
   */
  name: string;

  /**
   * 当前命名空间的类型。
   *
   * @see NamespaceType - 定义不同类型命名空间的枚举。
   */
  type: number; // 相当于Java中的Integer

  /**
   * 该命名空间的加载级别
   *
   * @see LoadLevel - 定义不同加载级别的枚举。
   */
  loadLevel: number; // 相当于Java中的Integer

  /**
   * 当前命名空间的版本。
   */
  version: string;

  /**
   * 当前命名空间组装完毕后，对应生成的OSS资源文件
   */
  resourceId: string;

  /**
   * 资源文件 md5
   */
  md5: string;

  /**
   * 属于哪个appKey。
   */
  appKey: string;

  /**
   * 属于app的哪个版本
   */
  appVersion: string;

  status: number;

  source: number;

  /**
   * 创建者
   */
  creator: string;

  /**
   * 审核者
   */
  reviewer: string;

  /**
   * 来源数据
   */
  sourceData: string;

  /**
   * 前一个版本的资源ID
   */
  previousResourceId: string;

  /**
   * 发布时间
   */
  gmtPublish: Date;

  /**
   * 谁删除的
   */
  deleter: string;

  /**
   * 策略表达式
   */
  strategy: string;

  /**
   * 要覆盖的旧的策略版本，多个version以','分割
   */
  overwriteStrategyVersions: string;

  /**
   * 是否是立即生效
   */
  isEmergent: string;

  /**
   * 有效的版本顺序
   */
  versions: string;

  /**
   * 待失效的版本
   */
  offlines: string;

  /**
   * 灰度版本列表
   */
  grayVersions: string;
  gmtCreate: number;
  gmtModified: number;
  gmtCreateTime: string;
  gmtModifiedTime: string;
  gmtPublishTime: string;
  id: number;
  sourceDataMeta: any;
  tigaMetadata: string;
}

export interface KnockoutVersion {
  reason: string;
  reasonId: number;
  targetList: NamespaceVersion[];
}

export interface ChangeBO {
  appKey: string;
  changeVersion: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  id: number;
  isAvailable: string;
  loadLevel: number;
  metas: string; // Assuming it's a JSON string
  name: string;
  namespaceId: string;
  operator: string;
  status: number;
  type: number;
  valid: boolean;
  versionVersion: string;
  versions: string;
}

export interface WmccView {
  message: string;
  id: number;
}

interface GrayConfig {
  expression: string;
  supportConfigs: {
    [key: string]: {
      maxGrayCnt: number;
      support: boolean;
      supportDimensions: string[];
    };
  };
}

interface IndexBO {
  appIndexVersion: string;
  appKey: string;
  appVersion: string;
  createTime: number;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  id: number;
  indexId: string;
  isAvailable: string;
  md5: string;
  publishType: string;
  resourceId: string;
  totalCnt: number;
  type: number;
  valid: boolean;
  version: string;
  versionIndexVersion: string;
}

interface NoStrategyListItem {
  appKey: string;
  appVersion: string;
  creator: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  gmtPublish: number;
  gmtPublishTime: string;
  id: number;
  isAvailable: string;
  isEmergent: string;
  loadLevel: number;
  md5: string;
  name: string;
  namespaceId: string;
  offlines: string;
  resourceId: string;
  reviewer: string;
  source: number;
  sourceData: string;
  status: number;
  type: number;
  valid: boolean;
  version: string;
  versions: string;
}

interface RatioGrayConfig {
  support: boolean;
  unsupportedMsg: string;
}

export interface RecordItem {
  appKey: string; // 应用唯一标识
  checkResult: string; // 检查结果字符串
  checkResultDO: Record<string, unknown>; // 检查结果对象
  creator: string; // 创建人标识
  gmtCreate: number; // 创建时间（时间戳）
  gmtCreateTime: string; // 创建时间（格式化字符串）
  gmtModified: number; // 修改时间（时间戳）
  gmtModifiedTime: string; // 修改时间（格式化字符串）
  id: number; // 唯一标识ID
  isAvailable: string; // 是否可用 (e.g., "y" 或 "n")
  namespaceId: string; // 命名空间ID
  paramDO: Record<string, unknown>;
  params: string; // 参数字符串
  result: string; // 结果字符串
  resultDO: Record<string, unknown>; // 结果对象
  status: number; // 状态码 (e.g., 200 表示成功)
  type: number; // 类型 (e.g., 5)
  valid: boolean; // 是否有效
  ver: number; // 版本号
  version: string; // 版本标识字符串
}

export interface ResourceBO {
  appKey: string;
  arg1: string;
  arg2: string;
  contentMd5: string;
  data: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModified: number;
  gmtModifiedTime: string;
  id: number;
  isAvailable: string;
  md5: string;
  resourceId: string;
  srcContent: string;
  type: number;
  valid: boolean;
  scenesContentsMap: { [key: string]: string };
}

export interface VersionDetail {
  appBO: AppBO;
  changeBO: ChangeBO;
  grayConfig: GrayConfig;
  indexBO: IndexBO;
  latestGrayRecord: VersionGrayRecord;
  lastVersionBO: NamespaceVersion;
  namespaceBO: NamespaceBO;
  noStrategyList: NamespaceVersion[];
  offlineList: NamespaceVersion[];
  onlineList: NamespaceVersion[];
  ratioGrayConfig: RatioGrayConfig;
  recordList: RecordItem[];
  resourceBO: ResourceBO;
  userMap: UserMap;
  versionBO: NamespaceVersion;
  viewConfig: ViewConfig;
  wmccView: WmccView;
}

export type DeploymentStatus =
  | 'BETA'
  | 'PENDING'
  | 'GRAYSCALE'
  | 'VERIFIED'
  | 'PUBLISHED'
  | 'CANCELLED';

export interface VersionGrayRecord {
  namespaceId: string;
  appKey: string;
  namespaceName: string;
  version: string;
  changeVersion: string;
  grayRatio: number;
  grayMetas: string;
  status: string;
  gmtModified: number;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtModifiedTime: string;
}

export interface SceneContent {
  name: string;
  content: string;
}

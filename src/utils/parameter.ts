import React from 'react';
import { parameterChangeType2Color } from '@/constants/parameter';

import JSONIcon from '@/assets/icons/json.svg';
import FloatIcon from '@/assets/icons/float.svg';
import SwitchIcon from '@/assets/icons/switch.svg';
import NumberIcon from '@/assets/icons/number.svg';
import StringIcon from '@/assets/icons/string.svg';

export function getBgColor(changeType?: 'CREATE' | 'UPDATE' | 'DELETE') {
  return changeType ? parameterChangeType2Color[changeType] : 'bg-white';
}

/**
 * 获取参数类型对应的SVG图标组件
 * @param valueType 参数类型值
 * @returns SVG图标组件或null
 */
export const getParameterTypeIcon = (valueType: string) => {
    const SvgIconMap = {
        BOOLEAN: SwitchIcon,
        STRING: StringIcon,
        JSON: JSONIcon,
        DOUBLE: FloatIcon,
        LONG: NumberIcon,
    } as const;

    return SvgIconMap[valueType as keyof typeof SvgIconMap] || null;
};

/**
 * 渲染参数类型图标
 * @param valueType 参数类型值
 * @param props 传递给SVG组件的属性
 * @returns 渲染的图标组件或null
 */
export const renderParameterTypeIcon = (valueType: string, props: React.SVGProps<SVGSVGElement> = {}) => {
    const IconComponent = getParameterTypeIcon(valueType);
    if (!IconComponent) return null;

    return React.createElement(IconComponent, props);
};

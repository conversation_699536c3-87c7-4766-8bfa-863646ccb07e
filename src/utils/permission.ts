import { trimEmpId } from '@/utils/common';

declare const window: {
  _DATA_: {
    userId?: string;
    userName?: string;
    isAdmin?: boolean | string;
    emailPrefix?: string;
  };
};

export function isSystemAdmin() {
  const { isAdmin } = window._DATA_;
  // 老的控制台是字符串
  return (isAdmin && isAdmin === 'true') || isAdmin === true;
}

export function hasNsEditPermission(namespace: { owners: string }) {
  return hasNSPermission(namespace && namespace.owners);
}

export function hasNsTestPermission(namespace: { testers: string }) {
  return hasNSPermission(namespace && namespace.testers);
}

function hasNSPermission(users) {
  const {
    // 老的控制台有 emailPrefix
    emailPrefix = '',
    userName,
    userId,
  } = window._DATA_;
  if (isSystemAdmin()) {
    return true;
  }
  if (!users) {
    return false;
  }
  // 兼容工号 0 的问题
  if (Array.isArray(users) && userId) {
    return users.map(user => trimEmpId(user)).includes(trimEmpId(userId));
  }
  return (
    (userId && users.includes(trimEmpId(userId))) ||
    (userName && users.includes(userName)) ||
    (emailPrefix && users.includes(emailPrefix))
  );
}

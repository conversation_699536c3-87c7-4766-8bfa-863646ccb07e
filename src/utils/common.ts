import moment from 'moment';
import { isTestEnv } from './env';

const TAOBAO_APPKEY_MAP = {
  // 线上环境
  production: {
    21380790: '淘宝iPhone客户端-ios',
    21646297: '淘宝主客户端Android-android',
    34639004: '手淘HarmonyOS客户端-harmony',
  },
  // 日常环境
  daily: {
    531772: '淘宝iPhone客户端-ios',
    4272: '淘宝主客户端Android-android',
    60046460: '手淘HarmonyOS客户端-harmony',
  },
};

/**
 * 获取当前环境的淘宝应用 appKey 映射
 */
export function getCurrentTBAppKeyMap() {
  return isTestEnv() ? TAOBAO_APPKEY_MAP.daily : TAOBAO_APPKEY_MAP.production;
}

/**
 * 获取当前环境的所有淘宝应用 appKey 列表
 */
export function getCurrentTBAppKeys() {
  return Object.keys(getCurrentTBAppKeyMap());
}

/**
 * 获取当前环境的第一个淘宝应用 appKey
 */
export function getFirstTBAppKey() {
  const appKeys = getCurrentTBAppKeys();
  return appKeys.length > 0 ? appKeys[0] : '';
}

export function isTBApp(appKey: string) {
  const currentMap = getCurrentTBAppKeyMap();
  return Object.keys(currentMap).includes(appKey);
}

export function getTBAppName(appKey: string) {
  const currentMap = getCurrentTBAppKeyMap();
  return currentMap[appKey];
}

export function getFormatDate(timestamp: string | number) {
  timestamp = Number.isNaN(Number(timestamp)) ? timestamp : Number(timestamp);
  return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
}

export function trimEmpId(empId: string) {
  return empId?.replace(/^0+/, '');
}

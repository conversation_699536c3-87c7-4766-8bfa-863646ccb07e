import { COLOR2KEY, COLORS, DEFAULT_CONDITION_COLOR } from '@/constants/condition';

export const getTagColor = ({
  conditionId,
  color,
  conditionColor,
}: {
  conditionId?: string;
  color?: string | undefined;
  conditionColor?: string | undefined;
}): string => {
  if ('*' === conditionId) {
    return DEFAULT_CONDITION_COLOR;
  }
  const _color = color || conditionColor;
  if (!_color) {
    return DEFAULT_CONDITION_COLOR;
  }
  return COLOR2KEY[_color] || (_color.startsWith('#') ? _color : DEFAULT_CONDITION_COLOR);
};

export const getColorName = (colorKey: string) => {
  return COLORS[colorKey]?.name || colorKey;
};

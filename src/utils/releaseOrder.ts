/**
 * 根据申请发布操作的结果计算状态
 * @param result 申请发布操作的结果对象
 * @returns 计算后的状态
 */
export function calculateApplyReleaseStatus(
  result: any,
): 'SUCCESS' | 'FAILED' | 'CANCELED' | 'IN_PROGRESS' {
  if (!result) {
    return 'IN_PROGRESS';
  }

  const { checkStatusEnum, orderStatusEnum } = result;

  // 检查通过或订单通过
  if (checkStatusEnum === 'CHECK_PASS' || orderStatusEnum === 'ORDER_PASS') {
    return 'SUCCESS';
  }

  // 检查取消或订单取消
  if (checkStatusEnum === 'CHECK_CANCEL' || orderStatusEnum === 'ORDER_CANCEL') {
    return 'CANCELED';
  }

  // 检查失败或订单失败
  if (checkStatusEnum === 'CHECK_FAIL' || orderStatusEnum === 'ORDER_FAIL') {
    return 'FAILED';
  }

  // 默认状态为进行中
  return 'IN_PROGRESS';
}

/**
 * 根据 releaseOrderStages 确定当前活跃的工作流节点
 * @param releaseOrderStages 发布单阶段数组
 * @returns 当前活跃的工作流节点key
 */
export function getCurrentActiveWorkflowStage(
  releaseOrderStages: API.ReleaseOrderStageDTO[] = [],
): string {
  if (!releaseOrderStages?.length) return 'beta';

  // 创建阶段类型到工作流节点的映射
  const stageTypeToWorkflowKey: Record<string, string> = {
    APPLY_RELEASE: 'beta',
    SMALLFLOW_GRAY: 'smallflow-gray',
    VERIFY: 'smallflow-gray',
    RATIO_GRAY: 'gray',
    RELEASE: 'gray',
  };

  // 找到第一个进行中的阶段
  const inProgressStage = releaseOrderStages.find(stage => stage.status === 'IN_PROGRESS');
  if (inProgressStage) {
    return stageTypeToWorkflowKey[inProgressStage.type!] || 'beta';
  }

  // 使用 releaseOrderStages 数组的顺序作为阶段顺序
  let lastSuccessIndex = -1;

  for (let i = 0; i < releaseOrderStages.length; i++) {
    const stage = releaseOrderStages[i];
    if (stage && (stage.status === 'SUCCESS' || stage.status === 'SKIPPED')) {
      lastSuccessIndex = i;
    }
  }

  // 如果所有阶段都成功了，则活跃节点为最后一个
  if (lastSuccessIndex === releaseOrderStages.length - 1) {
    const lastStage = releaseOrderStages[lastSuccessIndex];
    return stageTypeToWorkflowKey[lastStage.type!] || 'release';
  }

  // 下一个阶段为活跃节点
  const nextStageIndex = lastSuccessIndex + 1;
  if (nextStageIndex < releaseOrderStages.length) {
    const nextStage = releaseOrderStages[nextStageIndex];
    return stageTypeToWorkflowKey[nextStage.type!] || 'beta';
  }

  return 'beta';
}

/**
 * 检查指定阶段之前的所有阶段是否都已完成
 * @param releaseOrderStages 发布单阶段数组
 * @param targetStageType 目标阶段类型
 * @returns 是否前置阶段都已完成
 */
export function arePreStagesCompleted(
  releaseOrderStages: API.ReleaseOrderStageDTO[] = [],
  targetStageType: string,
): boolean {
  // 找到目标阶段在数组中的位置
  const targetIndex = releaseOrderStages.findIndex(stage => stage.type === targetStageType);

  if (targetIndex === -1) return false;

  // 检查目标阶段之前的所有阶段
  for (let i = 0; i < targetIndex; i++) {
    const stage = releaseOrderStages[i];
    if (!stage || (stage.status !== 'SUCCESS' && stage.status !== 'SKIPPED')) {
      return false;
    }
  }

  return true;
}

/**
 * 获取指定类型的阶段
 * @param releaseOrderStages 发布单阶段数组
 * @param stageType 阶段类型
 * @returns 阶段对象或undefined
 */
export function getStageByType(
  releaseOrderStages: API.ReleaseOrderStageDTO[] = [],
  stageType: string,
): API.ReleaseOrderStageDTO | undefined {
  return releaseOrderStages.find(stage => stage.type === stageType);
}

/**
 * 获取发布单剩余未完成的阶段列表
 * @param releaseOrderStages 发布单阶段数组
 * @returns 剩余未完成的阶段列表（包含 ALL 选项）
 */
export function getRemainingIncompleteStages(
  releaseOrderStages: API.ReleaseOrderStageDTO[] = [],
): Array<{
  value: string;
  label: string;
}> {
  // 阶段类型到中文标签的映射
  const stageTypeToLabel: Record<string, string> = {
    APPLY_RELEASE: '申请发布',
    SMALLFLOW_GRAY: '小流量灰度',
    VERIFY: '人工验证',
    RATIO_GRAY: '百分比发布',
  };

  const incompleteStages = releaseOrderStages
    .filter(
      stage => stage.status !== 'SUCCESS' && stage.status !== 'SKIPPED' && stage.type !== 'RELEASE',
    )
    .map(stage => ({
      value: stage.type!,
      label: stageTypeToLabel[stage.type!] || stage.type!,
    }));

  // 添加 ALL 选项
  return [
    ...incompleteStages,
    {
      value: 'ALL',
      label: '所有',
    },
  ];
}

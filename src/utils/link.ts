import { getEnv, getTigaReleaseHost, isOnlineEnv, isPreEnv, isTestEnv } from '@/utils/env';

export function getBpmsUrl(bpmsId) {
  if (isTestEnv()) {
    return `https://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=${bpmsId}`;
  }
  return `https://bpms.alibaba-inc.com/workdesk/instDetail?procInsId=${bpmsId}`;
}

export function getChangeFreeUrl(orderId: string) {
  if (!orderId) {
    return null;
  }
  if (isTestEnv()) {
    return `http://changesafe.alibaba.net/pages/order_detail/index.html?orderId=${orderId}`;
  } else {
    return `https://aicf.alibaba-inc.com/aicf/approval/detail/${orderId}`;
  }
}

export function getWmccUrl(wmccId) {
  if (isPreEnv()) {
    return `https://pre-wmcc.alibaba-inc.com/diamond/beta-publish#/dashboard/monitor/${wmccId}`;
  }
  return `https://wmcc.alibaba-inc.com/diamond/beta-publish#/dashboard/monitor/${wmccId}`;
}

export function getDebugQrUrl(
  name = '',
  appKey = '',
  appVersion = '',
  version = '',
  appIndexVersion = '',
  indexInfo?: any,
) {
  const env = getEnv();
  if (appIndexVersion.startsWith('16')) {
    appIndexVersion = `11${appIndexVersion.slice(2)}`;
  }

  const domain = isOnlineEnv() ? 'pages.tmall.com' : 'pre-wormhole.tmall.com';
  const uri = `https://${domain}/wow/z/app/orange/debug/home`;

  return `${uri}?env=${env}&name=${name}&appKey=${appKey}&appVersion=${appVersion}&version=${version}&appIndexVersion=${appIndexVersion}${
    indexInfo ? `&accsIndexInfo=${encodeURIComponent(JSON.stringify(indexInfo))}` : ''
  }`;
}

/**
 * 获取任务详情链接（仅包含观测，不包含操作）
 * @param taskId
 * @param size
 */
export function getTigaObservationUrl(taskId: number, size?: string) {
  return `https://${getTigaReleaseHost()}/task/observation?taskId=${taskId}&fullscreen=Y&size=${
    size || 'small'
  }`;
}

/**
 * 获取任务详情链接（包含操作和观测）
 * @param taskId
 * @param fullscreen
 * @param minimum
 * @param size
 */
export function getTigaTaskDetailUrl(
  taskId: number,
  fullscreen?: boolean,
  minimum?: boolean,
  size?: 'small',
) {
  return `https://${getTigaReleaseHost()}/task/detail?taskId=${taskId}&fullscreen=${
    fullscreen ? 'Y' : 'N'
  }&minimum=${minimum ? 'Y' : 'N'}&size=${size || ''}`;
}

/**
 * 获取大盘指标观测链接
 * @param params
 */
export function getTigaMetricObservationUrl(params: {
  taskId?: string | number;
  templateId?: string | number;
  appKeys?: string;
  startTime?: string | number;
}) {
  const search = new URLSearchParams();
  search.set('fullscreen', 'Y');
  search.set('size', 'small');
  if (params.taskId) search.set('taskId', String(params.taskId));
  if (params.templateId) search.set('templateId', String(params.templateId));
  if (params.appKeys) search.set('appKeys', params.appKeys);
  if (params.startTime) search.set('startTime', String(params.startTime));
  return `https://${getTigaReleaseHost()}/metric/observation?${search.toString()}`;
}

export function getReleaseOrderDetailUrl(releaseVersion: string) {
  return `/workspace/switch/release-orders/${releaseVersion}`;
}

export function getSwitchNamespaceDetailUrl(namespaceId: string) {
  return `/workspace/switch/namespaces/${namespaceId}`;
}

export function getSwitchDebugQrUrl(
  debugInfo: API.DebugInfoDTO & { appKey: string; version?: string },
) {
  const env = getEnv();
  const domain = isOnlineEnv() ? 'pages.tmall.com' : 'pre-wormhole.tmall.com';
  const uri = `https://${domain}/wow/z/app/orange/debug/pro?env=${env}&name=${
    debugInfo.namespaceName
  }&appKey=${debugInfo.appKey}&indexVersion=${debugInfo.indexVersion || ''}&changeVersion=${
    debugInfo.changeVersion || ''
  }&version=${debugInfo.version || ''}`;

  console.log(uri);
  return uri;
}

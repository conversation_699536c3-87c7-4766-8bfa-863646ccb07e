import { MenuDataItem } from '@ant-design/pro-layout';
import { asideMenuConfig } from '@/menuConfig';
import services from '@/services/orange-be';
import { getTBAppName } from '@/utils/common';

export interface BreadcrumbRule {
    pattern: RegExp;
    getBreadcrumbItems: (pathParts: string[], params: any) => Promise<BreadcrumbItem[]>;
}

export interface BreadcrumbItem {
    key: string;
    label: string;
    path?: string; // 可选的路径，如果提供则该项可点击
}

const findPath = (menus: MenuDataItem[], targetKey: string, path: MenuDataItem[] = []): MenuDataItem[] | null => {
    // First try to find the exact path in menus
    for (const item of menus) {
        if (item.key === targetKey) {
            return [...path, item];
        }
        if (item.children) {
            const newPath = findPath(item.children, targetKey, [...path, item]);
            if (newPath) {
                return newPath;
            }
        }
    }

    // If not found, try to match the parent path
    const pathParts = targetKey.split('/').filter(Boolean);
    if (pathParts.length > 0) {
        // Try to find the parent menu item
        const parentPath = `/${pathParts.slice(0, -1).join('/')}`;
        for (const item of menus) {
            if (item.key === parentPath) {
                return [...path, item];
            }
            if (item.children) {
                const newPath = findPath(item.children, parentPath, [...path, item]);
                if (newPath) {
                    return newPath;
                }
            }
        }
    }

    return null;
};

export const breadcrumbRules: BreadcrumbRule[] = [
    {
        pattern: /^workspace\/switch\/namespaces$/,
        getBreadcrumbItems: async (pathParts, params) => {
            return [
                {
                    key: '/workspace/dashboard',
                    label: '工作台',
                    path: '/workspace/dashboard',
                },
                {
                    key: '/workspace/swith/namespace',
                    label: '命名空间',
                    path: '/workspace/switch/namespaces',
                },
            ];
        },
    },
    {
        pattern: /^workspace\/switch\/namespaces\/([^/]+)$/,
        getBreadcrumbItems: async (pathParts, params) => {
            const result = await services.NamespaceController.getByNamespaceId({
                namespaceId: pathParts[3],
            });
            const namespace = result.data;
            return [
                {
                    key: '/workspace/dashboard',
                    label: '工作台',
                    path: '/workspace/dashboard',
                },
                {
                    key: '/workspace/switch/namespaces',
                    label: getTBAppName(namespace.appKey),
                    path: `/workspace/switch/namespaces?appKey=${namespace.appKey}`,
                },
                {
                    key: `/${pathParts.join('/')}`,
                    label: namespace.name,
                },
            ];
        },
    },
    {
        pattern: /^workspace\/switch\/release-orders\/([^/]+)$/,
        getBreadcrumbItems: async (pathParts, params) => {
            const releaseVersion = pathParts[3];
            const releaseOrder = await services.ReleaseOrderController.getDetail({
                releaseVersion,
            });
            const namespace = await services.NamespaceController.getByNamespaceId({
                namespaceId: releaseOrder.data.namespaceId,
            });
            return [
                {
                    key: '/workspace/dashboard',
                    label: '工作台',
                    path: '/workspace/dashboard',
                },
                {
                    key: '/workspace/switch/namespaces',
                    label: getTBAppName(namespace.data?.appKey),
                    path: `/workspace/switch/namespaces?appKey=${namespace.data?.appKey}`,
                },
                {
                    key: releaseOrder.data?.namespaceId,
                    label: namespace.data?.name,
                    path: `/workspace/switch/namespaces/${releaseOrder.data?.namespaceId}`,
                },
                {
                    key: 'release-order',
                    label: '变更管理',
                    path: `/workspace/switch/namespaces/${releaseOrder.data?.namespaceId}?tab=release-order`,
                },
                {
                    key: releaseVersion,
                    label: releaseVersion,
                },
            ];
        },
    },
];

export const getBreadcrumbItems = async (currentPath: string): Promise<BreadcrumbItem[]> => {
    const pathParts = currentPath.split('/').filter(Boolean);

    // 首先尝试匹配自定义规则
    for (const rule of breadcrumbRules) {
        if (rule.pattern.test(currentPath)) {
            return await rule.getBreadcrumbItems(pathParts, {});
        }
    }

    // 如果没有匹配的规则，使用默认的菜单路径查找
    const menuItems = findPath(asideMenuConfig, `/${currentPath}`) || [];
    return menuItems.map(item => ({
        key: item.key || '',
        label: item.label || '',
    }));
};
'use strict';

export const ENV_ONLINE = 'ONLINE';
export const ENV_PRE = 'PRE';
export const ENV_DAILY = 'DAILY';

export interface EnvConfig {
    key: string;
    host: string;
    label?: string;
}

export const ENV_GROUP_HOSTS: EnvConfig[] = [
    {
        key: ENV_DAILY,
        host: 'orange-console-daily.alibaba.net',
        label: '日常',
    },
    {
        key: ENV_PRE,
        host: 'orange-console-pre.alibaba-inc.com',
        label: '预发',
    },
    {
        key: ENV_ONLINE,
        host: 'orange-console.alibaba-inc.com',
        label: '生产',
    },
];

export const PRO_ENV_GROUP_HOSTS: EnvConfig[] = [
    {
        key: ENV_DAILY,
        host: 'orange-pro.taobao.net',
        label: '日常',
    },
    {
        key: ENV_PRE,
        host: 'pre-orange-pro.alibaba-inc.com',
        label: '预发',
    },
    {
        key: ENV_ONLINE,
        host: 'orange-pro.alibaba-inc.com',
        label: '生产',
    },
];

export const getConfig = () => {
    const { hostname } = window.location;
    const config = ENV_GROUP_HOSTS.find(item => item.host === hostname);
    const proConfig = PRO_ENV_GROUP_HOSTS.find(item => item.host === hostname);
    return config || proConfig || ENV_GROUP_HOSTS[0];
};

export function getEnv() {
    const config = getConfig();
    return config.key;
}

export function isPreEnv() {
    const env = getEnv();
    return (env === ENV_PRE);
}

export function isOnlineEnv() {
    const env = getEnv();
    return (env === ENV_ONLINE);
}

export function isTestEnv() {
    const env = getEnv();
    return (env === ENV_DAILY);
}

export function isOrangePro() {
    const { hostname } = window.location;
    return hostname.includes('orange-pro');
}

export function getEnvName() {
    const config = getConfig();
    return config.label;
}

// Tiga发布平台域名配置
export const TIGA_HOSTS: EnvConfig[] = [
    {
        key: ENV_DAILY,
        host: 'tiga-release-daily.alibaba.net',
    },
    {
        key: ENV_PRE,
        host: 'tiga-release-pre.alibaba-inc.com',
    },
    {
        key: ENV_ONLINE,
        host: 'tiga-release.alibaba-inc.com',
    },
];

export function getTigaReleaseHost() {
    const env = getEnv();
    const config = TIGA_HOSTS.find(item => item.key === env);
    return config?.host || TIGA_HOSTS[0].host;
}

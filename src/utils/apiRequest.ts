import { RequestConfig } from '@ice/plugin-request/types';
import * as ice from 'ice';
import { BusinessError } from '@/types/error';

const request = async function <D>(url: string, config?: RequestConfig<D>) {
    const res: any = await ice.request({
        ...config,
        paramsSerializer: (params) => {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (Array.isArray(value)) {
                    value.forEach(v => queryParams.append(key, v));
                } else if (value !== undefined) {
                    queryParams.append(key, String(value));
                }
            });
            return queryParams.toString();
        },
        url,
        data: config.data || config.params,
    });
    if (!res.success && !config.ignoreError) throw new BusinessError(res.errorCode || res.code, res.errorMsg || res.message);
    return res;
};

export default request;

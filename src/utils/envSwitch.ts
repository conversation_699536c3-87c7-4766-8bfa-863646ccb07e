import { ENV_GROUP_HOSTS, isOrangePro, PRO_ENV_GROUP_HOSTS } from './env';

export const handleEnvChange = (targetEnv: string) => {
    const isPro = isOrangePro();
    const targetConfig = (isPro ? PRO_ENV_GROUP_HOSTS : ENV_GROUP_HOSTS).find(item => item.key === targetEnv);

    if (!targetConfig) {
        return;
    }

    const path = window.location.pathname;
    const hash = window.location.hash || '';
    const hashWithoutQuery = hash.split('?')[0];

    const segments = hashWithoutQuery.split('/');
    const lastSegment = segments[segments.length - 1];

    // 以数字和32位UUID结尾的也是详情页
    const isDetailPage = hashWithoutQuery.includes('/version/detail') || hashWithoutQuery.includes('/namespace/detail') || (lastSegment && (
        /^\d+$/.test(lastSegment) ||
        /^[0-9a-f]{32}$/i.test(lastSegment)
    ));

    if (isDetailPage) {
        window.location.href = getHomeUrl(targetConfig.host);
    } else {
        window.location.href = `https://${targetConfig.host}${path}${hashWithoutQuery}`;
    }
};

function getHomeUrl(targetHost: string) {
    if (isOrangePro()) {
        return `https://${targetHost}#/workspace/dashboard`;
    }
    return `https://${targetHost}/v5#/workspace/namespace/me`;
}
import { ResourceBO } from '@/types/version';

export function getScenesContentsByResource(resource: ResourceBO) {
  const scenesContents = [
    {
      name: 'Default',
      content: resource.srcContent,
    },
  ];
  if (resource.scenesContentsMap) {
    for (let scene in resource.scenesContentsMap) {
      scenesContents.push({
        name: scene,
        content: resource.scenesContentsMap[scene],
      });
    }
  }
  return scenesContents;
}

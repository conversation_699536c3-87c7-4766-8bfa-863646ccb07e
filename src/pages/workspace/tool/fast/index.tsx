import { NamespaceList } from '@/components';
import { defineDataLoader } from 'ice';
import moment from 'moment';
import { queryAppList } from '@/services/app';
import { useData } from '@ice/runtime/router';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Al<PERSON>,
  Button,
  Card,
  Checkbox,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Select,
} from 'antd';
import { enableBootNamespace, getBootNamespaceList } from '@/services/bootNamespace';
import { useEffect, useState } from 'react';
import { getAppNamespaceList, getNamespaceDetail } from '@/services/namespace';
import { pushToActiveDevices } from '@/services/version';
import { isSubstring, sortAppKeys } from '@/lib/utils';
import { RocketOutlined, SearchOutlined } from '@ant-design/icons';
import { NamespaceBO } from '@/types/namespace';
import { getCurrentTBAppKeys } from '@/utils/common';

const { Option } = Select;

interface VersionInfo {
  appKey: string;
  name: string;
  versionVersion: string;
  namespaceId: string;
  gmtModified: number;
  operator: string;
}

const SUBMIT_MESSAGE_KEY = 'submit';

export default function Index() {
  const [form] = Form.useForm();
  const [bootNamespaceList] = useData();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [latestVersionInfo, setLatestVersionInfo] = useState<VersionInfo | null>(null);
  const [isWhitelisted, setIsWhitelisted] = useState(true);

  // 新增筛选相关状态
  const [appList, setAppList] = useState<any[]>([]);
  const [appKey, setAppKey] = useState<string | undefined>(() => {
    try {
      return localStorage.getItem('text-namespace-recent-appKey') || undefined;
    } catch {
      return undefined;
    }
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [namespaceList, setNamespaceList] = useState<NamespaceBO[]>([]);
  const [loading, setLoading] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const [validBootNamespaceList, setValidBootNamespaceList] = useState<any[]>(
    bootNamespaceList.content
      .filter(ns => ns.status === 1 && ns.expireTime > Date.now())
      .map(ns => {
        return {
          appKey: ns.appKey,
          name: ns.namespaceName,
          expireTime: ns.expireTime,
        };
      }),
  );
  const validBootNamespaceMap = validBootNamespaceList.reduce((map, ns) => {
    map[`${ns.appKey}%%${ns.name}`] = ns;
    return map;
  }, {});

  // 获取应用列表
  useEffect(() => {
    (async () => {
      setAppList(await queryAppList());
    })();
  }, []);

  // 根据appKey获取namespace列表
  useEffect(() => {
    if (!appKey) {
      setNamespaceList([]);
      return;
    }
    setLoading(true);
    getAppNamespaceList(appKey)
      .then(({ namespaceList }) => {
        setNamespaceList(namespaceList);
      })
      .finally(() => setLoading(false));
  }, [appKey]);

  // 筛选后的namespace列表
  const filteredNamespaces = namespaceList.filter(
    ns =>
      ns.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      (ns.detail || '').toLowerCase().includes(searchKeyword.toLowerCase()),
  );

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1); // 改变页面大小时重置到第一页
    }
  };

  const handleAppKeyChange = value => {
    setAppKey(value);
    setSearchKeyword('');

    try {
      if (value) {
        localStorage.setItem('text-namespace-recent-appKey', value);
      } else {
        localStorage.removeItem('text-namespace-recent-appKey');
      }
    } catch {
      // Ignore localStorage errors
    }
  };

  const handleSearch = keyword => {
    setSearchKeyword(keyword);
  };

  const handleQuickEffect = async namespace => {
    setLatestVersionInfo(null);

    try {
      const namespaceDetail = await getNamespaceDetail(namespace.namespaceId);
      if (namespaceDetail.waitList?.length) {
        message.error('当前配置有未发布完的版本，请完成发布后再操作');
        return;
      }

      if (!namespaceDetail.changeBO) {
        message.error('当前配置无线上有效版本');
        return;
      }

      setLatestVersionInfo(namespaceDetail.changeBO);
      form.setFieldsValue({
        expireTime: moment().add(3, 'days'),
        needOnlinePush: true,
        needEnableBootNamespace: true,
      });
      setIsWhitelisted(true);
      setIsModalVisible(true);
    } catch (error) {
      message.error('获取最新版本信息失败');
    }
  };

  const handleConfirmQuickEffect = () => {
    form.validateFields().then(async values => {
      message.loading({
        content: '提交中...',
        key: SUBMIT_MESSAGE_KEY,
      });
      const { expireTime, needEnableBootNamespace, needOnlinePush } = values;
      if (!needOnlinePush && !needEnableBootNamespace) {
        message.error({
          content: '请至少选择一种加速方式',
          key: SUBMIT_MESSAGE_KEY,
        });
        return;
      }

      if (needEnableBootNamespace && latestVersionInfo) {
        try {
          await enableBootNamespace(
            latestVersionInfo.appKey,
            latestVersionInfo.name,
            expireTime.unix() * 1000,
          );
          validBootNamespaceList.push({
            appKey: latestVersionInfo.appKey,
            name: latestVersionInfo.name,
            expireTime: expireTime.unix() * 1000,
          });
          setValidBootNamespaceList(validBootNamespaceList);
        } catch (error) {
          message.error({
            content: error.message,
            key: SUBMIT_MESSAGE_KEY,
          });
          return;
        }
      }

      if (needOnlinePush && latestVersionInfo) {
        try {
          const result = await pushToActiveDevices(
            latestVersionInfo.namespaceId,
            latestVersionInfo.versionVersion,
          );
          console.log('PushToActiveDevices result >>>>', result);
        } catch (error) {
          message.error({
            content: `推送配置至在线设备失败：${error.message}`,
            key: SUBMIT_MESSAGE_KEY,
          });
          return;
        }
      }

      message.success({
        content: '操作成功',
        key: SUBMIT_MESSAGE_KEY,
      });
      setIsModalVisible(false);
    });
  };

  const renderExtraActions = record => {
    const ns = validBootNamespaceMap[`${record.appKeyOrGroup}%%${record.name}`];
    const actions = [];
    if (ns) {
      actions.push(
        <Popover
          content={
            <div>
              <p>
                <strong>加速白名单到期时间：</strong>
                {moment(ns.expireTime).format('YYYY-MM-DD HH:mm:ss')}
              </p>
            </div>
          }
        >
          <div className="flex items-center text-green-500">
            <RocketOutlined className="mr-2" />
            {moment(ns.expireTime).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        </Popover>,
      );
    }

    actions.push(
      <Button
        key="action"
        size={'small'}
        icon={<RocketOutlined />}
        onClick={() => handleQuickEffect(record)}
      >
        加速生效
      </Button>,
    );

    return actions;
  };

  return (
    <>
      <PageContainer
        header={{
          ghost: true,
          title: '加速生效',
          extra: [
            <div className="flex items-center" key="app-select">
              <span className="mr-2" style={{ fontSize: 14 }}>
                选择应用：
              </span>
              <Select
                placeholder="输入 AppKey 或者名称进行查找"
                onChange={handleAppKeyChange}
                style={{ width: 350 }}
                value={appKey || undefined}
                showSearch
                filterOption={(input, option) => isSubstring(input, option!.children)}
              >
                {sortAppKeys(getCurrentTBAppKeys(), appList).map(item => (
                  <Option key={item.appKey} value={item.appKey}>
                    {`${item.appKey}-${item.appName}`}
                  </Option>
                ))}
              </Select>
            </div>,
          ],
        }}
      >
        <div className="flex items-center mb-4">
          <Input.Search
            prefix={<SearchOutlined />}
            className="mr-4 max-w-md"
            disabled={!appKey}
            placeholder="输入名称或描述搜索配置"
            value={searchKeyword}
            onChange={e => handleSearch(e.target.value)}
            enterButton
          />
          <div className={'flex-1'} />
          <Button
            key="permission"
            target="_blank"
            href="https://acl.alibaba-inc.com/apply/cart/detail.htm?permissionIds=1155146188"
          >
            申请权限
          </Button>
        </div>

        <NamespaceList
          namespaces={filteredNamespaces}
          loading={loading}
          total={filteredNamespaces.length}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          nameUrlBuilder={item => `/workspace/namespace/detail?namespaceId=${item.namespaceId}`}
          renderExtraActions={renderExtraActions}
        />

        <Modal
          title="加速生效确认"
          visible={isModalVisible}
          onOk={handleConfirmQuickEffect}
          onCancel={() => setIsModalVisible(false)}
        >
          {latestVersionInfo ? (
            <>
              <Alert
                message="请确保下方的版本是要推送的正确内容版本"
                description="如果正确内容版本尚未发布，请先完成正确内容发布后再进行加速"
                type="error"
              />
              <Card
                title="待加速版本"
                style={{
                  marginBottom: 16,
                  marginTop: 5,
                }}
              >
                <p>
                  <strong>版本号：</strong>
                  <a
                    href={`/index.htm#/namespace/version/detail/${latestVersionInfo.namespaceId}-${latestVersionInfo.versionVersion}`}
                    target="_blank"
                  >
                    {latestVersionInfo.versionVersion}
                  </a>
                </p>
                <p>
                  <strong>最后操作时间：</strong>
                  {moment(latestVersionInfo.gmtModified).format('YYYY-MM-DD HH:mm:ss')}
                </p>
                <p>
                  <strong>操作人：</strong>
                  {latestVersionInfo.operator}
                </p>
              </Card>
            </>
          ) : (
            <Card style={{ marginBottom: 16 }}>
              <p>暂无版本信息</p>
            </Card>
          )}

          <Form form={form}>
            <Form.Item name="needOnlinePush" valuePropName="checked">
              <Checkbox>推送至在线设备</Checkbox>
            </Form.Item>
            <Form.Item name="needEnableBootNamespace" valuePropName="checked">
              <Checkbox
                onChange={e => {
                  setIsWhitelisted(e.target.checked);
                  if (e.target.checked) {
                    form.setFieldsValue({ expireTime: moment().add(3, 'days') });
                  }
                }}
              >
                加入快速通道白名单
              </Checkbox>
            </Form.Item>
            {isWhitelisted && (
              <Form.Item
                label="白名单到期时间"
                name="expireTime"
                rules={[
                  {
                    required: isWhitelisted,
                    message: '请选择到期时间',
                  },
                ]}
              >
                <DatePicker
                  showTime
                  disabledDate={current => current && current < moment().endOf('day')}
                />
              </Form.Item>
            )}
          </Form>
        </Modal>
      </PageContainer>
    </>
  );
}

export const dataLoader = defineDataLoader([async () => getBootNamespaceList({ pageSize: 100 })]);

'use client';

import { useEffect, useState } from 'react';
import { history } from 'ice';
import { Button, Card, Space, Table, Tag, Tooltip, Typography } from 'antd';
import {
  EditOutlined,
  HistoryOutlined,
  PushpinOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { defineDataLoader } from '@ice/runtime/data-loader';
import { getNamespaceListForMe } from '@/services/namespace';
import { Link, useData } from '@ice/runtime/router';
import { queryModuleByModuleIds } from '@/services/service';
import { LOAD_LEVEL_CONFIG, TYPE_CONFIG } from '@/constants/namespace';
import { PageContainer } from '@ant-design/pro-layout';
import { MtlModuleList, TextNamespaceEditor, User } from '@/components';
import { MtlModule, NamespaceBO } from '@/types/namespace';

const { Text } = Typography;

export default function Component() {
  const list = useData();
  const [myNamespaceList, setMyNamespaceList] = useState(list);
  const [mtlModuleMap, setMtlModuleMap] = useState<Record<string, MtlModule>>({});

  const [pinnedAppKeys, setPinnedAppKeys] = useState<string[]>(() => {
    if (typeof window !== 'undefined') {
      return JSON.parse(localStorage.getItem('pinnedAppKeys') || '[]');
    }
    return [];
  });

  const [pinnedNamespaces, setPinnedNamespaces] = useState<string[]>(() => {
    if (typeof window !== 'undefined') {
      return JSON.parse(localStorage.getItem('pinnedNamespaces') || '[]');
    }
    return [];
  });

  const [namespaceEditorModalVisible, setNamespaceEditorModalVisible] = useState(false);
  const [editingNamespace, setEditingNamespace] = useState<NamespaceBO | undefined>();

  const showNamespaceEditorModal = (data?: NamespaceBO) => {
    setEditingNamespace(data);
    setNamespaceEditorModalVisible(true);
  };

  const refreshNamespaceList = async () => {
    const updatedList = await getNamespaceListForMe();
    setMyNamespaceList(updatedList);
  };

  useEffect(() => {
    (async () => {
      const modules = new Set<string>();
      myNamespaceList.forEach(group => {
        group.namespaceList.forEach(namespace => {
          namespace.modules?.split(',')?.forEach(id => id && modules.add(id));
        });
      });
      const moduleId2ModuleInfoMap = await queryModuleByModuleIds(Array.from(modules));
      setMtlModuleMap(moduleId2ModuleInfoMap);
    })();
  }, [myNamespaceList]);

  const getColumns = (isPinnedSection: boolean) => {
    const baseColumns: ColumnsType<NamespaceBO> = isPinnedSection
      ? [
          {
            title: '应用',
            key: 'app',
            render: (_, record) => {
              const group = myNamespaceList.find(g => g.app.appKey === record.appKeyOrGroup);
              return (
                <Space direction={'vertical'}>
                  <Text>{record.appKeyOrGroup}</Text>
                  {group && <Tag color="purple">{group.app.appName}</Tag>}
                </Space>
              );
            },
          },
        ]
      : [];

    const columns: ColumnsType<NamespaceBO> = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Link
            className="text-blue-500 hover:text-blue-700"
            to={`/workspace/namespace/detail?namespaceId=${record.namespaceId}`}
          >
            {text}
          </Link>
        ),
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        render: type => {
          const config = TYPE_CONFIG[type] || {};
          return (
            <Tooltip title={config.desc}>
              <Tag color={config.color}>{config.label}</Tag>
            </Tooltip>
          );
        },
      },
      {
        title: '加载级别',
        dataIndex: 'loadLevel',
        key: 'loadLevel',
        render: (_, record) => {
          const config = LOAD_LEVEL_CONFIG[record.loadLevel] || {};
          return (
            <Tooltip title={config.desc}>
              <Tag color={config.color}>{config.label}</Tag>
            </Tooltip>
          );
        },
      },
      {
        title: '摩天轮模块',
        dataIndex: 'modules',
        key: 'modules',
        render: (moduleIds: string) => (
          <MtlModuleList moduleIds={moduleIds?.split(',')} moduleInfoMap={mtlModuleMap} />
        ),
      },
      {
        title: '相关人员',
        dataIndex: 'owners',
        key: 'owners',
        render: (owners: string, record) => (
          <Space direction="vertical">
            <Space>
              管理员:
              <User empIds={record?.owners?.split(',')} showAvatar size={'xsmall'} avatarNum={3} />
            </Space>
            <Space>
              测试负责人:
              <User empIds={record?.testers?.split(',')} showAvatar size={'xsmall'} avatarNum={3} />
            </Space>
          </Space>
        ),
      },
      {
        title: '描述',
        dataIndex: 'detail',
        key: 'detail',
      },
      {
        title: '操作',
        key: 'actions',
        render: (_, record) => (
          <div className="flex gap-2">
            <Tooltip title={pinnedNamespaces.includes(record.namespaceId) ? '取消置顶' : '置顶'}>
              <Button
                icon={<PushpinOutlined />}
                type={pinnedNamespaces.includes(record.namespaceId) ? 'primary' : 'default'}
                onClick={() => toggleNamespacePin(record.namespaceId)}
                size="small"
              />
            </Tooltip>
            <Button
              type={'primary'}
              icon={<EditOutlined />}
              size="small"
              onClick={() => showNamespaceEditorModal(record)}
            >
              编辑
            </Button>
            <Button
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => {
                history?.push(
                  `/workspace/version/list?name=${record.name}&appKey=${record.appKeyOrGroup}`,
                );
              }}
            >
              发布列表
            </Button>
          </div>
        ),
      },
    ];

    return [...baseColumns, ...columns];
  };

  const togglePin = (appKey: string) => {
    const newPinnedAppKeys = pinnedAppKeys.includes(appKey)
      ? pinnedAppKeys.filter(key => key !== appKey)
      : [...pinnedAppKeys, appKey];

    setPinnedAppKeys(newPinnedAppKeys);
    localStorage.setItem('pinnedAppKeys', JSON.stringify(newPinnedAppKeys));
  };

  const toggleNamespacePin = (namespaceId: string) => {
    const newPinnedNamespaces = pinnedNamespaces.includes(namespaceId)
      ? pinnedNamespaces.filter(id => id !== namespaceId)
      : [...pinnedNamespaces, namespaceId];

    setPinnedNamespaces(newPinnedNamespaces);
    localStorage.setItem('pinnedNamespaces', JSON.stringify(newPinnedNamespaces));
  };

  const getPinnedNamespaces = () => {
    const allNamespaces = myNamespaceList.flatMap(group => group.namespaceList);
    return allNamespaces
      .filter(ns => pinnedNamespaces.includes(ns.namespaceId))
      .sort((a, b) => a.appKeyOrGroup.localeCompare(b.appKeyOrGroup));
  };

  const sortedGroups = [...myNamespaceList].sort((a, b) => {
    const aIsPinned = pinnedAppKeys.includes(a.app.appKey);
    const bIsPinned = pinnedAppKeys.includes(b.app.appKey);
    if (aIsPinned && !bIsPinned) return -1;
    if (!aIsPinned && bIsPinned) return 1;
    return 0;
  });

  return (
    <PageContainer
      header={{
        ghost: true,
        title: '我的配置',
        extra: [
          <Button type="primary" onClick={() => showNamespaceEditorModal()}>
            新建配置
          </Button>,
        ],
      }}
    >
      {getPinnedNamespaces().length > 0 && (
        <Card title="置顶配置" className="shadow-sm mb-4">
          <Table
            columns={getColumns(true)}
            dataSource={getPinnedNamespaces()}
            rowKey="namespaceId"
            pagination={false}
            size="middle"
          />
        </Card>
      )}
      {sortedGroups.map(group => (
        <Card
          key={group.app.appKey}
          title={
            <div className="flex items-center gap-2">
              <span>
                {group.app.appName} (AppKey: {group.app.appKey})
              </span>
              <Tooltip title={pinnedAppKeys.includes(group.app.appKey) ? '取消置顶' : '置顶'}>
                <Button
                  icon={<PushpinOutlined />}
                  type={pinnedAppKeys.includes(group.app.appKey) ? 'primary' : 'default'}
                  onClick={() => togglePin(group.app.appKey)}
                  size="small"
                />
              </Tooltip>
              <Button
                icon={<UnorderedListOutlined />}
                type="default"
                size="small"
                onClick={() =>
                  history?.push(`/workspace/namespace/list?appKey=${group.app.appKey}`)}
              />
            </div>
          }
          className="shadow-sm mb-4"
        >
          <Table
            columns={getColumns(false)}
            dataSource={group.namespaceList}
            rowKey="namespaceId"
            pagination={false}
            size="middle"
          />
        </Card>
      ))}
      <TextNamespaceEditor
        visible={namespaceEditorModalVisible}
        initialData={editingNamespace}
        showModal={setNamespaceEditorModalVisible}
        mtlModuleMap={mtlModuleMap}
        onSuccess={refreshNamespaceList}
      />
    </PageContainer>
  );
}

export const dataLoader = defineDataLoader(async () => {
  return await getNamespaceListForMe();
});

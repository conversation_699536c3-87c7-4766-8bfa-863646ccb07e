import React, { useEffect, useState } from 'react';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Layout,
  message,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tabs,
  Typography,
} from 'antd';
import {
  DownOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  LinkOutlined,
  UpOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  KnockoutVersionModel,
  PropertiesDiffViewer,
  PropertiesEditor,
  SceneEditorModal,
  StrategyEditor,
  User,
} from '@/components';
import { getNamespaceDetail } from '@/services/namespace';
import { defineDataLoader } from '@ice/runtime/data-loader';
import { queryResourceDetail } from '@/services/resouce';
import { ListItem, NamespaceDetail } from '@/types/namespace';
import { useData } from '@ice/runtime/router';
import { createVersion, queryKnockoutVersions } from '@/services/version';
import {
  LOAD_LEVEL_CONFIG,
  NS_DEFAULT_CUSTOM_VALUE,
  NS_DEFAULT_VALUE,
  TYPE_CONFIG,
} from '@/constants/namespace';
import { getJSONValueKeysFromProperties, getKeysFromProperties } from '@/lib/utils';
import { SCENE_MAP } from '@/constants/scene';
import { getScenesContentsByResource } from '@/utils/version';
import { KnockoutVersion, ResourceBO } from '@/types/version';
import { Expression } from '@/components/condition/StrategyEditor';

const { Content } = Layout;
const { Text, Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

export default function Component() {
  const namespaceDetail: NamespaceDetail = useData();
  const [basicInfo, setBasicInfo] = useState(
    Object.assign(
      {},
      namespaceDetail.appBO,
      namespaceDetail.changeBO || {},
      namespaceDetail.namespaceBO || {},
    ),
  );
  const [editModalVisible, setEditModalVisible] = useState(false);
  // 被删除的版本列表
  const [deletedVersions, setDeletedVersions] = useState<ListItem[]>([]);
  // 版本列表中被选中的线上版本(默认选中兜底版本)
  const [activeVersion, setActiveVersion] = useState(namespaceDetail?.list?.slice(-1)?.[0] || {});
  // 编辑区域的策略表达式
  const [activeStrategyExpression, setActiveStrategyExpression] = useState<Expression[]>([]);
  // 编辑区域原有的多场景配置内容
  const [originalScenesContents, setOriginalScenesContents] = useState<
    { name: string; content: string }[]
  >([]);
  // 编辑区域最新的多场景配置内容
  const [modifiedScenesContents, setModifiedScenesContents] = useState<
    {
      name: string;
      content: string;
    }[]
  >([
    {
      name: 'Default',
      content: namespaceDetail.namespaceBO?.type === 3 ? NS_DEFAULT_CUSTOM_VALUE : NS_DEFAULT_VALUE,
    },
  ]);

  // 是否正在编辑策略表达式
  const [isEditingExpression, setIsEditingExpression] = useState(false);

  // 当前选中的场景名
  const [activeSceneName, setActiveSceneName] = useState('Default');
  const [sceneModalVisible, setSceneModalVisible] = useState(false);
  const [isEditingSceneName, setIsEditingSceneName] = useState(false);

  // 变更内容弹窗
  const [diffModalVisible, setDiffModalVisible] = useState(false);

  // 待删除版本
  const [knockoutVersionModalVisible, setKnockoutVersionModalVisible] = useState(false);
  const [knockoutVersions, setKnockoutVersions] = useState<KnockoutVersion[]>([]);

  // 待发布版本
  const waitPublishVersions = namespaceDetail?.waitList || [];
  const versions = namespaceDetail?.list || [];

  const activeStrategyStr = activeStrategyExpression?.length
    ? activeStrategyExpression.map(expr => `${expr.key}${expr.operator}${expr.value}`).join('&')
    : null;

  const isExistingStrategy = versions.some(
    s =>
      s.strategy ===
      activeStrategyExpression.map(expr => `${expr.key}${expr.operator}${expr.value}`).join(' & '),
  );

  const allowedKeys = getKeysFromProperties(
    modifiedScenesContents?.find(i => i.name === 'Default')?.content,
  );
  const noStrategyList = namespaceDetail.noStrategyList?.filter(i => i.appVersion !== '*') || [];
  const [noStrategyListCollapsed, setNoStrategyListCollapsed] = useState(true);

  useEffect(() => {
    (async () => {
      if (!activeVersion || !versions?.length) {
        return;
      }

      const resourceId = versions.filter(i => i.id === activeVersion.id)?.[0]?.resourceId;
      const resource: ResourceBO = await queryResourceDetail(resourceId);
      const scenes = getScenesContentsByResource(resource);
      setOriginalScenesContents(scenes);
      setModifiedScenesContents(scenes);
    })();
  }, [activeVersion]);

  const handleEditorChange = (updateContent, sceneName) => {
    const updatedScenarios = modifiedScenesContents.map(scene =>
      (scene.name === sceneName
        ? {
            ...scene,
            content: updateContent,
          }
        : scene),
    );
    setModifiedScenesContents(updatedScenarios);
  };

  const handleSubmitSceneName = sceneName => {
    if (modifiedScenesContents.map(i => i.name).includes(sceneName)) {
      throw new Error('该场景已存在');
    }

    if (isEditingSceneName) {
      setModifiedScenesContents(
        modifiedScenesContents.map(scenario =>
          (scenario.name === activeSceneName
            ? {
                ...scenario,
                name: sceneName,
              }
            : scenario),
        ),
      );
    } else {
      setModifiedScenesContents([
        ...modifiedScenesContents,
        {
          name: sceneName,
          content: '',
        },
      ]);
    }

    setActiveSceneName(sceneName);
    setSceneModalVisible(false);
  };

  const handleVersionClick = versionItem => {
    setActiveVersion(versionItem);
    setActiveSceneName('Default');
    setIsEditingExpression(false);
    setActiveStrategyExpression(
      versionItem?.strategy ? parseExpressionString(versionItem.strategy) : [],
    );
  };

  const handleEditBasicInfo = () => {
    setEditModalVisible(true);
  };

  const handleEditModalOk = values => {
    setBasicInfo(values);
    setEditModalVisible(false);
  };

  const handleDeleteConfig = () => {
    Modal.confirm({
      title: 'Are you sure you want to delete this configuration?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone.',
      onOk() {
        // Here you would typically delete the configuration
        message.success('Configuration deleted successfully');
      },
    });
  };

  const handleCompare = strategyId => {
    // Implement comparison logic
    message.info(`Comparing strategy ${strategyId}`);
  };

  const handleVersionDelete = versionId => {
    const strategyToDelete = versions.find(s => s.id === versionId);
    if (strategyToDelete) {
      setDeletedVersions([...deletedVersions, strategyToDelete]);
    }
  };

  const handleVersionRestore = versionId => {
    setDeletedVersions(deletedVersions.filter(s => s.id !== versionId));
  };

  const handleViewDetails = versionItem => {
    const detailUrl = `/v5#/workspace/version/detail?namespaceId=${basicInfo.namespaceId}&version=${versionItem.version}`;
    window.open(detailUrl, '_blank');
  };

  const parseExpressionString = expressionString => {
    return expressionString.split('&').map(expr => {
      const [key, operator, value] = expr
        .trim()
        .match(/(\w+)([=<>!~]+)(.+)/)
        .slice(1);
      return {
        key,
        operator,
        value,
      };
    });
  };

  const strategyColumns = [
    {
      title: '策略',
      key: 'strategy',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {record.strategy && (
            <Text
              strong
              style={{
                textDecorationLine: deletedVersions.some(s => s.id === record.id)
                  ? 'line-through'
                  : 'none',
              }}
            >
              {record.strategy}
            </Text>
          )}
          <Text
            type="secondary"
            style={{
              textDecorationLine: deletedVersions.some(s => s.id === record.id)
                ? 'line-through'
                : 'none',
            }}
          >{`${record.version}(${record.creator})`}</Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'operations',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {/* <Button type="link" onClick={() => handleCompare(record.id)}>Compare</Button> */}
          {record.strategy ? (
            deletedVersions.some(s => s.id === record.id) ? (
              <Button type="link" onClick={() => handleVersionRestore(record.id)}>
                恢复
              </Button>
            ) : (
              <Button type="link" danger onClick={() => handleVersionDelete(record.id)}>
                删除
              </Button>
            )
          ) : null}
          <Button type="link" onClick={() => handleViewDetails(record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  const noStrategyColumns = [
    {
      title: 'App Version',
      key: 'appVersion',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text
            strong
            style={{
              textDecorationLine: deletedVersions.some(s => s.id === record.id)
                ? 'line-through'
                : 'none',
            }}
          >
            {record.appVersion}
          </Text>
          <Text
            type="secondary"
            style={{
              textDecorationLine: deletedVersions.some(s => s.id === record.id)
                ? 'line-through'
                : 'none',
            }}
          >{`${record.version}(${record.creator})`}</Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'operations',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {/* <Button type="link" onClick={() => handleCompare(record.id)}>Compare</Button> */}
          {/* {((deletedVersions.some(s => s.id === record.id) ? ( */}
          {/*   <Button type="link" onClick={() => handleVersionRestore(record.id)}>恢复</Button> */}
          {/* ) : ( */}
          {/*   <Button type="link" danger onClick={() => handleVersionDelete(record.id)}>删除</Button> */}
          {/* )))} */}
          <Button type="link" onClick={() => handleViewDetails(record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  const handlePublish = async () => {
    const content = modifiedScenesContents.find(s => (s.name = 'Default'))?.content;
    if (!content) {
      message.error('默认场景内容不能为空');
      return;
    }

    const scenesContents = modifiedScenesContents
      .filter(s => s.name !== 'Default')
      .reduce((acc, cur) => {
        acc[cur.name] = cur.content;
        return acc;
      }, {});

    const deleteVersions = deletedVersions?.map(i => i.version);
    try {
      const { namespaceId, version } = await createVersion(
        basicInfo.namespaceId,
        content,
        scenesContents,
        activeStrategyStr,
        deleteVersions,
      );
      window.location.href = `/v5#/workspace/version/detail?namespaceId=${namespaceId}&version=${version}`;
    } catch (e) {
      message.error(e.message);
    }
  };

  const renderBasicInfo = () => {
    return (
      <Card
        title={
          <div className="tag bg-orange-100 text-orange-800 text-lg inline-block px-2.5 py-0.5 rounded whitespace-nowrap">
            {basicInfo.name || '-'}
          </div>
        }
        className="mb-5"
        extra={
          <Space>
            <Button
              type="link"
              icon={<LinkOutlined />}
              onClick={() =>
                window.open(
                  `v5#/workspace/version/list/?name=${basicInfo.name}&namespaceId=${basicInfo.namespaceId}&appKey=${basicInfo.appKey}`,
                )}
            >
              发布单列表
            </Button>
            <Button
              type="link"
              icon={<LinkOutlined />}
              onClick={() => window.open(`/#/namespace/version/debug/${basicInfo.namespaceId}`)}
            >
              Debug
            </Button>
            <Button
              type="link"
              icon={<LinkOutlined />}
              onClick={() => window.open('https://aliyuque.antfin.com/wireless-orange/wiki/vgtkbb')}
            >
              帮助文档
            </Button>
            <Button disabled icon={<EditOutlined />} onClick={handleEditBasicInfo}>
              编辑
            </Button>
            {/* <Button danger icon={<DeleteOutlined />} onClick={handleDeleteConfig}>删除</Button> */}
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Text strong>AppKey:</Text> {`${basicInfo.appKey}-${basicInfo.appName}`}
          </Col>
          <Col span={6}>
            <Text strong>加载级别:</Text> {LOAD_LEVEL_CONFIG[basicInfo.loadLevel]?.label || '-'}
          </Col>
          <Col span={6}>
            <Text strong>类型:</Text> {TYPE_CONFIG[basicInfo.type]?.label || '-'}
          </Col>
          <Col span={6}>
            <Text strong>摩天轮模块:</Text> {basicInfo.modules}
          </Col>
          <Col span={6}>
            <Text strong>创建时间:</Text> {basicInfo.gmtCreateTime}
          </Col>
          <Col span={6}>
            <Row>
              <Text className="mr-2" strong>
                管理员:{' '}
              </Text>
              <User empIds={basicInfo.owners?.split(',')} showAvatar size={'xsmall'} />
            </Row>
          </Col>
          <Col span={6}>
            <Row>
              <Text className="mr-2" strong>
                测试负责人:{' '}
              </Text>
              <User empIds={basicInfo.testers?.split(',')} showAvatar size={'xsmall'} />
            </Row>
          </Col>
        </Row>
      </Card>
    );
  };

  let disablePublishReason = '';
  if (waitPublishVersions?.length) {
    const waitPublishVersion = waitPublishVersions[0];
    const publishVersionDetailUrl = `/v5#/workspace/version/detail?namespaceId=${waitPublishVersion.namespaceId}&version=${waitPublishVersion.version}`;
    disablePublishReason = `<span style="color: red">警告：仍有未发布完的版本，请完成 <a href="${publishVersionDetailUrl}" style="color: #3599ff">发布</a> 后再操作</span>`;
  } else if (
    !deletedVersions?.length &&
    JSON.stringify(originalScenesContents) === JSON.stringify(modifiedScenesContents) &&
    activeStrategyStr == activeVersion.strategy
  ) {
    disablePublishReason = '当前版本策略内容无变更且无待删除版本';
  } else if (
    deletedVersions.length &&
    activeStrategyStr &&
    deletedVersions.map(v => v.strategy || '').includes(activeStrategyStr)
  ) {
    disablePublishReason = '当前策略已被置为待删除策略';
  } else if (isEditingExpression) {
    disablePublishReason = '策略编辑中...';
  } else {
    const emptySceneNames = modifiedScenesContents
      .filter(({ content }) => {
        return !content.trim();
      })
      .map(i => i.name);
    if (emptySceneNames?.length) {
      disablePublishReason = `场景[${emptySceneNames.join()}]内容为空`;
    }
  }

  return (
    <Layout
      style={{
        minHeight: '100vh',
        padding: '20px',
      }}
    >
      <Content>
        {renderBasicInfo()}

        <Row gutter={16}>
          <Col span={8}>
            <Card title="策略列表" style={{ marginBottom: '20px' }}>
              <Table
                dataSource={versions}
                columns={strategyColumns}
                pagination={false}
                rowClassName={record =>
                  (record.id === activeVersion.id ? 'ant-table-row-selected' : '')}
                onRow={record => ({
                  onClick: () => handleVersionClick(record),
                })}
              />
            </Card>
            {!!noStrategyList?.length && (
              <Card
                title="常规版本（待下线）"
                style={{ marginBottom: '20px' }}
                extra={
                  <Button
                    type={'link'}
                    onClick={() => setNoStrategyListCollapsed(!noStrategyListCollapsed)}
                  >
                    {noStrategyListCollapsed ? (
                      <span>
                        更多 <DownOutlined />
                      </span>
                    ) : (
                      <span>
                        收起 <UpOutlined />
                      </span>
                    )}
                  </Button>
                }
              >
                {!noStrategyListCollapsed && (
                  <Table
                    dataSource={noStrategyList}
                    columns={noStrategyColumns}
                    pagination={false}
                    rowClassName={record =>
                      (record.id === activeVersion.id ? 'ant-table-row-selected' : '')}
                    onRow={record => ({
                      onClick: () => handleVersionClick(record),
                    })}
                  />
                )}
              </Card>
            )}
          </Col>
          <Col span={16}>
            <Card
              title="配置内容"
              extra={
                <Space>
                  {disablePublishReason ? (
                    <span dangerouslySetInnerHTML={{ __html: disablePublishReason }} />
                  ) : null}
                  <Button
                    disabled={!!disablePublishReason}
                    type="primary"
                    onClick={() => {
                      setDiffModalVisible(true);
                    }}
                  >
                    提交发布
                  </Button>
                </Space>
              }
              style={{ marginBottom: '20px' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <StrategyEditor
                  expressions={activeStrategyExpression}
                  isEditing={isEditingExpression}
                  onEditingChange={editing => setIsEditingExpression(editing)}
                  onExpressionsChange={newExpressions =>
                    setActiveStrategyExpression(newExpressions)}
                />
                <Tabs
                  tabBarExtraContent={'右击配置行可以进行 JSON 编辑'}
                  activeKey={activeSceneName}
                  onChange={setActiveSceneName}
                  type="editable-card"
                  onEdit={(targetKey, action) => {
                    if (action === 'add') {
                      setIsEditingSceneName(false);
                      setSceneModalVisible(true);
                    } else if (action === 'remove') {
                      setModifiedScenesContents(
                        modifiedScenesContents.filter(s => s.name !== targetKey),
                      );
                      if (activeSceneName === targetKey) {
                        setActiveSceneName(modifiedScenesContents[0].name);
                      }
                    }
                  }}
                >
                  {modifiedScenesContents.map(scenario => {
                    // default 类型的才提供值 json 变化校验
                    const jsonValueKeys =
                      basicInfo.type === 1
                        ? getJSONValueKeysFromProperties(
                            originalScenesContents.filter(i => i.name == scenario.name)?.[0]
                              ?.content,
                          )
                        : [];

                    return (
                      <TabPane
                        tab={
                          <span>
                            {SCENE_MAP[scenario.name]}
                            {scenario.name !== 'Default' && (
                              <EditOutlined
                                onClick={e => {
                                  e.stopPropagation();
                                  setActiveSceneName(scenario.name);
                                  setIsEditingSceneName(true);
                                  setSceneModalVisible(true);
                                }}
                                style={{ marginLeft: 8 }}
                              />
                            )}
                          </span>
                        }
                        key={scenario.name}
                        closable={scenario.name !== 'Default'}
                      >
                        <PropertiesEditor
                          jsonValueKeys={jsonValueKeys}
                          allowedKeys={scenario.name !== 'Default' ? allowedKeys : undefined}
                          height="400px"
                          value={scenario.content}
                          onChange={value => handleEditorChange(value, scenario.name)}
                        />
                      </TabPane>
                    );
                  })}
                </Tabs>
              </Space>
            </Card>
          </Col>
        </Row>

        <Modal
          title="Edit Basic Information"
          visible={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          footer={null}
        >
          <Form initialValues={basicInfo} onFinish={handleEditModalOk}>
            <Form.Item name="namespace" label="Namespace">
              <Input />
            </Form.Item>
            <Form.Item name="appKey" label="AppKey">
              <Input />
            </Form.Item>
            <Form.Item name="upgradeLevel" label="Upgrade Level">
              <Select>
                <Option value="highInit">highInit</Option>
                <Option value="mediumInit">mediumInit</Option>
                <Option value="lowInit">lowInit</Option>
              </Select>
            </Form.Item>
            <Form.Item name="type" label="Type">
              <Select>
                <Option value="STANDARD">STANDARD</Option>
                <Option value="CUSTOM">CUSTOM</Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Save Changes
              </Button>
            </Form.Item>
          </Form>
        </Modal>
        <SceneEditorModal
          visible={sceneModalVisible}
          initialValue={isEditingSceneName ? activeSceneName : undefined}
          onCancel={() => setSceneModalVisible(false)}
          onOk={handleSubmitSceneName}
          namespaceType={basicInfo.type}
        />
        <Modal
          title={
            (isExistingStrategy ? '修改' : '新增') +
            (activeStrategyStr ? `策略 ${activeStrategyStr} 版本` : '兜底版本')
          }
          open={diffModalVisible}
          onOk={async () => {
            const deleteVersions = deletedVersions.map(i => i.version);
            try {
              setKnockoutVersions(
                await queryKnockoutVersions(
                  basicInfo.namespaceId,
                  activeStrategyStr,
                  deleteVersions,
                ),
              );
              setKnockoutVersionModalVisible(true);
            } catch (e) {
              message.error(e.message);
            } finally {
              setDiffModalVisible(false);
            }
          }}
          onCancel={() => setDiffModalVisible(false)}
          width={1200}
        >
          <PropertiesDiffViewer
            originalScenesContents={originalScenesContents}
            diffScenesContents={modifiedScenesContents}
          />
        </Modal>

        <Modal
          title={
            <Title level={4}>
              <WarningOutlined className="text-yellow-500 mr-2" />
              {knockoutVersions?.length ? '本次发布将删除以下版本' : '本次发布无版本被删除'}
            </Title>
          }
          open={knockoutVersionModalVisible}
          onOk={handlePublish}
          onCancel={() => setKnockoutVersionModalVisible(false)}
          width={1000}
          centered
          footer={[
            <Button key="cancel" onClick={() => setKnockoutVersionModalVisible(false)}>
              取消
            </Button>,
            <Button key="ok" type="primary" onClick={handlePublish}>
              确认
            </Button>,
          ]}
        >
          <KnockoutVersionModel knockoutVersions={knockoutVersions} />
        </Modal>
      </Content>
    </Layout>
  );
}

export const dataLoader = defineDataLoader(async ctx => {
  return getNamespaceDetail(ctx?.query?.namespaceId);
});

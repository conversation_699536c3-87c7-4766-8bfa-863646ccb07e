import { NamespaceList, TextNamespaceEditor } from '@/components';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Input, message, Popconfirm, Select } from 'antd';
import { EditOutlined, HistoryOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { hasNsEditPermission, hasNsTestPermission } from '@/utils/permission';
import { getAppNamespaceList, permissionApply } from '@/services/namespace';
import { getBpmsUrl } from '@/utils/link';
import { queryAppList } from '@/services/app';
import { useEffect, useState } from 'react';
import { isSubstring, sortAppKeys } from '@/lib/utils';
import { NamespaceBO } from '@/types/namespace';
import { getCurrentTBAppKeys, getFirstTBAppKey } from '@/utils/common';

const { Option } = Select;

export default function Index() {
  const [appList, setAppList] = useState<any[]>([]);
  const [appKey, setAppKey] = useState<string | undefined>(() => {
    try {
      return localStorage.getItem('text-namespace-recent-appKey') || getFirstTBAppKey();
    } catch {
      return getFirstTBAppKey();
    }
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [namespaceList, setNamespaceList] = useState<NamespaceBO[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingNamespace, setEditingNamespace] = useState<NamespaceBO | undefined>(undefined);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  useEffect(() => {
    (async () => {
      setAppList(await queryAppList());
    })();
  }, []);

  useEffect(() => {
    if (!appKey) {
      setNamespaceList([]);
      return;
    }
    setLoading(true);
    getAppNamespaceList(appKey)
      .then(({ namespaceList }) => {
        setNamespaceList(namespaceList);
      })
      .finally(() => setLoading(false));
  }, [appKey]);

  const filteredNamespaces = namespaceList.filter(
    ns =>
      ns.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      (ns.detail || '').toLowerCase().includes(searchKeyword.toLowerCase()),
  );

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1); // 改变页面大小时重置到第一页
    }
  };

  const handleAppKeyChange = value => {
    setAppKey(value);
    setSearchKeyword('');
    // Save to localStorage
    try {
      if (value) {
        localStorage.setItem('text-namespace-recent-appKey', value);
      } else {
        localStorage.removeItem('text-namespace-recent-appKey');
      }
    } catch {
      // Ignore localStorage errors
    }
  };

  const handleSearch = keyword => {
    setSearchKeyword(keyword);
  };

  const handleCreateSuccess = () => {
    // 刷新命名空间列表
    if (appKey) {
      setLoading(true);
      getAppNamespaceList(appKey)
        .then(({ namespaceList }) => {
          setNamespaceList(namespaceList);
        })
        .finally(() => setLoading(false));
    }
    // 重置编辑状态
    setEditingNamespace(undefined);
  };

  const applySubmit = async (namespaceId: string, role: string) => {
    const result = await permissionApply(namespaceId, role);
    if (result === 'SKIP') {
      message.success('申请成功，可去我的配置里查看', 10);
    } else if (result) {
      message.success(
        <div className={'text-left'}>
          <h4>申请成功</h4>
          申请权限流程已创建，
          <a
            className={'text-blue-500 hover:text-blue-700'}
            href={getBpmsUrl(result)}
            target="_blank"
          >
            查看审批流程
          </a>
        </div>,
        10,
      );
    }
  };

  const renderApply = function (record, role: string) {
    const roleName = role === 'nsOwner' ? '管理员' : '测试负责人';
    const tips = `您可以联系管理员直接操作，或者点此走审批流程，确定要申请${record.name}的${roleName}权限吗(注意不要重复申请)？`;

    return (
      <Popconfirm
        title={tips}
        onConfirm={() => {
          applySubmit(record.namespaceId, role);
        }}
      >
        {' '}
        <Button size="small" icon={<PlusOutlined />}>
          申请{roleName}权限
        </Button>
      </Popconfirm>
    );
  };

  const renderDeployList = record => {
    return (
      <Button
        size="small"
        icon={<HistoryOutlined />}
        onClick={() => {
          window.open(
            `v5#/workspace/version/list?name=${record.name}&appKey=${record.appKeyOrGroup}`,
          );
        }}
      >
        发布列表
      </Button>
    );
  };

  const renderExtraActions = record => {
    if (!hasNsEditPermission(record) && !hasNsTestPermission(record)) {
      return [
        renderApply(record, 'nsOwner'),
        renderApply(record, 'nsTester'),
        renderDeployList(record),
      ];
    }
    if (hasNsEditPermission(record)) {
      return [
        <Button
          size="small"
          icon={<EditOutlined />}
          onClick={() => {
            setEditingNamespace(record);
            setModalVisible(true);
          }}
        >
          编辑
        </Button>,
        renderDeployList(record),
      ];
    }

    return [renderApply(record, 'nsOwner'), renderDeployList(record)];
  };

  return (
    <>
      <PageContainer
        header={{
          ghost: true,
          title: '全部配置',
          extra: (
            <div className="flex items-center">
              <span className="mr-2" style={{ fontSize: 14 }}>
                选择应用：
              </span>
              <Select
                placeholder="输入 AppKey 或者名称进行查找"
                onChange={handleAppKeyChange}
                style={{ width: 350 }}
                value={appKey || undefined}
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) => isSubstring(input, option!.children)}
              >
                {sortAppKeys(getCurrentTBAppKeys(), appList).map(item => (
                  <Option key={item.appKey} value={item.appKey}>
                    {`${item.appKey}-${item.appName}`}
                  </Option>
                ))}
              </Select>
            </div>
          ),
        }}
      >
        <div className="flex items-center mb-4">
          <Input.Search
            prefix={<SearchOutlined />}
            className="mr-4 max-w-md"
            disabled={!appKey}
            placeholder="输入名称或描述搜索配置"
            value={searchKeyword}
            onChange={e => handleSearch(e.target.value)}
            enterButton
          />
          <div className={'flex-1'} />
          <Button
            type="primary"
            onClick={() => {
              setEditingNamespace(undefined);
              setModalVisible(true);
            }}
          >
            新建配置
          </Button>
        </div>
        <NamespaceList
          namespaces={filteredNamespaces}
          loading={loading}
          total={filteredNamespaces.length}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          renderExtraActions={renderExtraActions}
          nameUrlBuilder={item => `/workspace/namespace/detail?namespaceId=${item.namespaceId}`}
        />
      </PageContainer>
      <TextNamespaceEditor
        visible={modalVisible}
        initialData={editingNamespace}
        showModal={show => {
          setModalVisible(show);
          if (!show) {
            setEditingNamespace(undefined);
          }
        }}
        onSuccess={handleCreateSuccess}
      />
    </>
  );
}

import { GlobalLayout } from '@/components';
import { Outlet } from 'ice';
import { asideMenuConfig } from '@/menuConfig';
import { useUserStore } from '@/store';
import { useEffect } from 'react';
import { usePageTitle } from '@/hooks/usePageTitle';

export default function Layout(props) {
  const updateCurrentUser = useUserStore(state => state.updateCurrentUser);

  // 更新页面标题
  usePageTitle();

  useEffect(() => {
    const systemInfo = (window as any)._DATA_ || {};
    updateCurrentUser({
      empId: systemInfo.userId,
      displayName: systemInfo.userName,
      avatar: `https://work.alibaba-inc.com/photo/${systemInfo.userId}.jpg?`,
    });
  }, []);

  return (
    <GlobalLayout menuItems={asideMenuConfig}>
      <Outlet />
    </GlobalLayout>
  );
}

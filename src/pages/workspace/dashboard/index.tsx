'use client';

import { useEffect, useState } from 'react';
import {
  Badge,
  Button,
  Input,
  Layout,
  List,
  message,
  Modal,
  Popconfirm,
  Select,
  Tabs,
  Tag,
} from 'antd';
import {
  CheckCircleOutlined,
  RocketOutlined,
  SafetyCertificateOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { DataEntryList } from '@ali/mc-uikit';
import * as TaskController from '@/services/orange-be/TaskController';
import { AnnouncementCard, NamespaceList, NamespaceSidebar, User, UserSelect } from '@/components';
import { getCurrentTBAppKeys, getFormatDate, getTBAppName } from '@/utils/common';
import { getReleaseOrderDetailUrl } from '@/utils/link';
import { history } from 'ice';
import { TASK_STATUS_MAP, TASK_TYPE_MAP } from '@/constants/task';
import * as NamespaceController from '@/services/orange-be/NamespaceController';
import { FaTasks } from 'react-icons/fa';

const { Sider, Content } = Layout;

const getTaskTypeTag = (taskType: API.TaskType) => {
  const type = TASK_TYPE_MAP[taskType] || {
    color: 'default',
    text: taskType,
  };
  return <Tag color={type.color}>{type.text}</Tag>;
};

const getTaskStatusTag = (status: API.TaskStatus) => {
  const statusInfo = TASK_STATUS_MAP[status] || {
    color: 'default',
    text: status,
  };
  return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
};

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState<string>();
  const [activeStatusTab, setActiveStatusTab] = useState<API.TaskHandlerStatus>('PENDING');
  const [tasks, setTasks] = useState<API.TaskDetailDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const [namespaceLoading, setNamespaceLoading] = useState(false);
  const [namespaces, setNamespaces] = useState<API.NamespaceDTO[] | null>();
  const [namespacePagination, setNamespacePagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [taskCounts, setTaskCounts] = useState<Record<API.TaskHandlerStatus, number>>({
    PENDING: 0,
    COMPLETED: 0,
    NO_NEED: 0,
  });
  const [paginationMap, setPaginationMap] = useState<Record<API.TaskHandlerStatus, { current: number; pageSize: number; total: number }>>({
    PENDING: { current: 1, pageSize: 10, total: 0 },
    COMPLETED: { current: 1, pageSize: 10, total: 0 },
    NO_NEED: { current: 1, pageSize: 10, total: 0 },
  });

  const pagination = paginationMap[activeStatusTab];
  const [filters, setFilters] = useState<{
    taskType?: API.TaskType;
  }>({});
  const [transferModalVisible, setTransferModalVisible] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<string>();
  const [selectedUser, setSelectedUser] = useState<string>();
  const [namespaceFilters, setNamespaceFilters] = useState({
    appKey: '',
    keyword: '',
  });
  const [searchInputValue, setSearchInputValue] = useState('');

  const fetchTaskCounts = async () => {
    try {
      const response = await TaskController.countMyTaskByStatus();
      if (response.success && response.data) {
        setTaskCounts(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch task counts:', error);
    }
  };

  useEffect(() => {
    fetchTaskCounts();
  }, []);

  const fetchTasks = async (params: {
    page?: number;
    size?: number;
    taskType?: string;
    status?: string;
  }) => {
    setLoading(true);
    try {
      const response = await TaskController.queryMyTasks({
        page: params.page,
        size: params.size,
        taskType: params.taskType,
        status: params.status,
      });
      if (response.success) {
        setTasks(response.data || []);
        setPaginationMap(prev => ({
          ...prev,
          [activeStatusTab]: {
            current: response.current || 1,
            pageSize: response.size || 10,
            total: response.total || 0,
          },
        }));
        // 如果没有代办，则自动帮用户跳转到我的命名空间
        // if (!response.data?.length && !activeTab) {
        //     setActiveTab('namespace');
        // } else {
        setActiveTab('todo');
        // }
      } else {
        message.error(response.message || '获取任务列表失败');
      }
    } catch (error) {
      message.error('获取任务列表失败');
      console.error('Failed to fetch tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks({
      page: pagination.current,
      size: pagination.pageSize,
      taskType: filters.taskType,
      status: activeStatusTab,
    });
  }, [pagination.current, pagination.pageSize, filters, activeStatusTab]);

  const handleIgnore = async (taskId: string) => {
    try {
      const response = await TaskController.ignore({ taskId });
      if (response.success) {
        message.success('忽略任务成功');
        fetchTasks({
          page: pagination.current,
          size: pagination.pageSize,
          taskType: filters.taskType,
          status: activeStatusTab,
        });
        fetchTaskCounts();
      } else {
        message.error(response.message || '忽略任务失败');
      }
    } catch (error) {
      message.error('忽略任务失败');
      console.error('Failed to ignore task:', error);
    }
  };

  const handleTransfer = async (taskId?: string, workNo?: string) => {
    if (!taskId) {
      message.error('任务ID不能为空');
      return;
    }
    if (!workNo) {
      message.error('请选择转交人员');
      return;
    }
    try {
      const response = await TaskController.transfer({
        taskId,
        workNo,
      });
      if (response.success) {
        message.success('转交任务成功');
        setTransferModalVisible(false);
        setSelectedTaskId(undefined);
        setSelectedUser(undefined);
        fetchTasks({
          page: pagination.current,
          size: pagination.pageSize,
          taskType: filters.taskType,
          status: activeStatusTab,
        });
        fetchTaskCounts();
      } else {
        message.error(response.message || '转交任务失败');
      }
    } catch (error) {
      message.error('转交任务失败');
      console.error('Failed to transfer task:', error);
    }
  };

  const showTransferModal = (taskId: string) => {
    setSelectedTaskId(taskId);
    setTransferModalVisible(true);
  };

  const handleFilter = (values: any) => {
    setFilters({
      taskType: values.taskType,
    });
    setPaginationMap(prev => ({
      ...prev,
      [activeStatusTab]: {
        ...prev[activeStatusTab],
        current: 1,
      },
    }));
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPaginationMap(prev => ({
      ...prev,
      [activeStatusTab]: {
        ...prev[activeStatusTab],
        current: page,
        pageSize: pageSize || prev[activeStatusTab].pageSize,
      },
    }));
  };

  const handleProcess = (item: any) => {
    if (item.bizType === 'RELEASE_ORDER' && item.bizId) {
      const url = getReleaseOrderDetailUrl(item.bizId);
      if (url && history) {
        history.push(url);
      }
    }
  };

  const fetchNamespaces = async () => {
    try {
      setNamespaceLoading(true);
      const result = await NamespaceController.query({
        hasPermission: true,
        page: namespacePagination.current,
        size: namespacePagination.pageSize,
        appKey: namespaceFilters.appKey || undefined,
        keyword: namespaceFilters.keyword || undefined,
      });
      setNamespaces(result.data);
      setNamespacePagination(prev => ({
        ...prev,
        total: result.total,
      }));
    } catch (error) {
      message.error(error.errorMsg || error.message);
    } finally {
      setNamespaceLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'namespace') {
      fetchNamespaces();
    }
  }, [activeTab, namespacePagination.current, namespacePagination.pageSize, namespaceFilters]);

  const handleNamespacePageChange = (page: number, size?: number) => {
    setNamespacePagination(prev => ({
      ...prev,
      current: page,
      pageSize: size || prev.pageSize,
    }));
  };

  const handleNamespaceFilterChange = (values: { appKey?: string; keyword?: string }) => {
    setNamespaceFilters(prev => ({
      ...prev,
      ...values,
    }));
    setNamespacePagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  const handleSearch = (value: string) => {
    setSearchInputValue(value);
    handleNamespaceFilterChange({ keyword: value });
  };

  const renderTodoContent = () => (
    <DataEntryList
      title={
        <div className="flex items-center">
          <span
            className={`mr-4 cursor-pointer flex items-center ${
              activeStatusTab === 'PENDING' ? 'text-blue-600 font-medium' : ''
            }`}
            onClick={() => setActiveStatusTab('PENDING')}
          >
            {taskCounts.PENDING > 0 && (
              <Badge count={taskCounts.PENDING} style={{ backgroundColor: '#ff4d4f' }} />
            )}
            <span className="ml-2">{!taskCounts.PENDING ? '0 ' : ''}待我处理</span>
          </span>
          <span
            className={`mr-4 cursor-pointer ${
              activeStatusTab === 'COMPLETED' ? 'text-blue-600 font-medium' : ''
            }`}
            onClick={() => setActiveStatusTab('COMPLETED')}
          >
            {taskCounts.COMPLETED || 0} 我已处理
          </span>
          <span
            className={`cursor-pointer ${
              activeStatusTab === 'NO_NEED' ? 'text-blue-600 font-medium' : ''
            }`}
            onClick={() => setActiveStatusTab('NO_NEED')}
          >
            {taskCounts.NO_NEED || 0} 无需处理
          </span>
        </div>
      }
      dataSource={tasks}
      loading={loading}
      filters={[
        {
          label: '任务类型',
          name: 'taskType',
          items: [
            {
              label: (
                <span>
                  <CheckCircleOutlined className="mr-2" />
                  验证
                </span>
              ),
              title: '验证',
              value: 'VERIFY',
            },
            {
              label: (
                <span>
                  <RocketOutlined className="mr-2" />
                  发布
                </span>
              ),
              title: '发布',
              value: 'RELEASE',
            },
            {
              label: (
                <span>
                  <SafetyCertificateOutlined className="mr-2" />
                  审批
                </span>
              ),
              title: '审批',
              value: 'APPROVE',
            },
          ],
        },
      ]}
      onFilter={handleFilter}
      pagination={{
        position: 'bottom',
        align: 'center',
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: handlePageChange,
      }}
      renderItem={item => {
        const taskId = item.taskId as string;
        return (
          <List.Item className="border-b border-gray-100 last:border-b-0">
            <div className="flex items-center justify-between w-full">
              <div className="flex flex-col">
                <div className="font-medium text-gray-800 mb-1 flex items-center gap-2">
                  <span className="mr-2">{item.description}</span>
                  {item.type && getTaskTypeTag(item.type)}
                  {item.status && getTaskStatusTag(item.status)}
                </div>
                <div className="text-sm text-gray-500 flex-row flex items-center h-full">
                  <User empIds={[item.creator!]} showAvatar size={'xsmall'} />
                  <span className="ml-2">创建于 {getFormatDate(item.gmtCreate!)}</span>
                  {activeStatusTab === 'COMPLETED' && (
                    <div className="ml-2 flex items-center">
                      <User empIds={[item.modifier!]} showAvatar size={'xsmall'} />
                      <span className="ml-2">完成于 {getFormatDate(item.gmtModified!)}</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  type="link"
                  size="small"
                  hidden={item.myHandlerStatus !== 'PENDING'}
                  onClick={() => handleProcess(item)}
                >
                  去处理
                </Button>
                <Button
                  type="link"
                  size="small"
                  hidden={item.myHandlerStatus !== 'PENDING'}
                  onClick={() => showTransferModal(taskId)}
                >
                  转交
                </Button>
                <Popconfirm
                  title={'确认忽略该任务吗？'}
                  onConfirm={() => taskId && handleIgnore(taskId)}
                >
                  <Button hidden={item.myHandlerStatus !== 'PENDING'} type="link" size="small">
                    忽略
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </List.Item>
        );
      }}
    />
  );

  const renderNamespaceContent = () => {
    const noFilter = !namespaceFilters.appKey && !namespaceFilters.keyword;
    const isEmpty = namespaces && namespaces.length === 0;

    if (noFilter && isEmpty) {
      return (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="text-gray-500 mb-4">
            暂无命名空间，可前往所有命名空间创建命名空间/申请权限
          </div>
          <Button
            type="primary"
            onClick={() => {
              history?.push('/workspace/switch/namespaces');
            }}
          >
            跳转所有命名空间
          </Button>
        </div>
      );
    }

    return (
      <div>
        <div className="flex mb-4">
          <Select
            placeholder="选择客户端"
            allowClear
            style={{ width: 240 }}
            onChange={value => handleNamespaceFilterChange({ appKey: value })}
            value={namespaceFilters.appKey || undefined}
          >
            {getCurrentTBAppKeys().map(appKey => (
              <Select.Option key={appKey} value={appKey}>
                {getTBAppName(appKey)}
              </Select.Option>
            ))}
          </Select>
          <div className="flex-1" />
          <Input.Search
            placeholder="输入命名空间名或描述关键字搜索"
            prefix={<SearchOutlined />}
            className="max-w-md"
            value={searchInputValue}
            onChange={e => setSearchInputValue(e.target.value)}
            onSearch={handleSearch}
            enterButton
          />
        </div>
        <NamespaceList
          nameUrlBuilder={namespace => `/workspace/switch/namespaces/${namespace.namespaceId}`}
          loading={namespaceLoading}
          namespaces={(namespaces || []) as any[]}
          total={namespacePagination.total}
          currentPage={namespacePagination.current}
          pageSize={namespacePagination.pageSize}
          onPageChange={handleNamespacePageChange}
          showAppKey
        />
      </div>
    );
  };

  const tabItems = [
    {
      key: 'todo',
      label: (
        <span className="flex items-center">
          <FaTasks className={'mx-3'} />
          <span>我的待办</span>
        </span>
      ),
      children: renderTodoContent(),
    },
    {
      key: 'namespace',
      label: (
        <span className="flex items-center">
          <UserOutlined />
          <span>我的命名空间</span>
        </span>
      ),
      children: renderNamespaceContent(),
    },
  ];

  return (
    <Layout className="min-h-screen">
      <Content className="p-6">
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </Content>

      {/* Right Sidebar */}
      <Sider width={320} className="bg-white border-l border-gray-200">
        <div className="p-4">
          <AnnouncementCard className="mb-4" />
          <NamespaceSidebar />
        </div>
      </Sider>
      <Modal
        maskClosable={false}
        title="转交任务"
        open={transferModalVisible}
        onCancel={() => {
          setTransferModalVisible(false);
          setSelectedTaskId(undefined);
          setSelectedUser(undefined);
        }}
        onOk={() => selectedTaskId && handleTransfer(selectedTaskId, selectedUser)}
        okText="确认转交"
        cancelText="取消"
      >
        <div className="py-4">
          <UserSelect
            value={selectedUser}
            onChange={value => setSelectedUser(value as string)}
            placeholder="请选择转交人员"
          />
        </div>
      </Modal>
    </Layout>
  );
}

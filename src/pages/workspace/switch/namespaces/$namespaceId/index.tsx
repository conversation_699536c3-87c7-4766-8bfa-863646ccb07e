'use client';

import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useParams, useSearchParams } from 'ice';
import { PageContainer } from '@ant-design/pro-layout';
import {
  ClockCircleOutlined,
  ControlOutlined,
  FileOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import {
  ConditionManagementTab,
  ParameterManagementTab,
  ReleaseOrderManagementTab,
  VersionHistoryTab,
} from '@/components';
import './styles.css';

const { TabPane } = Tabs;

export default function NamespaceDetails() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(() => searchParams.get('tab') || 'parameter');
  const { namespaceId } = useParams();

  if (!namespaceId) {
    return (
      <PageContainer>
        <div className="flex justify-center items-center h-64">
          <span className="text-gray-500">命名空间ID无效</span>
        </div>
      </PageContainer>
    );
  }

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  return (
    <PageContainer>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        className="namespace-management-tabs"
        size="large"
      >
        <TabPane
          tab={
            <span className="flex items-center px-2">
              <ControlOutlined className="mr-2" />
              参数管理
            </span>
          }
          key="parameter"
        >
          <ParameterManagementTab namespaceId={namespaceId} />
        </TabPane>
        <TabPane
          tab={
            <span className="flex items-center px-2">
              <FilterOutlined className="mr-2" />
              条件管理
            </span>
          }
          key="condition"
        >
          <ConditionManagementTab namespaceId={namespaceId} />
        </TabPane>
        <TabPane
          tab={
            <span className="flex items-center px-2">
              <FileOutlined className="mr-2" />
              变更管理
            </span>
          }
          key="release-order"
        >
          <ReleaseOrderManagementTab namespaceId={namespaceId} />
        </TabPane>
        <TabPane
          tab={
            <span className="flex items-center px-2">
              <ClockCircleOutlined className="mr-2" />
              版本记录
            </span>
          }
          key="version-history"
        >
          <VersionHistoryTab namespaceId={namespaceId} />
        </TabPane>
      </Tabs>
    </PageContainer>
  );
}

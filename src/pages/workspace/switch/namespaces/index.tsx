'use client';

import { useEffect, useState } from 'react';
import { Button, Input, message, Select, Tabs } from 'antd';
import { SearchOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';
import { NamespaceList, SwitchNamespaceEditor } from '@/components';
import { useSearchParams } from 'ice';
import { PageContainer } from '@ant-design/pro-layout';
import services from '@/services/orange-be';
import { SwitchNamespace } from '@/types/namespace';
import { getCurrentTBAppKeys, getFirstTBAppKey, getTBAppName } from '@/utils/common';

const { NamespaceController } = services;

const { Option } = Select;

export default function NamespaceManagement() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState('my');
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingNamespace, setEditingNamespace] = useState<SwitchNamespace | null>(null);
  const [loading, setLoading] = useState(false);
  const [namespaces, setNamespaces] = useState<SwitchNamespace[]>([]);
  const [total, setTotal] = useState(0);
  const [searchName, setSearchName] = useState('');

  // 获取当前环境的 appKey 映射
  const [selectedApp, setSelectedApp] = useState(searchParams.get('appKey') || getFirstTBAppKey());

  const fetchNamespaces = async () => {
    try {
      setLoading(true);
      const result = await NamespaceController.query({
        appKey: selectedApp,
        keyword: searchName,
        hasPermission: activeTab === 'my',
        page: currentPage,
        size: pageSize,
      });
      setNamespaces(result.data);
      setTotal(result.total);
    } catch (error) {
      message.error(error.errorMsg || error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNamespaces();
  }, [currentPage, pageSize, activeTab, selectedApp]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchNamespaces();
  };

  const showCreateModal = () => {
    setModalTitle('创建命名空间');
    setEditingNamespace(null);
    setModalVisible(true);
  };

  const showEditModal = (namespace: SwitchNamespace) => {
    setModalTitle('编辑命名空间');
    setEditingNamespace(namespace);
    setModalVisible(true);
  };

  const handleModalSubmit = async (values: any) => {
    try {
      if (editingNamespace) {
        await NamespaceController.update(
          {
            namespaceId: editingNamespace.namespaceId,
          },
          {
            owners: values.owners,
            testers: values.testers,
            description: values.description,
          },
        );
        message.success('命名空间更新成功');
      } else {
        await NamespaceController.create({
          appKey: selectedApp,
          bizType: values.bizType,
          bizId: values.bizId,
          owners: values.owners,
          testers: values.testers,
          name: values.name,
          description: values.description,
        });
        message.success('命名空间创建成功');
      }

      setModalVisible(false);
      fetchNamespaces();
    } catch (error) {
      message.error(`操作失败: ${error.errorMsg || error.message}`);
    }
  };

  const handleAppChange = (appKey: string) => {
    setSelectedApp(appKey);
    setSearchParams({ appKey });
  };

  return (
    <PageContainer
      header={{
        ghost: true,
        title: '命名空间',
        extra: [
          <div className="flex items-center">
            <span>选择应用：</span>
            <Select value={selectedApp} onChange={handleAppChange} style={{ width: 240 }}>
              {getCurrentTBAppKeys().map(appKey => (
                <Option key={appKey} value={appKey}>
                  {getTBAppName(appKey)}
                </Option>
              ))}
            </Select>
          </div>,
        ],
      }}
    >
      <div className="flex mb-4">
        <Input.Search
          placeholder="输入命名空间名或描述关键字搜索"
          prefix={<SearchOutlined />}
          className="mr-4 max-w-md"
          value={searchName}
          onChange={e => setSearchName(e.target.value)}
          onSearch={handleSearch}
          enterButton
        />
        <div className={'flex-1'} />
        <Button type="primary" onClick={showCreateModal}>
          创建命名空间
        </Button>
      </div>

      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane
          tab={
            <span className="flex items-center">
              <UserOutlined />
              <span>我的命名空间</span>
            </span>
          }
          key="my"
        />
        <Tabs.TabPane
          tab={
            <span className="flex items-center">
              <TeamOutlined />
              <span>全部命名空间</span>
            </span>
          }
          key="all"
        />
      </Tabs>

      <NamespaceList
        loading={loading}
        namespaces={namespaces as any[]}
        total={total}
        currentPage={currentPage}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onEdit={showEditModal}
        nameUrlBuilder={item => `/workspace/switch/namespaces/${item.namespaceId}`}
      />

      <SwitchNamespaceEditor
        visible={modalVisible}
        title={modalTitle}
        namespace={editingNamespace}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleModalSubmit}
        appKey={selectedApp}
      />
    </PageContainer>
  );
}

'use client';

import { Layout, Tabs } from 'antd';
import { useParams } from '@ice/runtime/router';
import {
  ReleaseOrderChangeView,
  ReleaseOrderDetailLayout,
  ReleaseOrderOperationTimeline,
} from '@/components';
import { useState } from 'react';
import './index.css';

export default function ReleaseOrder() {
  const { releaseVersion } = useParams();
  const [activeTab, setActiveTab] = useState('detail');

  const renderContent = () => {
    switch (activeTab) {
      case 'detail':
        return <ReleaseOrderDetailLayout releaseVersion={releaseVersion!} />;
      case 'diff':
        return <ReleaseOrderChangeView releaseVersion={releaseVersion!} />;
      case 'operations':
        return <ReleaseOrderOperationTimeline releaseVersion={releaseVersion!} />;
      default:
        return null;
    }
  };

  return (
    <Layout className="min-h-screen">
      <Tabs
        defaultActiveKey="detail"
        onChange={key => setActiveTab(key)}
        className="bg-gray-50 rounded-md px-6 pt-4 h-full release-order-tabs"
      >
        <Tabs.TabPane tab="版本详情" key="detail" />
        <Tabs.TabPane tab="变更内容" key="diff" />
        <Tabs.TabPane tab="操作记录" key="operations" />
      </Tabs>
      {renderContent()}
    </Layout>
  );
}

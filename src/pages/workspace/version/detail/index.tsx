'use client';

import { <PERSON><PERSON>, <PERSON>, Col, Row, Space } from 'antd';
import {
  BasicInfo,
  ConfigContent,
  DeployProgress as DeploymentProgress,
  MainProgress,
  OperationLog,
  QRCodeDisplay,
  StrategyList,
} from '@/components';
import { FINISHED_VERSION_STATUS_LIST } from '@/constants/version';
import { PageContainer } from '@ant-design/pro-layout';
import { useEffect } from 'react';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';
import { useSearchParams } from 'ice';
import { DownloadOutlined } from '@ant-design/icons';

export default function DeploymentPage() {
  const versionDetail = useVersionStore();
  const fetchAndUpdateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const fetchAndUpdateVersionDeviceCnt = useVersionStore(state => state.fetchAndUpdateVersionDeviceCnt);
  const [searchParams] = useSearchParams();
  const namespaceId = searchParams.get('namespaceId');
  const version = searchParams.get('version');

  useEffect(() => {
    if (namespaceId && version) {
      fetchAndUpdateVersion(namespaceId, version);
      fetchAndUpdateVersionDeviceCnt(namespaceId, version);
    }
  }, [fetchAndUpdateVersion, fetchAndUpdateVersionDeviceCnt, namespaceId, version]);

  if (!versionDetail || Object.keys(versionDetail).length === 0 || !versionDetail.versionBO) {
    return null;
  }

  const { versionBO } = versionDetail as VersionDetail;

  const isFinished = versionBO.status && FINISHED_VERSION_STATUS_LIST.includes(versionBO.status);

  return (
    <PageContainer
      title={
        <Space>
          {versionBO.version}
          <Button
            type="link"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => window.open(`https://dorangesource.alicdn.com/${versionBO.resourceId}`)}
          />
        </Space>
      }
      header={{ ghost: true }}
    >
      <Card className="mb-6">
        <MainProgress />
        {!isFinished && <DeploymentProgress />}
      </Card>

      <BasicInfo />

      <Row gutter={24} className={'mb-6'}>
        <Col span={18}>
          <ConfigContent />
        </Col>
        <Col span={6}>
          <QRCodeDisplay />
        </Col>
      </Row>

      <StrategyList />
      <OperationLog />
    </PageContainer>
  );
}

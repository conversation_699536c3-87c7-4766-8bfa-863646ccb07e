import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>r, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Select, Space, Switch, Tag, Tooltip, Typography } from 'antd';
import { CheckCircleFilled, CloseCircleFilled, DownloadOutlined } from '@ant-design/icons';
import moment from 'moment';
import { queryUserByEmpIds } from '@/services/service';
import { EmployeeList, VersionStatusTag } from '@/components';
import { defineDataLoader } from '@ice/runtime/data-loader';
import { queryAppList } from '@/services/app';
import { Link, useData, useSearchParams } from '@ice/runtime';
import { queryVersionList } from '@/services/version';
import { LOAD_LEVEL_CONFIG } from '@/constants/namespace';
import { UserMap } from '@/types/user';
import { ActionType } from '@ant-design/pro-table/lib';
import { ProFormInstance } from '@ant-design/pro-form/lib';
import { SOURCE_MAP, VERSION_STATUS_CONFIG } from '@/constants/version';
import { isSubstring } from '@/lib/utils';
import './index.css';
import { useUserStore } from '@/store';

const { Option } = Select;
const { Text } = Typography;

interface VersionData {
  version: string;
  name: string;
  appKey: string;
  namespaceId: string;
  creator: string;
  reviewer: string;
  gmtCreate: number;
  gmtCreateTime: string;
  gmtPublish: number;
  gmtPublishTime: string;
  status: number;
  source: number;
  isAvailable: string;
  loadLevel: number;
  valid: boolean;
  strategy: string;
  resourceId: string;
}

export default function Component() {
  const [appList] = useData();
  const currentUserEmpId = useUserStore(state => state.currentUser.empId);

  const appMap = appList.reduce((map, item) => {
    map[item.appKey] = item;
    return map;
  }, {});

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [users, setUsers] = useState<UserMap>({});
  const [createdByMe, setCreatedByMe] = useState(false);
  const [isValid, setIsValid] = useState(false);
  // const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const initialParams = {
    status: searchParams.get('status') || '',
    source: searchParams.get('source') || '',
    appKey: searchParams.get('appKey') || '',
    name: searchParams.get('name') || '',
    namespaceId: searchParams.get('namespaceId') || '',
    creator: searchParams.get('creator') || '',
    createdByMe: searchParams.get('createdByMe') === 'true',
    online: searchParams.get('online') === 'true',
    dateRange:
      searchParams.get('gps') && searchParams.get('gpe')
        ? [moment(Number(searchParams.get('gps'))), moment(Number(searchParams.get('gpe')))]
        : undefined,
  };

  useEffect(() => {
    setCreatedByMe(initialParams.createdByMe);
    setIsValid(initialParams.online);
  }, []);

  const columns: ProColumns<VersionData>[] = [
    {
      title: 'Version',
      dataIndex: 'version',
      search: false,
      render: (text, record) => (
        <Space>
          <Text copyable className="font-medium">
            {text}
          </Text>
          <Button
            type="link"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => window.open(`https://dorangesource.alicdn.com/${record.resourceId}`)}
          />
        </Space>
      ),
    },
    {
      title: '策略',
      dataIndex: 'strategy',
      search: false,
      render: text => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Namespace Name',
      dataIndex: 'name',
      render: text => (
        <Tooltip title={text}>
          <div className="max-w-[200px] truncate">{text}</div>
        </Tooltip>
      ),
      fieldProps: {
        placeholder: '输入 Namespace Name 精确搜索',
      },
    },
    {
      title: 'Namespace ID',
      dataIndex: 'namespaceId',
      hideInTable: true,
      fieldProps: {
        placeholder: '输入 Namespace ID 精确搜索',
      },
    },
    {
      title: '客户端',
      dataIndex: 'appKey',
      render: (_, record) => (
        <Space direction={'vertical'}>
          <Text>{record.appKey}</Text>
          <Tag color="purple">{appMap[record.appKey]?.appName}</Tag>
        </Space>
      ),
      renderFormItem: (_, { type }) => {
        if (type === 'form') {
          return null;
        }
        return (
          <Select
            showSearch
            style={{ width: '100%' }}
            placeholder="搜索客户端"
            optionFilterProp="children"
            allowClear
            filterOption={(input, option) => {
              return isSubstring(input, option?.label);
            }}
          >
            {appList.map(app => (
              <Option key={app.appKey} value={app.appKey} label={`${app.appKey}-${app.appName}`}>
                {`${app.appKey}-${app.appName}`}
              </Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      valueType: 'select',
      valueEnum: {
        0: { text: '平台' },
        1: { text: '预案' },
        2: { text: 'API' },
      },
      render: (_, record) => (
        <Tag color={['orange', 'blue', 'green'][record.source]}>{SOURCE_MAP[record.source]}</Tag>
      ),
    },
    {
      title: '加载级别',
      dataIndex: 'loadLevel',
      search: false,
      render: (_, record) => {
        const config = LOAD_LEVEL_CONFIG[record.loadLevel] || {};
        return (
          <Tooltip title={config.desc}>
            <Tag color={config.color}>{config.label}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '创建&发布',
      dataIndex: 'createAndPublish',
      search: false,
      render: (_, record) => {
        return (
          <Space direction="vertical">
            <span>
              创建: {record.gmtCreateTime}{' '}
              <EmployeeList empIds={[record.creator]} empInfoMap={users} />
            </span>
            <span>发布: {record.gmtPublishTime}</span>
          </Space>
        );
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      hideInTable: true,
      fieldProps: {
        placeholder: '输入工号精确搜索',
        disabled: createdByMe,
      },
    },
    {
      title: '发布时间',
      dataIndex: 'dateRange',
      valueType: 'dateTimeRange',
      hideInTable: true,
      search: {
        transform: value => ({
          gps: value[0],
          gpe: value[1],
        }),
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: VERSION_STATUS_CONFIG,
      render: (_, record) => <VersionStatusTag status={record.status} />,
    },
    {
      title: '生效',
      dataIndex: 'valid',
      valueType: 'select',
      search: false,
      render: (_, record) =>
        (record.valid ? (
          <CheckCircleFilled className="text-green-500" />
        ) : (
          <CloseCircleFilled className="text-gray-500" />
        )),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => (
        <Space direction="vertical">
          <Link
            className={'text-blue-500 hover:text-blue-700'}
            to={`/workspace/version/detail?namespaceId=${record.namespaceId}&version=${record.version}`}
          >
            发布单详情
          </Link>
          <Link
            className={'text-blue-500 hover:text-blue-700'}
            to={`/workspace/namespace/detail?namespaceId=${record.namespaceId}`}
          >
            配置详情
          </Link>
        </Space>
      ),
    },
  ];

  const handleFilterChange = (createdByMeValue: boolean, isValidValue: boolean) => {
    setCreatedByMe(createdByMeValue);
    setIsValid(isValidValue);
    if (actionRef.current) {
      actionRef.current.reload();
    }
  };

  return (
    <PageContainer
      className="version-list"
      header={{
        ghost: true,
        title: '发布列表',
      }}
    >
      <ProTable<VersionData>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        cardBordered
        request={async params => {
          const apiParams = {
            pageSize: params.pageSize?.toString() || '10',
            pageNo: params.current?.toString() || '1',
            name: params.name || '',
            namespaceId: params.namespaceId || '',
            source: params.source?.toString() || '',
            online: isValid ? 'true' : '',
            creator: createdByMe ? currentUserEmpId : params.creator || '',
            status: params.status?.toString() || '',
            appKey: params.appKey || '',
            ...(params.gps && params.gpe
              ? {
                  gps: moment(params.gps).format('YYYY-MM-DD HH:mm:ss'),
                  gpe: moment(params.gpe).format('YYYY-MM-DD HH:mm:ss'),
                }
              : {}),
          };

          setSearchParams({
            ...apiParams,
            createdByMe: createdByMe.toString(),
          });

          const data = await queryVersionList(apiParams);
          let empIds: string[] = [];
          data.content.forEach((item: VersionData) => {
            empIds = empIds.concat([item.creator, item.reviewer]);
          });
          if (empIds.length > 0) {
            const userMap = await queryUserByEmpIds({ empIds: Array.from(empIds) });
            setUsers(userMap);
          }

          return {
            data: data.content,
            success: true,
            total: data.total,
          };
        }}
        rowKey="version"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
        }}
        form={{
          initialValues: initialParams,
          syncToUrl: false,
        }}
        onReset={() => {
          if (formRef.current) {
            formRef.current.resetFields();
            setCreatedByMe(false);
            setIsValid(false);
            setSearchParams({});
            // navigate('?', { replace: true });
          }
        }}
        pagination={{
          pageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Space key="filters">
            <Switch
              checkedChildren="有效"
              unCheckedChildren="有效"
              checked={isValid}
              onChange={checked => handleFilterChange(createdByMe, checked)}
            />
            <Switch
              checkedChildren="我创建的"
              unCheckedChildren="我创建的"
              checked={createdByMe}
              onChange={checked => {
                formRef.current?.setFieldsValue({ creator: checked ? currentUserEmpId : '' });
                handleFilterChange(checked, isValid);
              }}
            />
          </Space>,
        ]}
      />
    </PageContainer>
  );
}

export const dataLoader = defineDataLoader([async () => queryAppList()]);

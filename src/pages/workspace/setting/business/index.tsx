import { useRef, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { But<PERSON>, Tooltip } from 'antd';
import { EmployeeList } from '@/components';
import { UserMap } from '@/types/user';
import { Business } from '@/types/service';
import { queryBusinessList, queryUserByEmpIds } from '@/services/service';
import { QuestionCircleFilled } from '@ant-design/icons';
import './index.css';
import { useSearchParams } from '@ice/runtime';

export default function SearchPage() {
  const [userInfoMap, setUserInfoMap] = useState<UserMap>({});
  const [searchParams, setSearchParams] = useSearchParams();
  const formRef = useRef<ProFormInstance>();

  const columns: ProColumns<Business>[] = [
    {
      title: 'ID',
      search: false,
      dataIndex: 'id',
      width: 80,
    },
    {
      title: (_, type) => {
        if (type === 'table') {
          return '名称';
        }
        return null;
      },
      fieldProps: {
        placeholder: '请输入名称(精确匹配)',
      },
      copyable: true,
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '创建者',
      search: false,
      dataIndex: 'creator',
      width: 120,
      render: (creator: string) => <EmployeeList empIds={[creator]} empInfoMap={userInfoMap} />,
    },
    {
      title: '管理员',
      search: false,
      dataIndex: 'owners',
      width: 120,
      render: (owners: string) => (
        <EmployeeList empIds={owners?.split(',')} empInfoMap={userInfoMap} />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      search: false,
      width: 180,
      render: text => new Date(text as number).toLocaleString(),
    },
    {
      title: '状态',
      search: false,
      dataIndex: 'status',
      width: 100,
      render: status => (status === 0 ? '正常' : '申请中'),
    },
    {
      title: '操作',
      search: false,
      width: 80,
      render: (_, record) => (
        <Button
          type={'primary'}
          size={'small'}
          onClick={() => {
            window.open(`/#/setting/business/detail/${record.name}`, '_self');
          }}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        ghost: true,
        title: 'Business 列表',
        extra: [
          <Tooltip title="API 接入文档">
            <QuestionCircleFilled
              key="QuestionCircleFilled"
              onClick={() => {
                window.open(
                  'https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREcZ7rz9LjVZd1wyK0',
                );
              }}
            />
          </Tooltip>,
          <Button
            type="primary"
            disabled
            onClick={() => {
              window.open('/#/setting/app/detail/DEFAULT', '_blank');
            }}
          >
            API 业务方注册
          </Button>,
        ],
      }}
    >
      <ProTable<Business>
        className="business-list"
        formRef={formRef}
        columns={columns}
        request={async params => {
          const { content, total } = await queryBusinessList({
            name: params.name,
            pageNo: params.current || 1,
            pageSize: params.pageSize || 10,
          });

          setSearchParams({ name: params.name });
          const empIds = content.reduce((acc: string[], item: Business) => {
            return acc.concat(item.owners.split(',')).concat(item.creator);
          }, []);
          setUserInfoMap(await queryUserByEmpIds({ empIds }));

          return {
            data: content,
            success: true,
            total: total,
          };
        }}
        rowKey="id"
        form={{
          initialValues: {
            name: searchParams.get('name') || '',
          },
          syncToUrl: false,
        }}
        onReset={() => {
          formRef.current?.resetFields();
          setSearchParams({});
        }}
        pagination={{
          pageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
        }}
        toolBarRender={false}
      />
    </PageContainer>
  );
}

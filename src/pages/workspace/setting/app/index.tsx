'use client';

import { useEffect, useState } from 'react';
import { Button, Input, Table } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { defineDataLoader, Link } from '@ice/runtime';
import { useData } from '@ice/runtime/router';
import { queryAppList } from '@/services/app';
import { PageContainer } from '@ant-design/pro-layout';
import { getNamespaceListForMe } from '@/services/namespace';
import { debounce } from 'lodash';

interface AppData {
  appKey: string;
  appName: string;
  motuAppId: string;
  applicationId: string;
}


export default function Component() {
  const [appList, myAppList] = useData();
  const commonSearchAppNames = (myAppList || []).map(i => i.appName).slice(0, 3);

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState<AppData[]>([]);

  useEffect(() => {
    filterData();
  }, [searchText]);

  const filterData = () => {
    const filtered = appList.filter(item =>
      Object.values(item).some(value =>
        value.toString().toLowerCase().includes(searchText.toLowerCase()),
      ),
    );
    setFilteredData(filtered);
  };

  const columns = [
    {
      title: 'AppKey',
      dataIndex: 'appKey',
      key: 'appKey',
    },
    {
      title: 'AppName',
      dataIndex: 'appName',
      key: 'appName',
    },
    {
      title: 'MotuAppID',
      dataIndex: 'motuAppId',
      key: 'motuAppId',
    },
    {
      title: '应用中心 ID',
      dataIndex: 'appId',
      key: 'appId',
      render: (text, record) => (<a
        href={`https://mtl4.alibaba-inc.com/#/appKey/${text}/detail/overview`}
        className="text-blue-500 hover:text-blue-700"
        target="_blank"
      >{text}</a>),
    },
    {
      title: '操作',
      key: 'actions',
      render: (text, record) => (
        <div className="space-x-2 text-blue-500">
          <Link to={`/workspace/namespace/list?appKey=${record.appKey}`}>配置列表</Link>
          <span>|</span>
          <Link to={`/workspace/version/list?appKey=${record.appKey}`}>版本列表</Link>
          <span>|</span>
          <a
            target={'_blank'}
            href={`/#/report/dashboard/dashboard?appKey=${record.appKey}`}
            className="hover:text-blue-700"
          >整体大盘</a>
          <span>|</span>
          <a
            target={'_blank'}
            href={`https://fbi.alibaba-inc.com/dashboard/view/page.htm?id=1066235&appKey=${record.appKey}`}
            className="hover:text-blue-700"
          >云上报表</a>
          <span>|</span>
          <a href={`/#/setting/app/detail/${record.appKey}`} className="hover:text-blue-700">设置管理</a>
        </div>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        ghost: true,
        title: 'APP 列表',
        extra: [
          <Button
            type="primary"
            onClick={() => {
              window.open('/#/setting/app/detail/DEFAULT', '_blank');
            }}
          >查看全局配置</Button>,
        ],
      }}
    >
      <div className="mb-6 flex justify-between items-center">
        <Input
          placeholder="可输入关键字搜索"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          className="max-w-md"
        />
        <div className="flex items-center ml-4">
          <span className="text-gray-500 mr-1">常用搜索：</span>
          {commonSearchAppNames.map((term) => (
            <Button
              key={term}
              type="link"
              className="px-2 py-0"
              onClick={() => setSearchText(term)}
            >
              {term}
            </Button>
          ))}
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="appKey"
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          showTotal: () => `共 ${filteredData.length} 条数据`,
        }}
        className="border rounded-lg"
      />
    </PageContainer>
  );
}

export const dataLoader = defineDataLoader([
  async () => queryAppList(),
  async () => {
    const list = await getNamespaceListForMe();
    return list.map(item => item.app);
  },
]);

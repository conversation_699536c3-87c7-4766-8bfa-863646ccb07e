import AES from '@ali/aes-tracker';
import '@ali/aes-tracker-plugin-api';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import '@ali/aes-tracker-plugin-autolog';
import AESPluginAutolog from '@ali/aes-tracker-plugin-autolog';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import AESPluginEventTiming from '@ali/aes-tracker-plugin-eventTiming';
import AESPluginJSError from '@ali/aes-tracker-plugin-jserror';
import AESPluginPerf from '@ali/aes-tracker-plugin-perf';
import AESPluginPV from '@ali/aes-tracker-plugin-pv';
import AESPluginResourceError from '@ali/aes-tracker-plugin-resourceError';
import { defineAppConfig, defineDataLoader } from 'ice';

export interface Window {
  AES_CONFIG?: {
    pid: string;
    user_type: string;
    uid?: string;
    username?: string;
    page_id: string;
    env: string;
    dim1: string;
    plugin_autolog: {
      click: string;
    };
  };
  sendEvent?: any;
  xf?: {
    init: (id: number, config: any) => void;
    setUser: (user: any) => void;
  };
  _DATA_?: {
    userId?: string;
    userName?: string;
    isAdmin?: boolean;
  };
}

declare const window: Window & typeof globalThis;

// App config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/app
export default defineAppConfig(() => ({
  router: {
    type: 'hash',
  },
}));

export const dataLoader = defineDataLoader(async () => {
  const userInfo = getUserInfo();

  let env = 'prod';
  if (window.location.host.includes('-pre')) {
    env = 'pre';
  } else if (window.location.host.includes('-daily')) {
    env = 'daily';
  }

  window.AES_CONFIG = {
    pid: 'orange-console',
    user_type: '14',
    uid: userInfo?.userid,
    username: userInfo?.name,
    page_id: window.location.hash,
    env,
    dim1: 'v5',
    plugin_autolog: {
      click: 'all',
    },
  };
  // 在部分环境中，三方埋点可能会对非 Element 节点调用 getComputedStyle 导致报错
  // 这里为埋点初始化添加防御，同时仅在生产环境启用 Autolog 插件
  try {
    const aes = new AES();
    aes.use(AESPluginPV, {
      // 开启history router的监听
      enableHistory: true,
      // 设置page_id的逻辑规则
      getPageId: url => {
        return window.location.pathname + window.location.hash; // 新的 page_id
      },
    });

    const basePlugins = [
      AESPluginEvent,
      AESPluginJSError,
      AESPluginAPI,
      AESPluginResourceError,
      AESPluginPerf,
      AESPluginEventTiming,
    ];

    // 仅在生产环境启用 Autolog，避免在预发/日常环境因第三方库不兼容导致的运行时错误
    if (env === 'prod') {
      basePlugins.push(AESPluginAutolog);
    }

    aes.use(basePlugins);
    window.sendEvent = aes.use(AESPluginEvent);
  } catch (err) {
    // 保底兜底，避免埋点问题影响业务可用性
    // eslint-disable-next-line no-console
    console.warn('AES init failed:', err);
  }

  window.xf &&
    window.xf.init(1138, {
      allowScreenRecording: false,
      primaryColor: '#ff6a00',
      iconDraggable: true,
      iconVisible: false,
      dialogTitle: '问题&建议反馈',
      iconPosition: 'bottom right',
    });
  window.xf &&
    window.xf.setUser({
      id: userInfo?.userid,
      name: userInfo?.name,
      type: 14,
    });

  return {
    userInfo,
  };
});

function getUserInfo() {
  if (window._DATA_?.userId) {
    const info = window._DATA_;
    return {
      name: info.userName,
      avatar: `https://work.alibaba-inc.com/photo/${info.userId}.120x120.jpg`,
      userid: info.userId,
      userType: info.isAdmin ? 'admin' : 'user',
      empId: info.userId,
    };
  }
  return null;
}

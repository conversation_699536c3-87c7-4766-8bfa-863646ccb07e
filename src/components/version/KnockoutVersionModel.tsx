import React from 'react';
import { Space, Table, Tooltip, Typography } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import { KnockoutVersion } from '@/types/version';

const { Title, Text } = Typography;

interface VersionData {
  key: string;
  version: string;
  AppVersion: string;
  Strategy: string;
  releaseTime: string;
  publisher: string;
}

const columns: ColumnsType<VersionData> = [
  {
    title: 'version',
    dataIndex: 'version',
    key: 'version',
    render: text => (
      <Tooltip title="Version identifier">
        <Text copyable>{text}</Text>
      </Tooltip>
    ),
  },
  {
    title: 'AppVersion',
    dataIndex: 'appVersion',
    key: 'appVersion',
  },
  {
    title: 'Strategy',
    dataIndex: 'strategy',
    key: 'strategy',
  },
  {
    title: '发布时间',
    dataIndex: 'gmtPublish',
    key: 'gmtPublish',
    render: text => {
      return moment(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '发布人',
    dataIndex: 'creator',
    key: 'creator',
  },
];

export default function KnockoutVersionModel(props: { knockoutVersions: KnockoutVersion[] }) {
  const { knockoutVersions } = props;
  return (
    <Space direction="vertical" size="large" className="w-full">
      {knockoutVersions.map(knockoutVersion => (
        <div>
          <Title level={5} className="text-red-500 flex items-center">
            <InfoCircleOutlined className="mr-2" />
            {`删除原因：${knockoutVersion.reason}`}
          </Title>
          <Table
            columns={columns as any}
            dataSource={knockoutVersion.targetList}
            pagination={false}
            className="shadow-md"
            rowClassName={() => 'hover:bg-gray-50 transition-colors'}
          />
        </div>
      ))}
    </Space>
  );
}

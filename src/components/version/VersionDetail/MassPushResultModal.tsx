'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Modal, Spin, Button, Form, Typography } from 'antd';
import { getMassPushResult } from '@/services/version';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';

const { Item: FormItem } = Form;
const { Text } = Typography;

interface MassPushResultModalProps {
  visible: boolean;
  onClose: () => void;
  record: any;
}

const MassPushResultModal: React.FC<MassPushResultModalProps> = ({
                                                                   visible,
                                                                   onClose,
                                                                   record,
                                                                 }) => {
  if (!record) return null;

  const versionDetail = useVersionStore();
  const [content, setContent] = useState<any>();

  const { versionBO } = versionDetail as VersionDetail;
  const resultDO = record.resultDO || {};
  const taskId = resultDO.massTaskId || '';
  const paramDO = record.paramDO || {};
  const deviceIds = paramDO.utdids || [];

  useEffect(() => {
    (async () => {
      const result = await getMassPushResult(versionBO.namespaceId, versionBO.version, record.id, taskId);
      setContent(result);
    })();
  }, [record.id]);

  return (
    <>
      <Modal
        title="BETA 灰度"
        open={visible}
        onCancel={onClose}
        width={600}
        footer={null}
      >
        <Form layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <FormItem label="推送 UTDID">
            {deviceIds.length > 0 ? deviceIds.join(', ') : '-'}
          </FormItem>
          {content && (
            <>
              <FormItem label="任务 ID">{taskId || '-'}</FormItem>
              <FormItem label="任务状态">{content.status || '-'}</FormItem>
              <FormItem label="收到 ACK 数">
                <Text>{content.ackNum === undefined || content.ackNum === null ? '-' : content.ackNum}</Text>
                <Text
                  type="secondary"
                  style={{
                    display: 'block',
                    fontSize: 13,
                  }}
                >
                  此为 ACCS 的 ACK，不代表 Orange 配置更新
                </Text>
              </FormItem>
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default MassPushResultModal;


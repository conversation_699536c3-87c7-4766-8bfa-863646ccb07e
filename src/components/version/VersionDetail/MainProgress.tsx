'use client';

import { Steps, Tooltip } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import {
  FINISHED_VERSION_STATUS_LIST,
  VERSION_STATUS_CANCEL,
  VERSION_STATUS_CREATED,
  VERSION_STATUS_DELETE,
  VERSION_STATUS_SUCCESS,
} from '@/constants/version';
import { RECORD_STATUS_SUCCESS } from '@/constants/record';
import { EmployeeList } from '@/components/user';
import { VersionStatusTag } from '@/components';
import { getWmccUrl } from '@/utils/link';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';

function MainProgress() {
  const versionDetail = useVersionStore();
  const { versionBO, wmccView, userMap } = versionDetail as VersionDetail;
  const getCurrentStep = function () {
    if (wmccView) {
      if (wmccView.message == '成功' || wmccView.message == '已生效') {
        return 3;
      }
      if (wmccView.id) {
        return 2;
      }
    }

    if (versionBO) {
      switch (versionBO.status) {
        case VERSION_STATUS_CREATED:
          return 0;
        case VERSION_STATUS_DELETE:
        case VERSION_STATUS_CANCEL:
        case VERSION_STATUS_SUCCESS:
          return 2;
        default:
          return 1;
      }
    }
    return 1;
  };

  const isFinished = versionBO.status && FINISHED_VERSION_STATUS_LIST.includes(versionBO.status);

  const _showStepStart = function () {
    return (
      <div className={'text-nowrap'}>
        <div>{(versionBO && versionBO.gmtCreateTime) || ''}</div>
        <div>
          {' '}
          {versionBO && versionBO.creator ? (
            <span>
              创建人：
              <EmployeeList empIds={[versionBO.creator]} empInfoMap={userMap} />
            </span>
          ) : null}
        </div>
      </div>
    );
  };

  const _renderTips = function (tip: string, open = false) {
    return (
      <Tooltip title={tip} open={open} className="help">
        <ExclamationCircleOutlined />
      </Tooltip>
    );
  };

  const _showStepVersion = function () {
    return (
      <div className={'text-nowrap'}>
        <div>
          {versionBO && versionBO.status == RECORD_STATUS_SUCCESS ? versionBO.gmtPublishTime : ''}
        </div>
        <div>
          {/* 一些 versionBO.reviewer 为 undefined 脏数据的兼容 */}
          {versionBO.reviewer && versionBO.reviewer !== 'undefined' && (
            <span>
              审核人：
              <EmployeeList empIds={versionBO.reviewer.split(',')} empInfoMap={userMap} />
            </span>
          )}
          <VersionStatusTag status={versionBO.status} />
        </div>
      </div>
    );
  };

  const _showStepProbe = function () {
    return (
      <div>
        <>
          {wmccView && wmccView.message}{' '}
          {currentStep == 2 &&
            _renderTips('探针按任务排队发布，阿里集群生产环境每次任务约 6.5 分钟', true)}
        </>
        <div>
          {wmccView.id ? (
            <a
              className={'text-blue-500 hover:text-blue-700'}
              target="_blank"
              href={getWmccUrl(wmccView.id)}
            >
              wmcc 任务
            </a>
          ) : null}
        </div>
      </div>
    );
  };

  let currentStep = getCurrentStep();
  let items = [
    {
      title: '初始',
      icon: currentStep > 0 ? <CheckCircleOutlined /> : null,
      description: _showStepStart(),
    },
    {
      title: '版本发布',
      icon:
        currentStep > 1 ? <CheckCircleOutlined /> : currentStep === 1 ? <LoadingOutlined /> : null,
      description: _showStepVersion(),
    },
    {
      title: '探针发布',
      icon:
        currentStep > 2 ? <CheckCircleOutlined /> : currentStep === 2 ? <LoadingOutlined /> : null,
      description: _showStepProbe(),
    },
    {
      title: '完成',
      icon: currentStep > 3 || isFinished ? <CheckCircleOutlined /> : null,
    },
  ];

  if (
    versionBO &&
    (versionBO.status === VERSION_STATUS_CANCEL || versionBO.status === VERSION_STATUS_DELETE)
  ) {
    items = items.filter(item => item.title !== '探针发布');
  }

  return (
    <div className="w-full py-6">
      <Steps items={items} current={currentStep} />
    </div>
  );
}

export default MainProgress;

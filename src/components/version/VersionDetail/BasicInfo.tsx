'use client';

import { Card, Descriptions } from 'antd';
import { AppBO } from '@/types/namespace';
import { useEffect, useState } from 'react';
import { getAppDetail } from '@/services/app';
import { LOAD_LEVEL_CONFIG, TYPE_CONFIG } from '@/constants/namespace';
import { EMERGENT_MAP, SOURCE_MAP } from '@/constants/version';
import { EmployeeList } from '@/components/user';
import { Link } from '@ice/runtime';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';

export default function BasicInfo() {
  const versionDetail = useVersionStore();
  const { namespaceBO, versionBO, userMap } = versionDetail as VersionDetail;
  const [appDetail, setAppDetail] = useState<AppBO>();

  useEffect(() => {
    (async () => {
      setAppDetail((await getAppDetail(namespaceBO.appKeyOrGroup)).appBO);
    })();
  }, []);

  return (
    <Card className="mb-6">
      <Descriptions bordered column={3}>
        <Descriptions.Item label="namespace">
          <Link
            className="text-blue-500 hover:text-blue-700"
            to={`/workspace/namespace/detail?namespaceId=${namespaceBO.namespaceId}`}
          >
            {namespaceBO.name}
          </Link>
        </Descriptions.Item>
        <Descriptions.Item label="appKey">
          {appDetail ? appDetail.appName : ''}({namespaceBO.appKeyOrGroup})
        </Descriptions.Item>
        <Descriptions.Item label="策略">{versionBO.strategy}</Descriptions.Item>
        <Descriptions.Item label="生效方式">{EMERGENT_MAP[versionBO.isEmergent]}</Descriptions.Item>
        <Descriptions.Item label="加载级别">
          {LOAD_LEVEL_CONFIG[namespaceBO.loadLevel].label}
        </Descriptions.Item>
        <Descriptions.Item label="类型">{TYPE_CONFIG[namespaceBO.type].label}</Descriptions.Item>
        <Descriptions.Item label="来源">{SOURCE_MAP[versionBO.source]}</Descriptions.Item>
        <Descriptions.Item label="负责人">
          <EmployeeList empIds={namespaceBO.owners?.split(',')} empInfoMap={userMap} />
        </Descriptions.Item>
        <Descriptions.Item label="测试负责人">
          <EmployeeList empIds={namespaceBO.testers?.split(',')} empInfoMap={userMap} />
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
}

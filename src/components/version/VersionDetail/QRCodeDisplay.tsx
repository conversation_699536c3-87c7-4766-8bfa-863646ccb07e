'use client';

import { <PERSON><PERSON>, Card, QRCode, Space } from 'antd';
import { useState } from 'react';
import { useVersionStore } from '@/store';
import { getDebugQrUrl } from '@/utils/link';
import { VersionDetail } from '@/types/version';
import { FINISHED_VERSION_STATUS_LIST } from '@/constants/version';
import { LOAD_LEVEL_CONFIG, TYPE_CONFIG } from '@/constants/namespace';
import snakeCase from 'lodash/snakeCase';

function QRCodeDisplay() {
  const versionDetail = useVersionStore();
  const [isZoomed, setIsZoomed] = useState(false);

  const { versionBO, changeBO, resourceBO } = versionDetail as VersionDetail;

  const { name, appKey, version, status, namespaceId } = versionBO;

  const indexInfo = FINISHED_VERSION_STATUS_LIST.includes(status)
    ? null
    : {
        appVersion: versionBO.appVersion,
        changeVersion: changeBO?.changeVersion,
        highLazy: versionBO.loadLevel === 10 ? 0 : 1,
        loadLevel: snakeCase(LOAD_LEVEL_CONFIG[versionBO.loadLevel].label).toUpperCase(),
        md5: resourceBO.md5,
        name: versionBO.name,
        resourceId: resourceBO.resourceId,
        type: TYPE_CONFIG[versionBO.type].label,
        version: versionBO.version,
      };

  const url = getDebugQrUrl(
    name,
    appKey,
    '*',
    version,
    versionDetail?.indexBO?.appIndexVersion,
    indexInfo,
  );
  console.log('DEBUG 页面地址： ', url);
  const originalWidth = window.innerWidth >= 1420 ? 300 : window.innerWidth > 1000 ? 200 : 150;

  return (
    <Card id="debug-qr" className="h-full w-full border" title="扫码灰度验证">
      <div className="flex flex-col items-center justify-center h-full space-y-4">
        <div onClick={() => setIsZoomed(!isZoomed)} style={{ cursor: 'pointer' }}>
          <QRCode value={url} size={isZoomed ? 350 : originalWidth} />
        </div>
        {/* <Text type="secondary" className="text-sm text-center">点击二维码可以{isZoomed ? '恢复大小' : '放大'}</Text> */}
        <Space direction={'horizontal'}>
          <Button
            type={'link'}
            onClick={() =>
              window.open('https://alidocs.dingtalk.com/i/nodes/N7dx2rn0JbxOaqnACgdPjlZNWMGjLRb3')}
          >
            查看文档
          </Button>
          <Button
            type={'link'}
            onClick={() => window.open(`/#/namespace/version/debug/${namespaceId}`)}
          >
            全量验证走此
          </Button>
        </Space>
      </div>
    </Card>
  );
}

export default QRCodeDisplay;

'use client';

import { <PERSON><PERSON>, Space, Table } from 'antd';
import { RecordItem, VersionDetail } from '@/types/version';
import { EmployeeList } from '@/components/user';
import { AVAILABLEL_MAP, GRAY_RATIO_UNIT, YES } from '@/constants/version';
import {
  CHECK_STATUS_CONFIG,
  RECORD_PARAMS_SHOWS,
  RECORD_RESULT_SHOWS,
  RECORD_STATUS_CONFIG,
  RECORD_STATUS_SUCCESS,
  RECORD_TYPE_APPLY,
  RECORD_TYPE_BETA,
  RECORD_TYPE_CONFIG,
  RECORD_TYPE_GRAY,
  RECORD_TYPE_RATIO_GRAY,
  RECORD_TYPE_SKIP,
} from '@/constants/record';
import { getBpmsUrl, getChangeFreeUrl } from '@/utils/link';
import { useVersionStore } from '@/store';
import { recordOperate } from '@/services/version';
import throttle from 'lodash/throttle';
import { useState } from 'react';
import { MassPushResultModal } from '@/components';

function OperationLog() {
  const versionDetail = useVersionStore();
  const updateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const [pushMassResultModalVisible, setPushMassResultModalVisible] = useState(false);
  const [selectedMassResultRecord, setSelectedMassResultRecord] = useState<RecordItem | null>(null);

  const { recordList, userMap, versionBO } = versionDetail as VersionDetail;

  const onHandleRefresh = throttle(async (recordId: number, type: number) => {
    await recordOperate(versionBO.namespaceId, versionBO.version, recordId, type, 'REFRESH');
    await updateVersion(versionBO.namespaceId, versionBO.version);
  }, 60 * 1000);

  const columns = [
    {
      title: '操作名称',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => RECORD_TYPE_CONFIG[text],
    },
    {
      title: '操作人',
      dataIndex: 'creator',
      key: 'creator',
      render: (text: string) => <EmployeeList empIds={[text]} empInfoMap={userMap} />,
    },
    {
      title: '相关时间',
      dataIndex: 'time',
      key: 'time',
      render: (text: string, record: RecordItem) => {
        return (
          <Space direction={'vertical'}>
            <span>创建：{record.gmtCreateTime}</span>
            <span>更新：{record.gmtModifiedTime}</span>
          </Space>
        );
      },
    },
    {
      title: '生效中',
      dataIndex: 'isAvailable',
      key: 'isAvailable',
      render: (text: string) => AVAILABLEL_MAP[text],
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => RECORD_STATUS_CONFIG[text],
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text, record) => {
        if (!record.ver || record.ver === 1) {
          return null;
        }
        const paramDO = record.paramDO || {};
        const resultDO = record.resultDO || {};

        const changefreeUrl =
          record.type === RECORD_TYPE_APPLY && resultDO.changefreeOrderId
            ? getChangeFreeUrl(resultDO.changefreeOrderId)
            : null;
        const bpmsUrl =
          record.type === RECORD_TYPE_SKIP && record.arg0 ? getBpmsUrl(record.arg0) : null;
        const grayRatio =
          record.type === RECORD_TYPE_RATIO_GRAY
            ? record.paramDO && record.paramDO.grayRatio
            : null;

        return (
          <div>
            {RECORD_PARAMS_SHOWS.map(item => {
              const key = item.value;
              if (paramDO[key]) {
                return (
                  <div key={key}>
                    {item.label}: {paramDO[key]}
                  </div>
                );
              } else {
                return null;
              }
            })}
            {RECORD_RESULT_SHOWS.map(item => {
              const key = item.value;
              if (resultDO[key]) {
                return (
                  <div key={key}>
                    {item.label}: {resultDO[key]}
                  </div>
                );
              } else {
                return null;
              }
            })}
            {changefreeUrl ? (
              <a
                className={'text-blue-500 hover:text-blue-700'}
                href={changefreeUrl}
                target="_blank"
              >
                {' '}
                CF地址
              </a>
            ) : null}
            {bpmsUrl ? (
              <a className={'text-blue-500 hover:text-blue-700'} href={bpmsUrl} target="_blank">
                {' '}
                BPMS地址
              </a>
            ) : null}
            {grayRatio ? `灰度比例: ${(grayRatio * 100) / GRAY_RATIO_UNIT}%` : null}
          </div>
        );
      },
    },
    {
      title: '检测',
      dataIndex: 'check',
      key: 'check',
      render: (text, record) => {
        if (!record.ver || record.ver === 1) {
          return null;
        }
        const checkResultDO = record.checkResultDO || {};
        if (!checkResultDO.checkStatus) {
          return null;
        }
        return (
          <Space direction={'horizontal'}>
            {CHECK_STATUS_CONFIG[checkResultDO.checkStatus]}
            {!checkResultDO.details ? null : (
              <Button
                type="dashed"
                size="small"
                onClick={function () {
                  // fixme
                  // checkHoldShow && checkHoldShow(checkResultDO, record.id);
                }}
              >
                查看
              </Button>
            )}
          </Space>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'op',
      key: 'op',
      render: (text, record) => {
        if (!record.ver || record.ver === 1) {
          return null;
        }
        const paramDO = record.paramDO || {};
        const resultDO = record.resultDO || {};
        const massId = resultDO.massTaskId || '';

        const showApply =
          record.isAvailable === YES &&
          record.type === RECORD_TYPE_APPLY &&
          record.status !== RECORD_STATUS_SUCCESS;
        const showBeta =
          record.isAvailable === YES &&
          record.status === RECORD_STATUS_SUCCESS &&
          record.type === RECORD_TYPE_BETA;
        const showGray = record.isAvailable === YES && record.type === RECORD_TYPE_GRAY;
        const showSkip = record.isAvailable === YES && record.type === RECORD_TYPE_SKIP;
        if (showApply && record.arg0) {
          return (
            <div>
              <Button
                type="dashed"
                size="small"
                onClick={async () => {
                  await onHandleRefresh(record.id, RECORD_TYPE_APPLY);
                }}
              >
                刷新CF
              </Button>
            </div>
          );
        } else if (showBeta) {
          const massResult = (
            <Button
              type="dashed"
              size="small"
              onClick={function () {
                setSelectedMassResultRecord(record);
                setPushMassResultModalVisible(true);
              }}
            >
              详细
            </Button>
          );
          return <div>{paramDO.utdids || massId ? massResult : null}</div>;
        } else if (showGray) {
          const massResult = (
            <Button
              type="dashed"
              size="small"
              onClick={function () {
                // fixme
                // getMassCircleResult && getMassCircleResult(record, massId);
              }}
            >
              任务查看
            </Button>
          );
          if (massId) {
            return <div>{massId ? massResult : ''}</div>;
          }
        } else if (showSkip) {
          if (record.status !== RECORD_STATUS_SUCCESS && record.arg0) {
            return (
              <div>
                <Button
                  type="dashed"
                  size="small"
                  onClick={async () => {
                    return onHandleRefresh(record.id, RECORD_TYPE_SKIP);
                  }}
                >
                  刷新 BPMS
                </Button>
              </div>
            );
          }
        }
        return <div />;
      },
    },
  ];

  return (
    <div className="mb-6">
      <h2 className="text-lg font-medium mb-4">操作记录</h2>
      <Table columns={columns} dataSource={recordList || []} rowKey="id" pagination={false} />
      <MassPushResultModal
        record={selectedMassResultRecord}
        visible={pushMassResultModalVisible}
        onClose={() => setPushMassResultModalVisible(false)}
      />
    </div>
  );
}

export default OperationLog;

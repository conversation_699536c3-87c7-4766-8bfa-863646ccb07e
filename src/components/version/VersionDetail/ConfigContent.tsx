'use client';

import { Card } from 'antd';
import { ScenePropertiesEditor } from '@/components/properties';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';

export default function ConfigContent() {
  const versionDetail = useVersionStore();
  const { resourceBO: resource } = versionDetail as VersionDetail;

  const scenes = [
    {
      name: 'Default',
      content: resource.srcContent,
    },
  ];
  if (resource.scenesContentsMap) {
    for (let scene in resource.scenesContentsMap) {
      scenes.push({
        name: scene,
        content: resource.scenesContentsMap[scene],
      });
    }
  }
  return (
    <Card className={'w-full'} title="变更内容">
      <ScenePropertiesEditor
        originalScenesContents={scenes}
        namespaceType={1}
        editorHeight={'300px'}
        readonly
      />
    </Card>
  );
}

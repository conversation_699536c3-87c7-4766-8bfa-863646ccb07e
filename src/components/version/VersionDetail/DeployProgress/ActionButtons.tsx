import { Button, message, Popconfirm } from 'antd';
import { getEnvName } from '@/utils/env';
import { RecordItem } from '@/types/version';
import { getCheckHoldResult, versionStage } from '@/services/version';
import {
  VERSION_STAGE_CLOSE,
  VERSION_STAGE_PUBLISH,
  VERSION_STAGE_REVIEW,
  VERSION_STAGE_SKIP,
} from '@/constants/version';
import { isSystemAdmin } from '@/utils/permission';
import { useState } from 'react';
import { CHECK_STATUS_PASS, SKIP_PROCESS_CHECK } from '@/constants/record';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';
import CheckHoldResultModal, { CheckHoldResultModalProps } from './CheckHoldResultModal';
import SkipProcessButton from './SkipProcessButton';
import ApplyButton from './ApplyButton';
import BetaButton from './BetaButton';
import <PERSON><PERSON><PERSON>on from './Gray/GrayButton';
import SmallFlowGrayButton from '@/components/version/VersionDetail/DeployProgress/Gray/SmallFlowGrayButton';

interface ActionButtonsProps {
  renderApply: boolean;
  renderApplyTips: string;
  renderGray: boolean;
  renderReview: boolean;
  renderFull: boolean;
  butName: string;
  lastOrSuccessCheckSkipRecord: RecordItem | null;
}

export function ActionButtons({
  renderApply,
  renderApplyTips,
  renderGray,
  renderReview,
  renderFull,
  butName,
  lastOrSuccessCheckSkipRecord,
}: ActionButtonsProps) {
  const versionDetail = useVersionStore();
  const updateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const [checkHoldResultModal, setCheckHoldResultModal] = useState<CheckHoldResultModalProps>({
    visible: false,
  });

  const { versionBO, namespaceBO, viewConfig, hasEditPermission, hasTestPermission } =
    versionDetail as VersionModel;

  const supportSmallGray = !!viewConfig?.smallGray;
  const hasCreateTigaTask = !!versionBO.tigaMetadata;

  const refreshVersionDetail = async () => {
    return updateVersion(versionBO.namespaceId, versionBO.version);
  };

  const showSkip = isSystemAdmin() || (viewConfig && viewConfig.oneStepSkip);

  const onHandleSkipHold = async (problem: string) => {
    try {
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_SKIP, {
        skipStage: SKIP_PROCESS_CHECK,
        problem: problem || '',
      });
      message.success('跳过发布成功');
      await refreshVersionDetail();
    } catch (e) {
      message.error(`跳过发布失败：${e.message}`);
    }
  };

  const checkHold = async (publishStage: string, recordId: number | null, nextProcess) => {
    try {
      const res = await getCheckHoldResult(
        namespaceBO.namespaceId,
        versionBO.version,
        publishStage,
        recordId,
      );
      const { checkStatus } = res;
      if (checkStatus !== CHECK_STATUS_PASS) {
        setCheckHoldResultModal({
          visible: true,
          checkResult: res,
          oneStepSkip: viewConfig.oneStepSkip,
          nextProcess,
          lastOrSuccessCheckSkipRecord,
          onHandleSkip: onHandleSkipHold,
        });
      } else {
        nextProcess && (await nextProcess());
      }
    } catch (error) {
      message.warning('前置检测异常了，您仍可继续操作', error.message);
      console.log('前置检测异常了，您仍可继续操作', error.message);
      nextProcess && (await nextProcess());
    }
  };

  const handlePublishSubmit = async () => {
    await checkHold(VERSION_STAGE_PUBLISH, null, async () => {
      try {
        await versionStage(namespaceBO.namespaceId, versionBO.version, VERSION_STAGE_PUBLISH);
        message.success('发布成功');
        await refreshVersionDetail();
      } catch (error) {
        message.error(`发布失败：${error.message}`);
      }
    });
  };

  return (
    <div className="flex justify-between mt-10">
      <div className="flex items-center gap-2">
        {showSkip && <SkipProcessButton />}
        <Popconfirm
          title="确定要取消发布吗？"
          onConfirm={async () => {
            try {
              await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_CLOSE);
              message.success('取消发布成功');
              await refreshVersionDetail();
            } catch (e) {
              message.error(`取消发布失败：${e.message}`);
            }
          }}
        >
          <Button disabled={!hasEditPermission}>取消发布</Button>
        </Popconfirm>
      </div>

      <div className="flex items-center gap-2">
        <BetaButton type={butName === 'beta' ? 'primary' : 'default'} />

        {renderApply && (
          <ApplyButton
            renderApplyTips={renderApplyTips}
            type={butName === 'apply' ? 'primary' : 'default'}
          />
        )}

        {renderGray && supportSmallGray && (
          <SmallFlowGrayButton
            checkHold={checkHold}
            type={butName === 'smallGray' ? 'primary' : 'default'}
          />
        )}

        {renderGray && !(supportSmallGray && !hasCreateTigaTask) && (
          <GrayButton checkHold={checkHold} type={butName === 'gray' ? 'primary' : 'default'} />
        )}

        {renderReview && (
          <Popconfirm
            title="确定验证通过吗？"
            onConfirm={async () => {
              try {
                await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_REVIEW);
                message.success('验证通过成功');
                await refreshVersionDetail();
              } catch (e) {
                message.error(`验证通过失败：${e.message}`);
              }
            }}
          >
            <Button
              type={butName === 'review' ? 'primary' : 'default'}
              disabled={!hasEditPermission && !hasTestPermission}
            >
              验证通过
            </Button>
          </Popconfirm>
        )}

        {renderFull && (
          <Popconfirm title="确定要全量发布吗？" onConfirm={handlePublishSubmit}>
            <Button
              type={butName === 'publish' ? 'primary' : 'default'}
              disabled={!hasEditPermission}
            >
              {getEnvName()}发布
            </Button>
          </Popconfirm>
        )}
      </div>

      <CheckHoldResultModal {...checkHoldResultModal} />
    </div>
  );
}

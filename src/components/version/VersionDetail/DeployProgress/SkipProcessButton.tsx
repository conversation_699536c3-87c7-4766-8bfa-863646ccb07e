'use client';

import React, { useState } from 'react';
import { Button, Checkbox, Form, Input, message, Modal, Select } from 'antd';
import { SKIP_PROCESS_APPLY, SKIP_PROCESS_ARR, SKIP_PROCESS_CHECK, SKIP_PROCESS_GRAY } from '@/constants/record';
import { EMERGENT_MAP, VERSION_STAGE_SKIP } from '@/constants/version';
import { versionStage } from '@/services/version';
import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';

const { TextArea } = Input;
const { Option } = Select;

const SkipProcessButton = () => {
    const versionDetail = useVersionStore();
    const updateVersion = useVersionStore((state) => state.fetchAndUpdateVersion);
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

    const {
        versionBO,
    } = versionDetail as VersionDetail;

    const showModal = () => {
        setVisible(true);
    };

    const handleOk = async () => {
        try {
            setConfirmLoading(true);
            const values = await form.validateFields();
            let extParams = {
                skipStage: values.skipStage,
                problem: values.problem || '',
                reason: values.reason,
            };

            switch (values.skipStage) {
                case SKIP_PROCESS_APPLY:
                    extParams['emergent'] = values.emergent || '';
                    break;
                case SKIP_PROCESS_GRAY:
                case SKIP_PROCESS_CHECK:
                    extParams['noApproval'] = values.noApproval;
                    break;
                default:
                    break;
            }

            await versionStage(
                versionBO.namespaceId,
                versionBO.version,
                VERSION_STAGE_SKIP,
                extParams,
            );
            message.success('跳过流程成功');
            setVisible(false);
            form.resetFields();
            await updateVersion(versionBO.namespaceId, versionBO.version);
        } catch (error) {
            message.error(error.message);
        } finally {
            setConfirmLoading(false);
        }
    };

    const handleCancel = () => {
        setVisible(false);
        form.resetFields();
    };

    return (
      <>
        <Button onClick={showModal}>跳过流程</Button>
        <Modal
          title="跳过流程"
          open={visible}
          onOk={handleOk}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            name="skipProcessForm"
            initialValues={{ noApproval: false }}
          >
            <Form.Item
              name="skipStage"
              label="跳过的阶段"
              rules={[
                            {
                                required: true,
                                message: '请选择要跳过的阶段',
                            },
                        ]}
            >
              <Select placeholder="选择一个跳过的阶段">
                {SKIP_PROCESS_ARR.map(item => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                            ))}
              </Select>
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => prevValues.skipStage !== currentValues.skipStage}
            >
              {({ getFieldValue }) => {
                            const skipStage = getFieldValue('skipStage');
                            if ([SKIP_PROCESS_GRAY, SKIP_PROCESS_CHECK].includes(skipStage)) {
                                return (
                                  <Form.Item name="noApproval" valuePropName="checked">
                                    <Checkbox>无需审批</Checkbox>
                                  </Form.Item>
                                );
                            }
                            if (skipStage === SKIP_PROCESS_APPLY) {
                                return (
                                  <Form.Item
                                    name="emergent"
                                    label="立即生效"
                                    rules={[
                                            {
                                                required: true,
                                                message: '请选择立即生效选项',
                                            },
                                        ]}
                                  >
                                    <Select placeholder="选择立即生效选项">
                                      {Object.keys(EMERGENT_MAP).map(key => (
                                        <Option key={key} value={key}>
                                          {EMERGENT_MAP[key]}
                                        </Option>
                                            ))}
                                    </Select>
                                  </Form.Item>
                                );
                            }
                            return null;
                        }}
            </Form.Item>

            <Form.Item
              name="reason"
              label="申请原因"
              rules={[
                            {
                                required: true,
                                message: '请提供跳过的原因',
                            },
                        ]}
            >
              <TextArea rows={4} placeholder="输入跳过此流程的理由" />
            </Form.Item>

            <Form.Item
              name="problem"
              label="遇到的问题"
            >
              <TextArea rows={4} placeholder="描述遇到的任何问题" />
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
};

export default SkipProcessButton;

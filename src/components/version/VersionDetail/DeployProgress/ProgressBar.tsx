import { But<PERSON>, Toolt<PERSON> } from 'antd';
import { EyeOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { isOnlineEnv } from '@/utils/env';
import { PROGRESS_NAMES, STEPS } from '@/constants/record';
import { isTBApp } from '@/utils/common';
import { VersionModel } from '@/models/version';
import { useVersionStore } from '@/store';

interface ProgressBarProps {
  currentStep: number;
}

export default function ProgressBar({ currentStep }: ProgressBarProps) {
  const progressPercent = (currentStep / (STEPS.length - 1)) * 100;

  const versionDetail = useVersionStore();
  const { versionBO } = versionDetail as VersionModel;

  return (
    <div className="space-y-9">
      <div className="space-y-9">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <h3 className="text-base font-medium">版本发布进展</h3>
            <span className="ml-2 text-sm font-normal">
              {PROGRESS_NAMES[currentStep]}
              {isOnlineEnv() ? null : '（非线上环境可跳过流程直接发布）'}
            </span>
            <Tooltip title="请按照绿色按钮提示开始下一步">
              <QuestionCircleOutlined className="text-gray-400" />
            </Tooltip>
          </div>
          {isTBApp(versionBO.appKey) && (
            <Button
              type="text"
              className="flex items-center text-blue-500 hover:text-blue-700"
              onClick={() => {
                const targetElement = document.getElementById('crash-report');
                targetElement && targetElement.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              <EyeOutlined />
              查看数据
            </Button>
          )}
        </div>
      </div>

      <div className="relative">
        <div className="h-2 bg-gray-100 rounded-full w-full" />
        <div
          className="absolute top-0 left-0 h-2 bg-blue-500 rounded-full transition-all duration-300"
          style={{ width: `${progressPercent}%` }}
        />

        <div className="flex justify-between mt-2">
          {STEPS.map((step, index) => (
            <div
              key={index}
              className={`text-sm ${
                index === 0 || index === STEPS.length - 1 ? '' : 'transform -translate-x-1/2'
              } ${index <= currentStep ? 'text-gray-900' : 'text-gray-400'}`}
              style={{
                ...(index !== STEPS.length - 1 && {
                  left: `${(index / (STEPS.length - 1)) * 100}%`,
                  position: 'absolute',
                }),
                ...(index === STEPS.length - 1 && {
                  right: '0%',
                  position: 'absolute',
                }),
              }}
            >
              {step}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

import { useMemo } from 'react';
import { NamespaceVersion, RecordItem } from '@/types/version';
import { ViewConfig } from '@/types/namespace';
import {
  RECORD_STATUS_FAIL,
  RECORD_STATUS_SUCCESS,
  RECORD_TYPE_APPLY,
  RECORD_TYPE_BETA,
  RECORD_TYPE_GRAY,
  RECORD_TYPE_RATIO_GRAY,
  RECORD_TYPE_REVIEW,
  RECORD_TYPE_SKIP,
  SKIP_PROCESS_ALL,
  SKIP_PROCESS_APPLY,
  SKIP_PROCESS_BETA,
  SKIP_PROCESS_CHECK,
  SKIP_PROCESS_GRAY,
} from '@/constants/record';

export function useDeploymentProgress(
  recordList: RecordItem[],
  viewConfig: ViewConfig,
  isRollback: boolean,
  version: NamespaceVersion,
) {
  return useMemo(() => {
    let currentStep = 0;
    let butName = 'beta';
    let hasSkipAll = false;
    let hasBeta = false;
    let hasBetaSkip = false;
    let hasApply = false;
    let hasApplySkip = false;
    let applyReady = false;
    let hasGray = false;
    let hasGraySkip = false;
    let hasReview = false;
    let lastApplyRecord: RecordItem | null = null;
    let lastOrSuccessCheckSkipRecord: RecordItem | null = null;

    for (const item of recordList) {
      const { type, status, paramDO } = item;

      switch (type) {
        case RECORD_TYPE_APPLY:
          hasApply = true;
          lastApplyRecord = item;
          if (status === RECORD_STATUS_SUCCESS) {
            applyReady = true;
          }
          break;
        case RECORD_TYPE_GRAY:
        case RECORD_TYPE_RATIO_GRAY:
          hasGray = true;
          break;
        case RECORD_TYPE_SKIP:
          if (lastOrSuccessCheckSkipRecord == null && paramDO.skipStage == SKIP_PROCESS_CHECK) {
            lastOrSuccessCheckSkipRecord = item;
          }
          if (status === RECORD_STATUS_SUCCESS) {
            if (paramDO?.skipStage === SKIP_PROCESS_BETA) hasBetaSkip = true;
            else if (paramDO?.skipStage === SKIP_PROCESS_APPLY) hasApplySkip = true;
            else if (paramDO?.skipStage === SKIP_PROCESS_GRAY) hasGraySkip = true;
            else if (paramDO?.skipStage === SKIP_PROCESS_CHECK) lastOrSuccessCheckSkipRecord = item;
            else if (paramDO?.skipStage === SKIP_PROCESS_ALL) hasSkipAll = true;
          }
          break;
        case RECORD_TYPE_BETA:
          hasBeta = true;
          break;
        case RECORD_TYPE_REVIEW:
          hasReview = true;
          break;
      }
    }

    // const needWhole = viewConfig.wholeProcess || false;
    const needWhole = true;
    const renderApply = true;
    const renderApplyTips = hasApply ? '重新申请' : '申请';
    const renderGray = !needWhole || applyReady || hasApplySkip || hasSkipAll;
    const renderReview =
      !hasReview &&
      (!needWhole ||
        hasSkipAll ||
        ((applyReady || hasApplySkip) &&
          (hasGraySkip || hasGray || version.tigaMetadata || isRollback)));
    const renderFull = !needWhole || hasReview;

    if (hasReview) {
      currentStep = 4;
      butName = 'publish';
    } else if (hasGray || hasGraySkip || hasSkipAll) {
      currentStep = 3;
      butName = 'review';
    } else if (hasApply || hasApplySkip) {
      if (applyReady || hasApplySkip) {
        currentStep = 2;
        butName = viewConfig?.smallGray ? 'smallGray' : 'gray';
      } else {
        currentStep = 1;
        butName = lastApplyRecord?.status === RECORD_STATUS_FAIL ? 'apply' : '';
      }
    } else if (hasBeta || hasBetaSkip) {
      currentStep = 1;
      butName = 'apply';
    }

    if (!needWhole) {
      butName = 'publish';
    }

    return {
      currentStep,
      butName,
      renderApply,
      renderApplyTips,
      renderGray,
      renderReview,
      renderFull,
      lastOrSuccessCheckSkipRecord,
    };
  }, [recordList, viewConfig, isRollback, version]);
}

import React from 'react';
import { Form, Input, InputNumber, Typography } from 'antd';
import { isOnlineEnv } from '@/utils/env';

const { Title, Text, Paragraph } = Typography;

interface CircleGrayProps {
  form: any;
  maxGrayCnt: number;
  expression: string;
  supportDimensions: string[];
  isComplexExpressions: boolean;
}

const CircleGray: React.FC<CircleGrayProps> = ({
  form,
  maxGrayCnt,
  expression,
  supportDimensions,
  isComplexExpressions,
}) => {
  return (
    <Form form={form} layout="vertical">
      <Form.Item
        name="deviceCnt"
        label="推送在线设备数(上限)"
        rules={[
          {
            required: true,
            message: '请输入推送设备数',
          },
        ]}
        extra={
          <Text type="secondary">
            请输入1-{maxGrayCnt}之间的数字，仅推送在线设备，最多推送当前在线设备数！
          </Text>
        }
      >
        <InputNumber min={1} max={maxGrayCnt} style={{ width: '100%' }} />
      </Form.Item>

      {!expression && (
        <Form.Item
          name="grayStrategy"
          label="追加圈选策略"
          extra={
            <Text type="secondary">
              您可新增圈选条件(同orange策略表达式规则)，建议仅使用如下维度:{' '}
              {supportDimensions.join(', ')}
            </Text>
          }
        >
          <Input style={{ width: '100%' }} />
        </Form.Item>
      )}

      <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
        <p className="text-sm text-red-700 mb-2">设备圈选时会忽略 did_hash 维度，务必务必确认！</p>
        <p className="text-sm text-red-700">
          安全生产要求：Orange 配置在正式上线前需要至少 1 轮灰度观察 5 分钟。灰度放量节奏依次不超过
          2000, 20000, 50000。
        </p>
      </div>

      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
        {isComplexExpressions && (
          <p className="text-sm text-yellow-700 mb-2">
            当前灰度圈选表达式需取所有策略的反，表达式过于复杂存在超时的可能。若长期未结束您可以在【任务查看】里自行取消，或者等待超时取消，数据以
          </p>
        )}
        {!isOnlineEnv() && (
          <p className="text-sm text-yellow-700 mb-2">
            仅线上环境设备在线，推送方可成功，其它环境请直接跳过。
          </p>
        )}
        <p className="text-sm text-yellow-700">
          若问题紧急可申请跳过，由主管进行审批处理；如果是回滚(走回滚按钮创建的版本)，可直接申请灰度跳过，不做审核。有问题自行承担。
        </p>
      </div>
    </Form>
  );
};

export default CircleGray;

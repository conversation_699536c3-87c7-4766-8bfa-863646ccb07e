import React from 'react';
import { message, Popconfirm, Steps, Tooltip } from 'antd';
import {
  GRAY_RATIO_UNIT,
  MIN_GRAY_MINUTES,
  TAO_TE_TEXT_NS_GRAY_RATIO_LIST,
  TEXT_NS_GRAY_RATIO_LIST,
} from '@/constants/record';
import { getApproximateNumberStr } from '@/lib/utils';
import { CheckCircleOutlined, LoadingOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { versionStage } from '@/services/version';
import { VERSION_STAGE_RATIO_GRAY } from '@/constants/version';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';
import { VersionGrayRecord } from '@/types/version';
import { isSystemAdmin } from '@/utils/permission';

const { Step } = Steps;

const RatioGray = () => {
  const versionDetail = useVersionStore();
  const updateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const {
    latestGrayRecord: grayRecord = {} as VersionGrayRecord,
    versionDeviceCnt,
    versionBO,
    tigaTaskDetail = {},
  } = versionDetail as VersionModel;

  // TBLiteAndroid-android(AppKey:24717361)
  // LTao4iPhone-ios(AppKey:24716707)
  // todo: 先临时这样支持一下，后续开放给业务配置
  const isTaoTe = ['24717361', '24716707'].includes(versionBO?.appKey);
  const grayRatioList = isTaoTe ? TAO_TE_TEXT_NS_GRAY_RATIO_LIST : TEXT_NS_GRAY_RATIO_LIST;

  const firstRatioDeviceCnt = versionDeviceCnt
    ? Math.floor((grayRatioList[1] * versionDeviceCnt) / GRAY_RATIO_UNIT)
    : 0;
  const canSkipFirstStep = tigaTaskDetail?.deviceStatistics?.activeCnt > firstRatioDeviceCnt;

  const handleStepChange = async (grayRatio: number) => {
    try {
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_RATIO_GRAY, {
        grayRatio,
      });
      message.success('百分比灰度发布成功');
      await updateVersion(versionBO.namespaceId, versionBO.version);
    } catch (e) {
      message.error(`百分比灰度发布失败: ${e.message}`);
    }
  };

  return (
    <>
      <Steps
        size="small"
        current={grayRatioList.indexOf(grayRecord.grayRatio || 0)}
        style={{ padding: '25px 0' }}
      >
        {grayRatioList.map(ratio => {
          const ratioCompare = ratio - (grayRecord.grayRatio || 0);
          const currentInProcess = ratioCompare === 0 && grayRecord.status === 'NEW';
          let disable = ratio === 0 || ratioCompare === 0;
          let disableReason = '';
          if (ratio < grayRecord.grayRatio) {
            disable = true;
            disableReason = '不支持往小调灰度比例';
          } else if (!grayRecord.grayRatio && ratio !== grayRatioList[1] && !canSkipFirstStep) {
            disable = true;
            disableReason = '请先完成第一批灰度放量';
          } else if (
            // 管理员和紧急发布可以免去灰度观测时间限制
            !isSystemAdmin() &&
            versionBO.isEmergent === 'n' &&
            grayRecord.grayRatio === grayRatioList[1] &&
            new Date().getTime() - grayRecord.gmtCreate < 1000 * 60 * MIN_GRAY_MINUTES &&
            // 当小流量灰度总生效设备数大于第一个百分比节点的设备量时，也可免去观测时间限制
            !canSkipFirstStep
          ) {
            disable = true;
            disableReason = `第一批灰度放量后至少观测 ${MIN_GRAY_MINUTES} 分钟`;
          }

          const deviceCntStr =
            versionDeviceCnt === undefined
              ? '未知'
              : getApproximateNumberStr(Math.floor((ratio * versionDeviceCnt) / GRAY_RATIO_UNIT));

          return (
            <Step
              key={ratio}
              icon={
                currentInProcess ? (
                  <Tooltip placement="top" title="等待探针文件生成中，预计需要 5-6 分钟">
                    <LoadingOutlined />
                  </Tooltip>
                ) : ratioCompare <= 0 ? (
                  <Tooltip placement="top" title={disableReason || null}>
                    <CheckCircleOutlined />
                  </Tooltip>
                ) : disable ? (
                  <Tooltip placement="top" title={disableReason || null}>
                    <PlayCircleOutlined />
                  </Tooltip>
                ) : (
                  <Popconfirm
                    title={`灰度放量 ${(ratio * 100) / GRAY_RATIO_UNIT}%`}
                    onConfirm={() => handleStepChange(ratio)}
                  >
                    <PlayCircleOutlined />
                  </Popconfirm>
                )
              }
              disabled={disable}
              title={
                ratio === 0 ? (
                  '初始'
                ) : !disable ? (
                  <Popconfirm
                    title={`灰度放量 ${(ratio * 100) / GRAY_RATIO_UNIT}%`}
                    onConfirm={() => handleStepChange(ratio)}
                  >
                    {`${(ratio * 100) / GRAY_RATIO_UNIT}%`}
                  </Popconfirm>
                ) : (
                  <Tooltip placement="top" title={disableReason || null}>
                    {`${(ratio * 100) / GRAY_RATIO_UNIT}%`}
                  </Tooltip>
                )
              }
              description={ratio === 0 ? '预估设备量' : deviceCntStr}
            />
          );
        })}
      </Steps>

      {canSkipFirstStep ? (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
          <p className="text-sm text-green-700">
            您已完成 {tigaTaskDetail?.deviceStatistics?.activeCnt} 台设备的小流量灰度放量，
            已为您跳过百分比强制第一批观测 30 分钟卡点限制，您可以继续选择后续任意比例进行放量观测。
          </p>
        </div>
      ) : (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <p className="text-sm text-red-700">
            安全生产要求：Orange 配置在正式上线前至少完成第一批灰度观察 30
            分钟，后续节点灰度比例和切流量节奏根据业务自行选择。
          </p>
        </div>
      )}
    </>
  );
};

export default RatioGray;

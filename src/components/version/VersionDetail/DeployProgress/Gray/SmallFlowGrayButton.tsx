import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Modal, Select } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';
import { createTigaTask, getTigaSuggestTemplateList } from '@/services/version';
import { getTigaTaskDetailUrl } from '@/utils/link';
import { getTigaReleaseHost } from '@/utils/env';
import { VERSION_STAGE_GRAY } from '@/constants/version';

export interface SmallFlowGrayButtonProps {
  type: 'default' | 'primary';
  checkHold: (publishStage: string, recordId: number | null, nextProcess) => void;
}

const SmallFlowGrayButton: React.FC<SmallFlowGrayButtonProps> = ({ type, checkHold }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [taskId, setTaskId] = useState<number | undefined>(undefined);
  const [iframeHeight, setIframeHeight] = useState(0);
  const [modalWidth, setModalWidth] = useState(1200);
  const [templateSelectVisible, setTemplateSelectVisible] = useState(false);
  const [templateList, setTemplateList] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<number | undefined>(undefined);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const versionDetail = useVersionStore();
  const { versionBO, namespaceBO, fetchAndUpdateVersion } = versionDetail as VersionModel & {
    fetchAndUpdateVersion: (namespaceId: string, version: string) => Promise<void>;
  };

  // 获取 Modal 宽度，根据屏幕宽度调整
  const getModalWidth = useCallback(() => {
    return window.innerWidth > 1512 ? 1500 : 1300;
  }, []);

  // 获取 iframe 高度，屏幕高度减去上下间隙
  const getIframeHeight = useCallback(() => {
    return window.innerHeight - 160;
  }, []);

  // 处理窗口大小变化
  const handleResize = useCallback(() => {
    const newWidth = getModalWidth();
    const newHeight = getIframeHeight();
    setModalWidth(newWidth);
    setIframeHeight(newHeight);
  }, [getModalWidth, getIframeHeight]);

  // 获取模板列表
  const loadTemplateList = useCallback(
    async (keywords?: string) => {
      try {
        setLoadingTemplates(true);
        const templates = await getTigaSuggestTemplateList(
          namespaceBO.namespaceId,
          versionBO.version,
          keywords,
        );
        setTemplateList(templates || []);
        // 默认选中第一个模板
        if (templates && templates.length > 0 && !selectedTemplate) {
          setSelectedTemplate(templates[0].id);
        }
      } catch (error) {
        message.error(`获取模板列表失败: ${error.message}`);
      } finally {
        setLoadingTemplates(false);
      }
    },
    [namespaceBO.namespaceId, versionBO.version, selectedTemplate],
  );

  // 处理模板搜索
  const handleTemplateSearch = useCallback(
    (value: string) => {
      loadTemplateList(value || undefined);
    },
    [loadTemplateList],
  );

  // 处理模板选择确认
  const handleTemplateConfirm = useCallback(async () => {
    if (!selectedTemplate) {
      message.error('请选择一个模板');
      return;
    }

    try {
      const currentTaskId = await createTigaTask(
        namespaceBO.namespaceId,
        versionBO.version,
        selectedTemplate,
      );
      setTaskId(currentTaskId);
      setTemplateSelectVisible(false);
      setModalVisible(true);
      setSelectedTemplate(undefined);

      // 刷新版本详情数据
      await fetchAndUpdateVersion(namespaceBO.namespaceId, versionBO.version);
    } catch (error) {
      message.error(`创建小流量灰度任务失败: ${error.message}`);
    }
  }, [selectedTemplate, namespaceBO.namespaceId, versionBO.version, fetchAndUpdateVersion]);

  // 组件挂载时添加监听器，卸载时移除
  useEffect(() => {
    // 初始化尺寸
    handleResize();

    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [handleResize]);

  const handleClick = async () => {
    let currentTaskId: number;

    if (versionBO.tigaMetadata) {
      const metadata = JSON.parse(versionBO.tigaMetadata);
      currentTaskId = metadata.taskId;
      setTaskId(currentTaskId);
      setModalVisible(true);
    } else {
      // 如果没有 tigaMetadata，先显示模板选择模态框
      setTemplateSelectVisible(true);
      loadTemplateList();
    }
  };

  return (
    <>
      <Button
        type={type}
        onClick={() => {
          checkHold(VERSION_STAGE_GRAY, null, () => {
            handleClick();
          });
        }}
      >
        小流量灰度
      </Button>

      {/* 模板选择模态框 */}
      <Modal
        title="选择小流量灰度模板"
        open={templateSelectVisible}
        onCancel={() => {
          setTemplateSelectVisible(false);
          setSelectedTemplate(undefined);
        }}
        width={600}
        footer={
          <div className="flex justify-between items-center">
            <div className="text-gray-400 flex items-center">
              <InfoCircleOutlined className="mr-2" />
              可前往&nbsp;
              <a
                href={`https://${getTigaReleaseHost()}/template/list?tab=ALL`}
                target="_blank"
                rel="noreferrer"
                className="text-blue-500"
              >
                Tiga 灰度模板中心
              </a>{' '}
              自定义更多模板
            </div>
            <div className="flex items-center">
              <a
                href="https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8aZxe5myC0nggMRDWgN7R35y?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent"
                target="_blank"
                rel="noreferrer"
                className="text-blue-500 mr-4"
              >
                操作文档
              </a>
              <Button
                onClick={() => {
                  setTemplateSelectVisible(false);
                  setSelectedTemplate(undefined);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={handleTemplateConfirm}
                loading={loadingTemplates}
                className="ml-2"
              >
                确定
              </Button>
            </div>
          </div>
        }
      >
        <Select
          placeholder="搜索并选择模板"
          className="w-full"
          value={selectedTemplate}
          onChange={setSelectedTemplate}
          loading={loadingTemplates}
          showSearch
          filterOption={false}
          onSearch={handleTemplateSearch}
          notFoundContent={loadingTemplates ? '加载中...' : '未找到模板'}
        >
          {templateList.map(template => (
            <Select.Option key={template.id} value={template.id}>
              {template.name}
            </Select.Option>
          ))}
        </Select>
      </Modal>

      {/* 小流量灰度详情模态框 */}
      <Modal
        title="小流量灰度"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={
          <div className="flex justify-between items-center">
            <div />
            <div className="flex items-center">
              <a
                href="https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8aZxe5myC0nggMRDWgN7R35y?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent"
                target="_blank"
                rel="noreferrer"
                className="text-blue-500 mr-4"
              >
                操作文档
              </a>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
            </div>
          </div>
        }
        width={modalWidth}
        style={{ top: '10px' }}
      >
        {taskId && (
          <iframe
            ref={iframeRef}
            src={getTigaTaskDetailUrl(taskId, true, true, 'small')}
            style={{
              width: '100%',
              height: `${iframeHeight}px`,
              border: 'none',
            }}
            title="小流量灰度"
            allowFullScreen
          />
        )}
      </Modal>
    </>
  );
};

export default SmallFlowGrayButton;

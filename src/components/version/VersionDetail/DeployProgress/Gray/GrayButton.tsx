import React, { useCallback, useEffect, useState } from 'react';
import { Button, Form, message, Modal, Popconfirm, Tabs } from 'antd';
import CircleGray from './CircleGray';
import RatioGray from './RatioGray';
import { VersionGrayRecord } from '@/types/version';
import { MIN_DEVICE_CNT } from '@/constants/record';
import {
  GRAY_CIRCLE_DIMENSIONS,
  GRAY_TYPE_MASS_CIRCLE,
  VERSION_STAGE_GRAY,
  VERSION_STAGE_SKIP,
} from '@/constants/version';
import { getLatestGrayRecord, versionStage } from '@/services/version';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';

const { TabPane } = Tabs;

export interface GrayCircleModalProps {
  type: 'default' | 'primary';
  checkHold: (publishStage: string, recordId: number | null, nextProcess) => void;
}

const GrayButton: React.FC<GrayCircleModalProps> = ({ type, checkHold }) => {
  const versionDetail = useVersionStore();
  const updateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const fetchAndUpdateTigaTaskDetail = useVersionStore(state => state.fetchAndUpdateTigaTaskDetail);

  const {
    latestGrayRecord,
    versionBO,
    viewConfig,
    ratioGrayConfig,
    grayConfig,
    versionDeviceCnt,
    hasEditPermission,
  } = versionDetail as VersionModel;

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [grayRecord, setGrayRecord] = useState<VersionGrayRecord>(latestGrayRecord || {});
  const [form] = Form.useForm();

  useEffect(() => {
    if (latestGrayRecord) {
      setGrayRecord(latestGrayRecord);
      setLoading(false);
    }
  }, [latestGrayRecord]);

  const refreshLastGrayRecord = useCallback(async () => {
    if (versionBO) {
      const res = await getLatestGrayRecord(versionBO.namespaceId, versionBO.version);
      if (res.status !== 'NEW') {
        setGrayRecord(res);
      }
    }
  }, [versionBO]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    if (visible && grayRecord.status === 'NEW' && viewConfig.ratioGray) {
      intervalId = setInterval(refreshLastGrayRecord, 10000);
    }
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [visible, grayRecord.status, viewConfig.ratioGray, refreshLastGrayRecord]);

  const supportConfigs = grayConfig?.supportConfigs || {};
  const expression = grayConfig?.expression || '';
  const massSupportConfig = supportConfigs[GRAY_TYPE_MASS_CIRCLE] || {};
  const maxGrayCnt = massSupportConfig.maxGrayCnt || 10000;
  const supportDimensions = massSupportConfig.supportDimensions || GRAY_CIRCLE_DIMENSIONS;
  const isComplexExpressions = !(versionBO && versionBO.strategy) && expression.length > 60;

  // 兼容查询设备量接口失败的情况
  const deviceCntEnoughForRatioGray =
    versionDeviceCnt === undefined || versionDeviceCnt >= MIN_DEVICE_CNT;
  const isRatioGrayEnabled =
    viewConfig.ratioGray && ratioGrayConfig?.support && deviceCntEnoughForRatioGray;

  let unsupportedMsg = (viewConfig.ratioGray && ratioGrayConfig?.unsupportedMsg) || '';
  if (!deviceCntEnoughForRatioGray) {
    unsupportedMsg = `符合当前策略规则的设备量小于 ${MIN_DEVICE_CNT}, 无需百分比灰度`;
  }
  // 如果不支持 Tiga 小流量灰度或不支持百分比灰度，仍然透出定量灰度入口
  const showDefaultGrayTab =
    !(viewConfig.smallGray && versionBO.tigaMetadata) || !isRatioGrayEnabled;

  const [activeTab, setActiveTab] = useState('circle_gray');

  useEffect(() => {
    setActiveTab(isRatioGrayEnabled ? 'ratio_gray' : 'circle_gray');
  }, [isRatioGrayEnabled]);

  useEffect(() => {
    if (visible && activeTab === 'ratio_gray' && versionBO?.tigaMetadata) {
      fetchAndUpdateTigaTaskDetail(versionBO.namespaceId, versionBO.version);
    }
  }, [
    visible,
    activeTab,
    versionBO?.tigaMetadata,
    versionBO.namespaceId,
    versionBO.version,
    fetchAndUpdateTigaTaskDetail,
  ]);

  const onHandleSkip = async () => {
    setLoading(true);
    try {
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_SKIP, {
        skipStage: VERSION_STAGE_GRAY,
      });
      message.success('申请跳过成功');
      form.resetFields();
      setVisible(false);
      await updateVersion(versionBO.namespaceId, versionBO.version);
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 定量灰度
   */
  const onSubmitCircleGray = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const params = {
        deviceCnt: values.deviceCnt || 1,
        attachedGrayStrategy: values.grayStrategy || '',
      };
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_GRAY, params);
      message.success('定量灰度发布成功');
      setVisible(false);
      form.resetFields();
      await updateVersion(versionBO.namespaceId, versionBO.version);
    } catch (error) {
      if (!error.errorFields?.length) {
        message.error(error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };

  if (maxGrayCnt && maxGrayCnt <= 0) {
    return (
      <>
        <Button type={type} disabled={!hasEditPermission} onClick={() => setVisible(true)} />
        <Modal
          title="定量灰度"
          open={visible}
          onCancel={handleCancel}
          width={600}
          footer={[
            <Button key="cancel" onClick={handleCancel}>
              取消
            </Button>,
            <Button key="skip" type={'primary'} onClick={onHandleSkip}>
              申请跳过
            </Button>,
          ]}
        >
          <div className="m-6 text-red-600">
            非生产环境或其它原因，各灰度能力均被关闭，请直接申请跳过~~
          </div>
        </Modal>
      </>
    );
  }

  return (
    <>
      <Button
        type={type}
        disabled={!hasEditPermission}
        onClick={() => {
          checkHold(VERSION_STAGE_GRAY, null, () => {
            setVisible(true);
          });
        }}
      >
        {showDefaultGrayTab ? '灰度发布' : '百分比灰度'}
      </Button>
      <Modal
        visible={visible}
        onCancel={handleCancel}
        width={1300}
        closable={false}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Popconfirm
            title="当前跳过不符合安全生产规范，申请跳过需要审批。审批通过后此审批单将不做灰度管控，确认要跳过吗？"
            onConfirm={onHandleSkip}
          >
            <Button loading={loading}>申请跳过</Button>
          </Popconfirm>,
          activeTab === 'circle_gray' && (
            <Button key="skip" loading={loading} type={'primary'} onClick={onSubmitCircleGray}>
              确认
            </Button>
          ),
        ]}
      >
        <Tabs
          onChange={key => setActiveTab(key)}
          activeKey={activeTab}
          tabBarExtraContent={unsupportedMsg}
        >
          {showDefaultGrayTab && (
            <TabPane
              tab="定量灰度"
              key="circle_gray"
              disabled={!!grayRecord.grayRatio && isRatioGrayEnabled}
            >
              <CircleGray
                form={form}
                maxGrayCnt={maxGrayCnt}
                expression={expression}
                supportDimensions={supportDimensions}
                isComplexExpressions={isComplexExpressions}
              />
            </TabPane>
          )}
          {viewConfig.ratioGray && (
            <TabPane tab="百分比灰度" key="ratio_gray" disabled={!isRatioGrayEnabled}>
              <RatioGray />
            </TabPane>
          )}
        </Tabs>
      </Modal>
    </>
  );
};

export default GrayButton;

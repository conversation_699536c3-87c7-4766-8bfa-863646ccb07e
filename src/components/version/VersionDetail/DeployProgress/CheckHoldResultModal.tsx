import React, { useState, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON>, Button, Popconfirm, Table, Badge } from 'antd';
import {
  CHECK_PROVIDER_MAP,
  CHECK_STATUS_PASS,
  RECORD_STATUS_PROCESSING,
  RECORD_STATUS_SUCCESS,
  SKIP_SUPPORT_BPMS,
  SKIP_SUPPORT_NONE,
} from '@/constants/record';
import { isSystemAdmin } from '@/utils/permission';
import { getBpmsUrl } from '@/utils/link';
import moment from 'moment';

export interface CheckDetail {
  provider: string;
  status: string;
  skipSupport: string;
  approvalUrl?: string;
  detailUrl?: string;
  message: string;
}

export interface CheckResult {
  checkStatus: string;
  lastCheckTime: string;
  details: CheckDetail[];
}

export interface CheckHoldResultModalProps {
  onHandleSkip?: (problem: string) => void;
  checkResult?: CheckResult;
  nextProcess?: () => void;
  lastOrSuccessCheckSkipRecord?: any;
  recordId?: string;
  oneStepSkip?: boolean;
  visible: boolean;
}

const CheckHoldResultModal = (props: CheckHoldResultModalProps) => {
  const [visible, setVisible] = useState(!!props?.visible);

  const {
    checkResult = null,
    nextProcess = null,
    oneStepSkip = false,
    recordId = null,
    lastOrSuccessCheckSkipRecord = null,
    onHandleSkip = null,
  } = props;

  const handleOk = useCallback(async () => {
    if (!recordId) {
      nextProcess && await nextProcess();
    }
    setVisible(false);
  }, [recordId, nextProcess]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  const columns = useMemo(() => [
    {
      dataIndex: 'provider',
      title: '来源',
      render: (text: string) => CHECK_PROVIDER_MAP[text],
    },
    {
      dataIndex: 'status',
      title: '状态',
      // render: (text: string) => ConvertUtils.getCheckStatusShow(text),
    },
    {
      title: '后续',
      dataIndex: 'skipSupport',
      render: (text: string, record: CheckDetail) => {
        if (record.status === CHECK_STATUS_PASS) {
          return '完成';
        }
        return '';
        // return ConvertUtils.getCheckSkipSupportName(text);
      },
    },
    {
      dataIndex: 'detailUrl',
      title: '详情',
      render: (text: string, record: CheckDetail) => {
        if (record.approvalUrl) {
          return <a href={record.approvalUrl} target="_blank" rel="noopener noreferrer">审批地址</a>;
        }
        return text ? <a href={text} target="_blank" rel="noopener noreferrer">详情查看</a> : null;
      },
    },
    {
      dataIndex: 'message',
      title: '描述',
      width: 500,
    },
  ], []);

  if (!checkResult) {
    return null;
  }

  const hasCheckSkip = lastOrSuccessCheckSkipRecord && lastOrSuccessCheckSkipRecord.status === RECORD_STATUS_SUCCESS;
  const isCheckSkipProcessing = lastOrSuccessCheckSkipRecord && lastOrSuccessCheckSkipRecord.status === RECORD_STATUS_PROCESSING;
  const bpmsUrl = isCheckSkipProcessing ? getBpmsUrl(lastOrSuccessCheckSkipRecord.arg0) : null;

  const details = checkResult.details || [];
  const checkStatus = checkResult.checkStatus || '';
  const isSecAdmin = oneStepSkip || isSystemAdmin();
  const bpmsList = details.filter(item => item.skipSupport === SKIP_SUPPORT_BPMS);
  const hasSupportBpms = bpmsList.length > 0;
  const noneList = details.filter(item => item.skipSupport === SKIP_SUPPORT_NONE);
  const hasSupportNone = noneList.length > 0;
  const showBpmsButton = !recordId && !hasSupportNone && hasSupportBpms && !hasCheckSkip;
  const bpmsProblem = bpmsList.map(item => item.message).join(';');

  let canNext = true;
  if (!recordId && !isSecAdmin) {
    if (hasSupportNone) {
      canNext = false;
    } else if (hasSupportBpms && !hasCheckSkip) {
      canNext = false;
    }
  }

  const applySkipCheckHold = useCallback(async () => {
    if (onHandleSkip) {
      onHandleSkip(bpmsProblem);
      setVisible(false);
    }
  }, [onHandleSkip, bpmsProblem]);

  const renderBpmsButton = useCallback((primary: boolean) => {
    if (!showBpmsButton) {
      return null;
    }
    return (
      <Popconfirm
        title="卡口跳过申请主管审批，若审批通过，后续所有卡口都将失效，需要业务方自行判断是否继续，确认申请跳过吗？"
        onConfirm={applySkipCheckHold}
      >
        <Button type={primary ? 'primary' : 'default'}>跳过卡口</Button>
      </Popconfirm>
    );
  }, [showBpmsButton, applySkipCheckHold]);

  const renderBpmsTips = useCallback(() => {
    if (hasCheckSkip) {
      return (
        <div>
          <span
            style={{ color: 'red' }}
          >卡口检测遇到了阻断，已经申请过卡口跳过，操作者若判断没有问题可点击强制继续执行后续逻辑~~</span>
          <span> {renderBpmsButton(false)} </span>
        </div>
      );
    } else if (isCheckSkipProcessing && bpmsUrl) {
      return (
        <div>
          <span style={{ color: 'red' }}>
            卡口检测遇到了阻断，申请的卡口跳过处于审批过程中，
            <a href={bpmsUrl} target="_blank" rel="noopener noreferrer">查看审批详情</a>，若 bpms 流程异常可重新申请
          </span>
          <span>
            {renderBpmsButton(false)}
            (不要重复申请)
          </span>
        </div>
      );
    } else {
      return (
        <div>
          <span style={{ color: 'red' }}>
            卡口检测遇到了阻断，操作者若判断没有问题，点击
            <span>{renderBpmsButton(true)}</span>
            申请并审批成功后才可执行后续逻辑~~
          </span>
        </div>
      );
    }
  }, [hasCheckSkip, isCheckSkipProcessing, bpmsUrl, renderBpmsButton]);

  const renderTips = useCallback(() => {
    if (recordId) {
      return null;
    } else if (hasSupportNone) {
      return (
        <div>
          <span style={{ color: 'red' }}>
            卡口检测遇到了封网阻断，请在点击
            <a href={noneList[0].approvalUrl} target="_blank" rel="noopener noreferrer">审批地址</a>
            执行后续逻辑，或者等待封网结束~~
          </span>
        </div>
      );
    } else if (hasSupportBpms) {
      return renderBpmsTips();
    } else if (checkStatus !== CHECK_STATUS_PASS) {
      return (
        <div>
          <span
            style={{ color: 'red' }}
          >卡口检测遇到了阻断或异常，支持 强制继续 继续放量，请根据情况判断是否继续~~   </span>
        </div>
      );
    }
    return null;
  }, [recordId, hasSupportNone, hasSupportBpms, checkStatus, noneList, renderBpmsTips]);

  const okButtonProps = !canNext ? { disabled: true } : {};
  const okText = (recordId || checkResult.checkStatus === CHECK_STATUS_PASS) ? '确认' : '强制继续';

  return (
    <Modal
      title="卡口检测结果"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText={okText}
      cancelText="关闭"
      okButtonProps={okButtonProps}
      width={900}
      destroyOnClose
    >
      <div className="mb-4">
        <div className="mb-2">检测结果：<Badge
          status={checkResult.checkStatus === CHECK_STATUS_PASS ? 'success' : 'error'}
          text={CHECK_PROVIDER_MAP[checkResult.checkStatus]}
        /></div>
        <div className="mb-2">检测时间：{moment(checkResult.lastCheckTime).format('YYYY-MM-DD HH:mm:ss')}</div>
        <div>
          每次放量(灰度或正式发布)前会执行卡口检测，若检测均通过或允许跳过方可执行后续逻辑，否则需要等待或者执行相应的审批流程.
          <a
            href="https://yuque.antfin-inc.com/wireless-orange/wiki/ggvqdb#BK4al"
            target="_blank"
            rel="noopener noreferrer"
          >文档</a>。
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={checkResult.details}
        rowKey={record => record.provider}
        pagination={false}
      />
      <div className="mt-4 ml-5">
        {renderTips()}
      </div>
    </Modal>
  );
};

export default CheckHoldResultModal;


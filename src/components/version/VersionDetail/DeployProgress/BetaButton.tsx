import React, { useState } from 'react';
import { Button, Form, Input, message, Modal, Popconfirm, Spin, Tooltip, Typography } from 'antd';
import { isOnlineEnv } from '@/utils/env';
import { versionStage } from '@/services/version';
import { VERSION_STAGE_BETA, VERSION_STAGE_SKIP } from '@/constants/version';
import { SKIP_PROCESS_BETA } from '@/constants/record';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { isTBApp } from '@/utils/common';

interface BetaButtonProps {
  type: 'primary' | 'default';
}

export const BetaButton: React.FC<BetaButtonProps> = ({ type }) => {
  const versionDetail = useVersionStore();
  const updateVersion = useVersionStore(state => state.fetchAndUpdateVersion);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { versionBO, hasEditPermission } = versionDetail as VersionModel;

  const handleOk = async () => {
    setLoading(true);
    try {
      const values = await form.validateFields();
      const utdids = values.utdids
        .split('\n')
        .map(i => i.trim())
        .join(',');
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_BETA, { utdids });
      setVisible(false);
      message.success('BETA灰度发布成功');
      form.resetFields();
      await updateVersion(versionBO.namespaceId, versionBO.version);
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  const handleSkipBeta = async () => {
    setLoading(true);
    try {
      await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_SKIP, {
        skipStage: SKIP_PROCESS_BETA,
      });
      message.success('BETA灰度跳过成功');
      setVisible(false);
      form.resetFields();
      await updateVersion(versionBO.namespaceId, versionBO.version);
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const betaTips = isOnlineEnv()
    ? '打开目标设备APP，确保在线后提交'
    : '仅线上环境设备在线，推送方可成功，其他环境请直接跳过';

  return (
    <>
      {isTBApp(versionBO.appKey) ? (
        <Popconfirm
          title="推荐使用 Orange Debug 页面中的【强制加载该版本配置内容】来进行指定设备的 BETA 验证"
          okText="好的，前往扫码进行快速 BETA 验证"
          cancelText="不，坚持使用 BETA 推送工具"
          onConfirm={() => {
            const targetElement = document.getElementById('debug-qr');
            targetElement && targetElement.scrollIntoView({ behavior: 'smooth' });
          }}
          onCancel={() => setVisible(true)}
        >
          <Button disabled={!hasEditPermission} type={type}>
            BETA灰度
          </Button>
        </Popconfirm>
      ) : (
        <Button disabled={!hasEditPermission} type={type} onClick={() => setVisible(true)}>
          BETA灰度
        </Button>
      )}
      <Modal
        open={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        cancelText="取消"
        okText="确认"
        footer={[
          <Button key="cancel" loading={loading} onClick={handleCancel}>
            取消
          </Button>,
          <Button key="skip" loading={loading} onClick={handleSkipBeta}>
            跳过 BETA 灰度
          </Button>,
          <Button key="confirm" loading={loading} type="primary" onClick={handleOk}>
            确认
          </Button>,
        ]}
      >
        <Spin spinning={loading}>
          <Form form={form} layout="vertical" className="space-y-6">
            <Form.Item
              name="utdids"
              label={
                <span className="text-lg font-medium flex items-center">
                  UTDID 列表
                  <Tooltip title="每行输入一个UTDID">
                    <InfoCircleOutlined className="ml-2 text-gray-400" />
                  </Tooltip>
                </span>
              }
              rules={[
                {
                  required: true,
                  message: 'UTDID列表为必填项',
                },
              ]}
            >
              <Input.TextArea
                rows={8}
                className="font-mono text-sm"
                placeholder="请在此输入UTDID列表，每行一个"
              />
            </Form.Item>

            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
              <Typography.Text type="danger" strong className="flex items-center">
                <ExclamationCircleOutlined className="mr-2" />
                警告：推送时将忽略策略与版本
              </Typography.Text>
            </div>

            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
              <Typography.Text type="warning" className="block text-sm">
                {betaTips}
              </Typography.Text>
            </div>
          </Form>
        </Spin>
      </Modal>
    </>
  );
};

export default BetaButton;

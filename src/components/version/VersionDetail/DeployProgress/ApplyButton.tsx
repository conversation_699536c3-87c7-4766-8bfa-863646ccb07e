'use client';

import React, { useState } from 'react';
import { Button, Form, Input, message, Modal, Radio, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { versionStage } from '@/services/version';
import { VERSION_STAGE_APPLY } from '@/constants/version';
import { useVersionStore } from '@/store';
import { VersionModel } from '@/models/version';
import { isTBApp } from '@/utils/common';

interface FormData {
    isEmergent: 'y' | 'n';
    reason: string;
}

interface ApplyButtonProps {
    renderApplyTips: string;
    type: 'primary' | 'default';
}

const ApplyButton: React.FC<ApplyButtonProps> = ({
                                                     type,
                                                     renderApplyTips,
                                                 }) => {
    const versionDetail = useVersionStore();
    const updateVersion = useVersionStore((state) => state.fetchAndUpdateVersion);
    const [form] = useForm<FormData>();
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    const {
        versionBO,
        hasEditPermission,
    } = versionDetail as VersionModel;

    const showModal = () => setVisible(true);

    const handleOk = async () => {
        try {
            setLoading(true);
            const values = await form.validateFields();
            await versionStage(versionBO.namespaceId, versionBO.version, VERSION_STAGE_APPLY, values);
            message.success('申请发布成功，等待审批通过');
            setVisible(false);
            form.resetFields();
            await updateVersion(versionBO.namespaceId, versionBO.version);
        } catch (error) {
            if (!error.errorFields?.length) {
                message.error(error.message);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        setVisible(false);
        form.resetFields();
    };

    return (
      <>
        <Button
          type={type}
          disabled={!hasEditPermission}
          onClick={showModal}
        >
          {renderApplyTips}发布
        </Button>
        <Modal
          open={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          confirmLoading={loading}
        >
          <Spin spinning={loading}>
            <Form form={form} layout="vertical" className="space-y-6">
              <Form.Item
                name="emergent"
                label={<span className="text-lg font-medium">生效类型</span>}
                rules={[{
                                required: true,
                                message: '请选择一个生效类型',
                            }]}
              >
                <Radio.Group className="space-y-4">
                  <Radio value="n" className="flex items-start">
                    <div>
                      <div className="font-medium">普通生效</div>
                      <div className="text-sm text-gray-500">约10分钟，推荐</div>
                    </div>
                  </Radio>
                  <Radio value="y" className="flex items-start">
                    <div>
                      <div className="font-medium">立即生效</div>
                      <div className="text-sm text-gray-500">约2分钟，需谨慎，影响全局</div>
                    </div>
                  </Radio>
                </Radio.Group>
              </Form.Item>

              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                <p className="text-sm text-yellow-700">
                  立即生效影响全局稳定性，仅回滚、故障类允许申请该生效类型，申请后需要一级或二级主管审批。
                </p>
              </div>
              {/* 当前仅手淘支持加速 */}
              {isTBApp(versionBO.appKey) && <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                <p className="text-sm text-red-700">
                  立即生效配置发布后会进行生效加速：推送该版本配置给在线设备并在设备启动时拉取最新版本配置，请自行进行风险评估。
                </p>
                </div>}

              <Form.Item
                name="reason"
                label={<span className="text-lg font-medium">申请原因</span>}
                help="不超过 64 个字符"
                rules={[
                                {
                                    required: true,
                                    message: '请输入申请原因',
                                },
                                {
                                    max: 64,
                                    message: '申请原因应在 64 个字符以内',
                                },
                            ]}
              >
                <Input.TextArea
                  rows={4}
                  className="resize-none"
                  placeholder="一句话简要说明发布原因，不超过 64 个字符"
                />
              </Form.Item>
            </Form>
          </Spin>
        </Modal>
      </>
    );
};
export default ApplyButton;


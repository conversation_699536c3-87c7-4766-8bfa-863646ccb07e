'use client';

import { Card } from 'antd';
import { useDeploymentProgress } from './useDeploymentProgress';
import { ActionButtons } from './ActionButtons';

import { useVersionStore } from '@/store';
import { VersionDetail } from '@/types/version';
import ProgressBar from '@/components/version/VersionDetail/DeployProgress/ProgressBar';

export default function DeploymentProgress() {
  const versionDetail = useVersionStore();
  const { versionBO: version, recordList, viewConfig } = versionDetail as VersionDetail;

  const isRollback = version?.sourceDataMeta?.rollback || false;

  const {
    currentStep,
    butName,
    renderApply,
    renderApplyTips,
    renderGray,
    renderReview,
    renderFull,
    lastOrSuccessCheckSkipRecord,
  } = useDeploymentProgress(recordList, viewConfig, isRollback, version);

  return (
    <Card className="w-full">
      <ProgressBar currentStep={currentStep} />
      <ActionButtons
        renderApply={renderApply}
        renderApplyTips={renderApplyTips}
        renderGray={renderGray}
        renderReview={renderReview}
        renderFull={renderFull}
        butName={butName}
        lastOrSuccessCheckSkipRecord={lastOrSuccessCheckSkipRecord}
      />
    </Card>
  );
}

'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Table } from 'antd';
import { Link } from '@ice/runtime';
import { EmployeeList } from '@/components/user';
import { FINISHED_VERSION_STATUS_LIST, OFFLINE_REASON_MAP, YES } from '@/constants/version';
import { useVersionStore } from '@/store';
import { ResourceBO, SceneContent, VersionDetail } from '@/types/version';
import { PropertiesDiffViewer } from '@/components/properties';
import { useEffect, useState } from 'react';
import { queryResourceDetail } from '@/services/resouce';
import { getScenesContentsByResource } from '@/utils/version';

function StrategyList() {
  const versionDetail = useVersionStore();
  const [diffModalVisible, setDiffModalVisible] = useState(false);
  const [diffRecord, setDiffRecord] = useState<any>();
  const [diffScenesContents, setDiffScenesContents] = useState<SceneContent[]>([]);

  useEffect(() => {
    (async () => {
      if (diffRecord && diffRecord.resourceId) {
        const resource: ResourceBO = await queryResourceDetail(diffRecord.resourceId);
        setDiffScenesContents(getScenesContentsByResource(resource));
      }
    })();
  }, [diffRecord && diffRecord.resourceId]);

  const {
    versionBO: version,
    lastVersionBO,
    noStrategyList,
    offlineList,
    onlineList,
    userMap,
    resourceBO,
  } = versionDetail as VersionDetail;
  let versionBO: any = Object.assign({}, version);
  const currentVersionId = versionBO.version;
  const lastVersionId = (lastVersionBO && lastVersionBO.version) || '';
  const onlineVersionIds = (versionBO.versions && versionBO.versions.split(',')) || [];
  const offlineMap = versionBO.offlines ? JSON.parse(versionBO.offlines) : {};
  const isFinished = versionBO.status && FINISHED_VERSION_STATUS_LIST.includes(versionBO.status);

  const strategyList: any[] = [];

  let title = '生效版本';
  const fillCommonVersionDesc = _item => {
    const item = Object.assign({}, _item);
    if (item.isAvailable === YES) {
      item.valid = 'success';
      item.validTitle = '生效中';
    } else {
      item.valid = 'error';
      item.validTitle = '已失效';
    }
    return item;
  };
  const fillVersionDesc = function (_item) {
    const item = fillCommonVersionDesc(_item);
    const itemVersion = item.version;
    if (itemVersion === currentVersionId) {
      item.code = 'current';
      item.message = '当前版本';
      item.valid = 'processing';
      item.validTitle = '生效';
      return item;
    }
    if (lastVersionId && itemVersion === lastVersionId) {
      item.code = 'last';
    }
    if (onlineVersionIds.indexOf(itemVersion) > -1) {
      item.valid = 'success';
      item.validTitle = '生效';
    }
    for (let key in offlineMap) {
      if (key === itemVersion) {
        item.valid = 'error';
        item.validTitle = '失效';
        let value = offlineMap[key];
        item.message = OFFLINE_REASON_MAP[value];
      }
    }
    return item;
  };

  if (versionBO.versions) {
    if (noStrategyList && noStrategyList.length > 1) {
      for (let i = 0; i < noStrategyList.length; i++) {
        let each = noStrategyList[i];
        if (each.appVersion != '*') {
          if (!offlineMap || !offlineMap[each.version]) {
            strategyList.push(fillVersionDesc(each));
          }
        }
      }
    }
    if (onlineList && onlineList[0]) {
      for (let i = 0; i < onlineList.length; i++) {
        let each = onlineList[i];
        strategyList.push(fillVersionDesc(each));
      }
    }
    if (offlineList && offlineList[0]) {
      for (let i = 0; i < offlineList.length; i++) {
        let each = offlineList[i];
        strategyList.push(fillVersionDesc(each));
      }
    }
  } else {
    // 历史发布 或者常规发布
    title = '关联版本';

    if (!isFinished) {
      versionBO.valid = 'processing';
      versionBO.validTitle = '生效';
    } else {
      versionBO = fillCommonVersionDesc(versionBO);
    }
    versionBO.code = 'current';
    versionBO.message = '当前版本';
    strategyList.push(versionBO);
    if (offlineList && offlineList[0]) {
      for (let i = 0; i < offlineList.length; i++) {
        let each = offlineList[i];
        strategyList.push(fillVersionDesc(each));
      }
    } else if (lastVersionBO.version) {
      const _lastVersionBO = fillCommonVersionDesc(lastVersionBO);
      _lastVersionBO.code = 'last';
      _lastVersionBO.message = '上一版本';
      strategyList.push(_lastVersionBO);
    }
  }

  const columns = [
    {
      dataIndex: 'id',
      title: 'Version',
      render: (text, record) => {
        return (
          <Link
            to={`/workspace/version/detail?namespaceId=${record.namespaceId}&version=${record.version}`}
          >
            {record.version}
          </Link>
        );
      },
    },
    {
      dataIndex: 'appVersion',
      title: 'appVersion',
    },
    {
      dataIndex: 'strategy',
      title: '策略',
    },
    {
      dataIndex: 'creator',
      title: '创建人',
      render: (text: string) => <EmployeeList empIds={[text]} empInfoMap={userMap} />,
    },
    {
      dataIndex: 'gmtPublishTime',
      title: '发布时间',
    },
    {
      title: '生效',
      dataIndex: 'valid',
      render: (text, record) => {
        return <Badge status={record.valid} text={record.validTitle} />;
      },
    },
    {
      title: '描述',
      dataIndex: 'message',
    },
    {
      title: '操作',
      dataIndex: 'op',
      render(text, record) {
        if (record.code === 'current') {
          return null;
        }
        return (
          <Button
            type="dashed"
            size="small"
            onClick={async () => {
              setDiffRecord(record);
              setDiffModalVisible(true);
            }}
          >
            数据对比
          </Button>
        );
      },
    },
  ];

  return (
    <div className="mb-6">
      <h2 className="text-lg font-medium mb-4">{title}</h2>
      <Table columns={columns} dataSource={strategyList} rowKey="version" pagination={false} />

      {diffRecord && (
        <Modal
          title={`${versionBO.version} Compare To ${diffRecord.version}(当前)`}
          open={diffModalVisible}
          onCancel={() => setDiffModalVisible(false)}
          width={1200}
          footer={null}
        >
          <PropertiesDiffViewer
            originalScenesContents={diffScenesContents}
            diffScenesContents={getScenesContentsByResource(resourceBO)}
          />
        </Modal>
      )}
    </div>
  );
}

export default StrategyList;

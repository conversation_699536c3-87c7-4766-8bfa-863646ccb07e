import { message, Modal, QRCode, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { getSwitchDebugQrUrl } from '@/utils/link';
import { getDebugInfo } from '@/services/orange-be/ReleaseOrderController';

interface QRCodeModalProps {
  open: boolean;
  onCancel: () => void;
  releaseVersion: string;
  appKey: string;
  isFinished: boolean;
}

export default function QRCodeModal({
  open,
  onCancel,
  releaseVersion,
  appKey,
  isFinished,
}: QRCodeModalProps) {
  const [debugInfo, setDebugInfo] = useState<API.DebugInfoDTO | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && releaseVersion) {
      setLoading(true);
      (async () => {
        try {
          const result = await getDebugInfo({ releaseVersion });
          setDebugInfo(result.data);
        } catch (e) {
          message.error(`获取调试信息失败：${e.message}`);
        } finally {
          setLoading(false);
        }
      })();
    }
  }, [open, releaseVersion]);

  return (
    <Modal open={open} onCancel={onCancel} footer={null} width={400} closeIcon={false} centered>
      {loading ? (
        <div className="text-center p-6">
          <Spin size="large" />
        </div>
      ) : debugInfo ? (
        <div className="text-center">
          <div className="flex justify-center p-6">
            <QRCode
              value={getSwitchDebugQrUrl({
                ...debugInfo,
                appKey: appKey,
                version: isFinished ? undefined : releaseVersion,
              })}
              size={300}
              bordered={false}
            />
          </div>
        </div>
      ) : (
        <div className="text-center p-6">
          <span className="text-gray-500">获取调试信息失败</span>
        </div>
      )}
    </Modal>
  );
}

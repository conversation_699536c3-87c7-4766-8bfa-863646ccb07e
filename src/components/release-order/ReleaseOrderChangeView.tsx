'use client';

import { useEffect, useState } from 'react';
import { Spin } from 'antd';
import services from '@/services/orange-be';
import ParameterDiffView from '@/components/parameter/ParameterDiffView';

const { ReleaseOrderController } = services;

interface ReleaseOrderChangeViewProps {
    releaseVersion: string;
}

export default function ReleaseOrderChangeView({ releaseVersion }: ReleaseOrderChangeViewProps) {
    const [changes, setChanges] = useState<any>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        (async () => {
            try {
                setLoading(true);
                const { data } = await ReleaseOrderController.getChanges({
                    releaseVersion,
                });
                setChanges(data);
            } catch (error) {
                console.error('Failed to fetch changes:', error);
            } finally {
                setLoading(false);
            }
        })();
    }, [releaseVersion]);

    if (loading) {
        return (
          <div className="p-10 flex justify-center">
            <Spin size="large" tip="加载中..." />
          </div>
        );
    }

    // Transform the data to match the diff view requirements
    const transformData = () => {
        // Create a map of condition names from conditionChanges and previousNamespace.conditionSnapshots
        const conditionNameMap = new Map();
        changes?.conditionChanges?.forEach((cond: any) => {
            if (cond.conditionId && cond.name) {
                conditionNameMap.set(cond.conditionId, cond.name);
            }
        });
        changes?.previousNamespace?.conditionSnapshots?.forEach((cond: any) => {
            if (cond.conditionId && cond.name) {
                conditionNameMap.set(cond.conditionId, cond.name);
            }
        });

        // Sort conditions according to conditionsOrder
        const sortConditions = (conditions: any[], conditionsOrder?: string) => {
            if (!conditions) return conditions;

            // Split conditions into default (*) and non-default conditions
            const defaultCondition = conditions.find(cond => cond.conditionId === '*');
            const nonDefaultConditions = conditions.filter(cond => cond.conditionId !== '*');

            if (!conditionsOrder) {
                return defaultCondition
                    ? [...nonDefaultConditions, defaultCondition]
                    : nonDefaultConditions;
            }

            const orderMap = new Map(
                conditionsOrder.split(',').map((id, index) => [id, index]),
            );

            const sortedNonDefault = [...nonDefaultConditions].sort((a, b) => {
                const orderA = orderMap.get(a.conditionId) ?? Number.MAX_SAFE_INTEGER;
                const orderB = orderMap.get(b.conditionId) ?? Number.MAX_SAFE_INTEGER;
                return orderA - orderB;
            });

            return defaultCondition
                ? [...sortedNonDefault, defaultCondition]
                : sortedNonDefault;
        };

        const parameterChanges = changes?.parameterChanges?.map((param: any) => {
            // Get all conditions from previousNamespace.parameterSnapshots for this parameter
            const previousParam = changes?.previousNamespace?.parameterSnapshots?.find(
                (p: any) => p.parameterKey === param.parameterKey,
            );

            // For original conditions (left side), use previousParam's conditions
            const originalConditions = sortConditions(
                previousParam?.parameterConditions?.map((cond: any) => ({
                    conditionId: cond.conditionId || '',
                    conditionName: conditionNameMap.get(cond.conditionId) || cond.conditionName || '默认',
                    value: cond.value || '',
                    color: cond.conditionColor || '',
                })) || [],
                previousParam?.conditionsOrder,
            );

            // For changed conditions (right side), combine both states
            const allConditions = new Map();

            // Add conditions from previous state
            previousParam?.parameterConditions?.forEach((cond: any) => {
                allConditions.set(cond.conditionId, {
                    conditionId: cond.conditionId || '',
                    conditionName: conditionNameMap.get(cond.conditionId) || cond.conditionName || '默认',
                    value: cond.value || '',
                    color: cond.conditionColor || '',
                    changeType: undefined,
                });
            });

            // Add or update conditions from current state
            param.parameterConditions?.forEach((cond: any) => {
                allConditions.set(cond.conditionId, {
                    conditionId: cond.conditionId || '',
                    conditionName: conditionNameMap.get(cond.conditionId) || cond.conditionName || '默认',
                    value: cond.value || '',
                    color: cond.conditionColor || '',
                    changeType: cond.changeType,
                });
            });

            const changedConditions = sortConditions(
                Array.from(allConditions.values()),
                param.conditionsOrder,
            );

            return {
                parameterKey: param.parameterKey || '',
                changeType: param.changeType,
                valueType: param.valueType || '',
                originalConditions,
                changedConditions,
            };
        }) || [];

        // Sort parameterChanges to put new parameters at the end
        const sortedParameterChanges = [...parameterChanges].sort((a, b) => {
            if (a.changeType === 'CREATE' && b.changeType !== 'CREATE') return 1;
            if (a.changeType !== 'CREATE' && b.changeType === 'CREATE') return -1;
            return 0;
        });

        const conditionChanges = changes?.conditionChanges?.map((cond: any) => ({
            id: cond.conditionId || '',
            name: cond.name || '',
            color: cond.color || '',
            expression: cond.expression,
            changeType: cond.changeType || 'CREATE',
        })) || [];

        return {
            parameterChanges: sortedParameterChanges,
            conditionChanges,
        };
    };

    const {
        parameterChanges,
        conditionChanges,
    } = transformData();

    return (
      <ParameterDiffView
        parameterChanges={parameterChanges}
        conditionChanges={conditionChanges}
        loading={loading}
        showVersionInfo
        previousVersion={changes?.previousNamespace?.namespaceVersion || ''}
        previousNamespace={changes?.previousNamespace}
      />
    );
}

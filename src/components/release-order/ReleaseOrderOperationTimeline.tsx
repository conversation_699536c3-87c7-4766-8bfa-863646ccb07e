'use client';

// 操作记录数据
import { useEffect, useState } from 'react';
import moment from 'moment';

import services from '@/services/orange-be';
import { Avatar, Empty, Tag } from 'antd';
import { getFormatDate } from '@/utils/common';
import { calculateApplyReleaseStatus } from '@/utils/releaseOrder';
import { FileTextOutlined } from '@ant-design/icons';
import { CMD_LABEL_MAP } from './detail/workflow/SmallFlowGrayWorkflowPanel';
import ReleaseOrderOperationDTO = API.ReleaseOrderOperationDTO;
import OperationType = API.OperationType;

const { ReleaseOrderController, UserController } = services;

const operationTypeMap: Record<OperationType, string> = {
  APPLY_RELEASE: '申请发布',
  SMALLFLOW_GRAY: '小流量灰度',
  RATIO_GRAY: '百分比发布',
  START_VERIFY: '发起验证',
  VERIFY_REPLY: '验证反馈',
  RELEASE: '正式发布',
  CANCEL: '取消发布',
  SKIP: '跳过',
};

const skipTypeMap: Record<string, string> = {
  APPLY_RELEASE: '申请发布',
  SMALLFLOW_GRAY: '小流量灰度',
  VERIFY: '人工验证',
  RATIO_GRAY: '百分比发布',
  RELEASE: '正式发布',
  ALL: '全部',
};

// 获取操作类型的显示文本
const getOperationTitle = ({ type }): string => {
  return operationTypeMap[type] || type; // 如果没有映射则返回原始值
};

const getOperationDescription = (operation: ReleaseOrderOperationDTO): React.ReactNode => {
  const { type, params, result, status } = operation;
  if (type === 'RATIO_GRAY') {
    return `灰度比例: ${(JSON.parse(params as any)?.grayRatio * 100) / 100000}%`;
  }
  if (type === 'SMALLFLOW_GRAY') {
    return `操作：${
      CMD_LABEL_MAP[JSON.parse(params as any)?.taskCmd || JSON.parse(params as any)] || params
    }`;
  }
  if (type === 'APPLY_RELEASE') {
    const paramsObj = JSON.parse(params || '{}');
    const resultObj = JSON.parse(result || '{}');
    const cfStatus = calculateApplyReleaseStatus(resultObj);

    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <span
              className={paramsObj.releaseLevel === 'EMERGENT' ? 'text-red-500' : 'text-gray-800'}
            >
              {paramsObj.releaseLevel === 'EMERGENT' ? '紧急发布' : '普通发布'}
            </span>
          </div>
          {status && (
            <div className="flex items-center">
              <Tag
                style={{ margin: 0 }}
                color={
                  cfStatus === 'SUCCESS'
                    ? 'green'
                    : cfStatus === 'FAILED'
                    ? 'red'
                    : resultObj.orderDetailUrl
                    ? 'blue'
                    : 'yellow'
                }
              >
                {cfStatus === 'SUCCESS'
                  ? '审批通过'
                  : cfStatus === 'FAILED'
                  ? '审批拒绝'
                  : cfStatus === 'CANCELED'
                  ? '审批取消'
                  : resultObj.orderDetailUrl
                  ? '审批中'
                  : '待提交'}
              </Tag>
            </div>
          )}
          <div className="flex items-center">
            <FileTextOutlined />
            <span className="ml-2 text-gray-500">{paramsObj.reason}</span>
          </div>
        </div>
        {resultObj.applyOrderUrl && (
          <a
            href={resultObj.orderDetailUrl || resultObj.applyOrderUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline ml-4"
          >
            查看申请单
          </a>
        )}
      </div>
    );
  }
  if (type === 'VERIFY_REPLY') {
    const { verifyStatus, verifyMessage } = JSON.parse(params || '{}');
    return (
      <div className="flex items-center justify-between ">
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <Tag style={{ margin: 0 }} color={verifyStatus === 'PASS' ? 'green' : 'red'}>
              {verifyStatus === 'PASS' ? '验证通过' : '验证不通过'}
            </Tag>
          </div>
          <div className="flex items-center">
            <FileTextOutlined />
            <span className="ml-1 text-gray-500">{verifyMessage}</span>
          </div>
        </div>
      </div>
    );
  }
  if (type === 'SKIP') {
    const { reason, skipType } = JSON.parse(params as any);
    return `跳过环节：${skipTypeMap[skipType] || skipType}；跳过原因：${reason}`;
  }
  return params || result || '';
};

interface ReleaseOrderOperationTimelineProps {
  releaseVersion: string;
}

export default function ReleaseOrderOperationTimeline({
  releaseVersion,
}: ReleaseOrderOperationTimelineProps) {
  const [operations, setOperations] = useState<ReleaseOrderOperationDTO[]>([]);
  const [userMap, setUserMap] = useState<Record<string, any>>({});

  useEffect(() => {
    (async () => {
      try {
        const { data } = await ReleaseOrderController.getOperations({
          releaseVersion,
        });
        setOperations(data);
        const workNoList = Array.from(new Set(data.map(item => item.creator)));
        if (workNoList.length) {
          const result = await UserController.query({ workNoList: workNoList.join(',') });
          if (result?.data) {
            setUserMap(result.data);
          }
        }
      } catch (error) {
        console.error('Failed to fetch changes:', error);
      }
    })();
  }, [releaseVersion]);

  // 按月份分组操作记录
  const groupedOperations = operations.reduce((groups, operation) => {
    const date = moment(operation.gmtCreate);
    const monthKey = date.format('YYYY 年 M 月');

    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }

    groups[monthKey].push(operation);
    return groups;
  }, {} as Record<string, ReleaseOrderOperationDTO[]>);

  return (
    <div className="p-4 flex justify-center">
      <div className="max-w-3xl w-full">
        {Object.keys(groupedOperations).length === 0 ? (
          <div className="flex justify-center items-center h-40">
            <Empty />
          </div>
        ) : (
          Object.entries(groupedOperations).map(([month, monthOperations]) => (
            <div key={month} className="mb-8">
              <div className="border-b pb-2 mb-4">
                <h3 className="text-base font-medium text-gray-500">{month}</h3>
              </div>

              <div className="relative">
                {/* 时间线垂直线 */}
                <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 z-0" />

                {/* 操作记录项 */}
                {monthOperations.map((operation, index) => (
                  <div key={index} className="flex mb-6 relative z-10">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                        <Avatar
                          size={48}
                          shape="circle"
                          src={`https://work.alibaba-inc.com/photo/${operation.creator}.jpg?`}
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center mb-1">
                        <span className="font-medium mr-2">
                          {userMap[operation.creator!]?.displayName || operation.creator}
                        </span>
                        <span className="text-gray-500 text-sm  mr-2">
                          创建于 {getFormatDate(operation.gmtCreate!)}
                        </span>
                        {operation.gmtCreate !== operation.gmtModified && (
                          <span className="text-gray-500 text-sm">
                            更新于 {getFormatDate(operation.gmtModified!)}
                          </span>
                        )}
                      </div>
                      <div className="bg-white p-4 rounded-lg border">
                        <div className="font-medium">
                          {getOperationTitle({ type: operation.type! })}
                        </div>
                        {(operation.params || operation.result) && (
                          <div className="text-gray-600 mt-2 break-words">
                            {getOperationDescription(operation)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

'use client';

import moment from 'moment';
import { useReleaseOrderStore } from '@/store';
import { Space, Tag, Tooltip, Typography } from 'antd';
import {
  BranchesOutlined,
  FolderOpenOutlined,
  MobileOutlined,
  QrcodeOutlined,
  SlidersOutlined,
} from '@ant-design/icons';
import { getTBAppName } from '@/utils/common';
import { useState } from 'react';
import QRCodeModal from '../QRCodeModal';
import { useConditionNames } from '@/hooks/useConditionNames';
import { User } from '@/components';

const { Title, Text } = Typography;

export default function ReleaseOrderDetailInfo() {
  const releaseOrderDetail = useReleaseOrderStore(state => state.releaseOrder);
  const [isQRCodeModalVisible, setIsQRCodeModalVisible] = useState(false);
  const isFinished =
    releaseOrderDetail.status === 'RELEASED' || releaseOrderDetail.status === 'CANCELED';

  const { conditionNames, loading: conditionNamesLoading } = useConditionNames({
    conditionIds: releaseOrderDetail.conditionIds,
  });

  const showQRCodeModal = () => {
    setIsQRCodeModalVisible(true);
  };

  const hideQRCodeModal = () => {
    setIsQRCodeModalVisible(false);
  };

  return (
    <div className="space-y-4">
      <div>
        <Title
          level={4}
          className="mb-4"
          style={{
            display: 'inline',
            whiteSpace: 'normal',
            wordBreak: 'break-all',
          }}
        >
          <span
            style={{
              display: 'inline-flex',
              alignItems: 'center',
            }}
          >
            {releaseOrderDetail.description}
            {releaseOrderDetail.status === 'CANCELED' && (
              <Tag
                color="red"
                style={{
                  marginLeft: 4,
                  padding: '0 6px',
                  height: 22,
                  lineHeight: '22px',
                  fontSize: 14,
                  display: 'inline-flex',
                  alignItems: 'center',
                }}
              >
                发布取消
              </Tag>
            )}
          </span>
        </Title>
        <Space className="text-gray-600 flex text-sm mt-2 items-center">
          <User empIds={[releaseOrderDetail.creator!]} showAvatar size={'xsmall'} />
          <Text>创建于 {moment(releaseOrderDetail.gmtCreate).format('YYYY-MM-DD HH:mm')}</Text>
        </Space>
      </div>

      <div className="space-y-2 border-t pt-4">
        <div className="flex items-center space-x-2">
          <MobileOutlined className="text-gray-500" />
          <Text className="text-sm break-words">
            {releaseOrderDetail.appKey}-{getTBAppName(releaseOrderDetail.appKey!)}
          </Text>
        </div>
        <div className="flex items-center space-x-2">
          <FolderOpenOutlined className="text-gray-500" />
          <Text className="text-sm">{releaseOrderDetail.namespaceName}</Text>
        </div>
        <div className="flex items-center space-x-2">
          <QrcodeOutlined
            className="text-gray-500 cursor-pointer hover:text-blue-500"
            onClick={showQRCodeModal}
          />
          <span className="text-sm cursor-pointer hover:text-blue-500" onClick={showQRCodeModal}>
            扫码单机验证
          </span>
        </div>

        {releaseOrderDetail.parameterKeys?.map((key, index) => (
          <div key={index} className="flex items-center space-x-2">
            {index === 0 ? (
              <Tooltip title="本次发布影响的参数">
                <SlidersOutlined className="text-gray-500" />
              </Tooltip>
            ) : (
              <div className="w-4 h-4" />
            )}
            <Text className="text-sm">{key}</Text>
          </div>
        ))}

        {releaseOrderDetail.conditionIds?.map((conditionId, index) => (
          <div key={conditionId} className="flex items-center space-x-2">
            {index === 0 ? (
              <Tooltip title="本次发布影响的条件">
                <BranchesOutlined className="text-gray-500" />
              </Tooltip>
            ) : (
              <div className="w-4 h-4" />
            )}
            <Text className="text-sm">
              {conditionNamesLoading ? conditionId : conditionNames[conditionId] || conditionId}
            </Text>
          </div>
        ))}
      </div>

      <QRCodeModal
        open={isQRCodeModalVisible}
        onCancel={hideQRCodeModal}
        releaseVersion={releaseOrderDetail.releaseVersion!}
        appKey={releaseOrderDetail.appKey!}
        isFinished={isFinished}
      />
    </div>
  );
}

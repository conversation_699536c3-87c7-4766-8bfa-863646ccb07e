import { Form, Input, Modal, Select } from 'antd';
import { useEffect } from 'react';
import { getRemainingIncompleteStages } from '@/utils/releaseOrder';

interface SkipWorkflowModalProps {
  visible: boolean;
  onOk: (values: any) => void;
  onCancel: () => void;
  loading: boolean;
  releaseOrderStages?: API.ReleaseOrderStageDTO[];
}

const SkipWorkflowModal: React.FC<SkipWorkflowModalProps> = ({
  visible,
  onOk,
  onCancel,
  loading,
  releaseOrderStages = [],
}) => {
  const [form] = Form.useForm();

  // 当弹窗打开时重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 动态获取剩余未完成的阶段列表
  const availableStages = getRemainingIncompleteStages(releaseOrderStages);

  const handleOk = () => {
    form
      .validateFields()
      .then(values => {
        onOk(values);
      })
      .catch(error => {
        console.error(error);
      });
  };

  return (
    <Modal
      maskClosable={false}
      title="跳过流程"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="stage"
          label="跳过环节"
          rules={[
            {
              required: true,
              message: '请选择要跳过的环节',
            },
          ]}
        >
          <Select placeholder="请选择要跳过的环节">
            {availableStages.map(stage => (
              <Select.Option key={stage.value} value={stage.value}>
                {stage.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="reason"
          label="跳过原因"
          rules={[
            {
              required: true,
              message: '请输入跳过原因',
            },
          ]}
        >
          <Input.TextArea rows={3} placeholder="请输入跳过原因" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SkipWorkflowModal;

'use client';

import { Button, Layout } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { WorkflowDetailSider } from '@ali/mc-uikit';
import ReleaseOrderDetailInfo from './ReleaseOrderDetailInfo';
import { useReleaseOrderStore } from '@/store';
import { getCurrentActiveWorkflowStage, getStageByType } from '@/utils/releaseOrder';
import { useEffect, useState } from 'react';

const { Sider } = Layout;

type WorkflowStageItem = {
  key: string;
  title: string;
  status?: 'finish' | 'process' | 'waiting';
};

export const WORKFLOW_STAGES: WorkflowStageItem[] = [
  {
    key: 'beta',
    title: '发布准入',
  },
  {
    key: 'smallflow-gray',
    title: '小流量灰度',
  },
  {
    key: 'gray',
    title: '分批发布',
  },
];

const ReleaseOrderDetailSider = () => {
  const [collapsed, setCollapsed] = useState(false);

  const releaseOrder = useReleaseOrderStore(state => state.releaseOrder);
  const mainWorkflowStage = useReleaseOrderStore(state => state.mainWorkflowStage);
  const updateWorkflowStage = useReleaseOrderStore(state => state.updateWorkflowStage);

  // 根据 releaseOrderStages 确定当前活跃的工作流节点
  const calculatedActiveStage = getCurrentActiveWorkflowStage(releaseOrder.releaseOrderStages);

  // 当发布单版本变化时，重新初始化工作流阶段到当前活跃阶段
  useEffect(() => {
    if (releaseOrder.releaseVersion && releaseOrder.releaseOrderStages) {
      if (calculatedActiveStage === 'beta') {
        const applyReleaseStage = getStageByType(releaseOrder.releaseOrderStages, 'APPLY_RELEASE');
        if (applyReleaseStage?.status !== 'INIT') {
          updateWorkflowStage({
            mainWorkflowStage: 'beta',
            subWorkflowStage: 'apply',
          });
          return;
        }
      }

      updateWorkflowStage({
        mainWorkflowStage: calculatedActiveStage,
        subWorkflowStage: calculatedActiveStage,
      });
    }
  }, [releaseOrder.releaseVersion, calculatedActiveStage, updateWorkflowStage]);

  // 使用 releaseOrderStages 数据来计算工作流状态
  const getWorkflowStages = (): WorkflowStageItem[] => {
    const stages = releaseOrder.releaseOrderStages || [];

    // 创建阶段类型到工作流节点的映射
    const stageTypeToWorkflowKey: Record<string, string> = {
      APPLY_RELEASE: 'beta',
      SMALLFLOW_GRAY: 'smallflow-gray',
      VERIFY: 'smallflow-gray', // 验证阶段归属到小流量灰度
      RATIO_GRAY: 'gray',
      RELEASE: 'gray',
    };

    return WORKFLOW_STAGES.map(workflowStage => {
      // 找到对应的 releaseOrderStage
      // find 最后一个是确保该节点的所有阶段都完成了才算完成，以最后一个阶段为准
      const matchingStage = stages?.findLast(
        stage => stageTypeToWorkflowKey[stage.type!] === workflowStage.key,
      );

      if (!matchingStage) {
        // 如果没有找到对应的阶段，根据发布单整体状态判断
        if (releaseOrder.status === 'RELEASED' || releaseOrder.status === 'CANCELED') {
          return {
            ...workflowStage,
            status: 'finish' as const,
          };
        }
        return {
          ...workflowStage,
          status: 'waiting' as const,
        };
      }

      // 根据阶段状态映射工作流状态
      switch (matchingStage.status) {
        case 'SUCCESS':
        case 'SKIPPED':
          return {
            ...workflowStage,
            status: 'finish' as const,
          };
        case 'IN_PROGRESS':
          return {
            ...workflowStage,
            status: 'process' as const,
          };
        default:
          return {
            ...workflowStage,
            status: 'waiting' as const,
          };
      }
    });
  };

  const workflowStages = getWorkflowStages();

  return (
    <Sider
      width={350}
      collapsed={collapsed}
      collapsedWidth={30}
      className="bg-white border-r border-gray-200 transition-all duration-300 relative"
      trigger={null}
    >
      <div className="h-full flex flex-col">
        <div className="absolute -right-3 top-24 z-10">
          <Button
            shape="circle"
            icon={collapsed ? <RightOutlined /> : <LeftOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="shadow-md"
          />
        </div>
        {!collapsed && (
          <div className="p-6 border-gray-200 relative">
            <WorkflowDetailSider
              prefixAddon={<ReleaseOrderDetailInfo />}
              stages={workflowStages}
              currentStage={mainWorkflowStage}
              onStageChange={key => {
                updateWorkflowStage({
                  mainWorkflowStage: key,
                  subWorkflowStage: key,
                });
              }}
            />
          </div>
        )}
      </div>
    </Sider>
  );
};

export default ReleaseOrderDetailSider;

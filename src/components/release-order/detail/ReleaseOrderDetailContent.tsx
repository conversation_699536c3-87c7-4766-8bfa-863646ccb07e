import RatioGrayWorkflowPanel from './workflow/RatioGrayWorkflowPanel';
import SmallFlowGrayWorkflowPanel from './workflow/SmallFlowGrayWorkflowPanel';
import { useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { useReleaseOrderStore } from '@/store';

import services from '@/services/orange-be';
import BetaWorkflowPanel from '@/components/release-order/detail/workflow/BetaWorkflowPanel';
import SkipWorkflowModal from './SkipWorkflowModal';
import ReleaseOrderStageCoordinator from '@/components/release-order/detail/workflow/ReleaseOrderStageCoordinator';

const { ReleaseOrderController } = services;

const ReleaseOrderDetailContent = () => {
  const releaseOrder = useReleaseOrderStore(state => state.releaseOrder);
  const hasNsEditPermission = useReleaseOrderStore(state => state.hasNsEditPermission);
  const mainWorkflowStage = useReleaseOrderStore(state => state.mainWorkflowStage);
  const updateReleaseOrder = useReleaseOrderStore(state => state.fetchAndUpdateReleaseOrder);

  const isFinished = releaseOrder.status === 'RELEASED' || releaseOrder.status === 'CANCELED';

  const [skipModalVisible, setSkipModalVisible] = useState(false);
  const [skipLoading, setSkipLoading] = useState(false);

  const handleCancelRelease = async () => {
    try {
      await ReleaseOrderController.cancel({
        releaseVersion: releaseOrder.releaseVersion,
      });
      message.success('取消发布成功');
      await updateReleaseOrder(releaseOrder.releaseVersion!);
    } catch (e) {
      message.error(`取消发布失败：${e.message}`);
    }
  };

  const handleSkipWorkflow = async ({ stage, reason }) => {
    setSkipLoading(true);
    try {
      await ReleaseOrderController.skip(
        { releaseVersion: releaseOrder.releaseVersion },
        {
          skipType: stage,
          reason,
        },
      );
      message.success('跳过流程成功');
      setSkipModalVisible(false);
      await updateReleaseOrder(releaseOrder.releaseVersion!);
    } catch (e) {
      message.error(`跳过流程失败：${e.message}`);
    } finally {
      setSkipLoading(false);
    }
  };

  return (
    <div className="px-6 py-4 space-y-4">
      {/* 阶段流水线 */}
      {'beta' === mainWorkflowStage && <BetaWorkflowPanel />}
      {'smallflow-gray' === mainWorkflowStage && <SmallFlowGrayWorkflowPanel />}
      {'gray' === mainWorkflowStage && <RatioGrayWorkflowPanel />}

      {/* 逆向操作按钮 */}
      {!isFinished && hasNsEditPermission && (
        <div className="flex justify-end mb-4">
          <Popconfirm
            title={'取消发布将同时回滚已应用此变更的设备上的相关设置。确定要取消发布吗？'}
            onConfirm={handleCancelRelease}
          >
            <Button danger>取消发布</Button>
          </Popconfirm>
          {!isFinished && (
            // fixme: 暂时对所有人开放，待功能稳定
            // isSystemAdmin() &&
            <Button className="ml-2" onClick={() => setSkipModalVisible(true)}>
              跳过流程
            </Button>
          )}
        </div>
      )}

      <SkipWorkflowModal
        visible={skipModalVisible}
        onOk={handleSkipWorkflow}
        onCancel={() => setSkipModalVisible(false)}
        loading={skipLoading}
        releaseOrderStages={releaseOrder.releaseOrderStages}
      />

      {/* 阶段详情 */}
      <ReleaseOrderStageCoordinator />
    </div>
  );
};

export default ReleaseOrderDetailContent;

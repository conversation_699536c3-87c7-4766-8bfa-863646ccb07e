import { Button, Card, Dropdown, Empty, message, Popconfirm, Steps, Tag } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  MoreOutlined,
  PauseCircleOutlined,
} from '@ant-design/icons';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import services from '@/services/orange-be';
import { useReleaseOrderStore } from '@/store';
import { getStageByType } from '@/utils/releaseOrder';
import moment from 'moment/moment';

const { ReleaseOrderController } = services;

export const CMD_LABEL_MAP = {
  START: '开始',
  RETRY_STAGE: '重试当前节点',
  RESUME: '继续任务',
  PAUSE: '暂停任务',
  SKIP_TASK: '跳过所有节点',
  SKIP_STAGE: '跳过当前节点',
} as const;

type CommandKey = keyof typeof CMD_LABEL_MAP;

// 状态展示映射
const STAGE_STATUS_LABEL_MAP: Record<string, string> = {
  INIT: '已创建',
  RUNNING: '运行中',
  PAUSED: '暂停',
  SUCCESS: '成功',
  FAILED: '失败',
  TIMEOUT: '超时完成',
  CANCELED: '取消',
  SKIPPED: '跳过',
};

const getStageStatusDisplay = (status?: string): string => {
  if (!status) return '';
  return STAGE_STATUS_LABEL_MAP[status] || status;
};

type TigaStageMeta = {
  name?: string;
  circleType?: 'WHITELIST' | string;
  deviceCnt?: number;
  grayMaxSeconds?: number;
  triggerGapSeconds?: number;
};

const primaryCmdList: CommandKey[] = ['START', 'PAUSE', 'RESUME', 'RETRY_STAGE'];

type TigaStage = {
  id?: number;
  status?:
    | 'INIT'
    | 'RUNNING'
    | 'PAUSED'
    | 'SUCCESS'
    | 'FAILED'
    | 'TIMEOUT'
    | 'CANCELED'
    | 'SKIPPED'
    | string;
  current?: boolean;
  circleStartTime?: number;
  meta?: TigaStageMeta;
  deviceStatistics?: {
    totalSendCnt?: number;
  };
};

const SmallFlowGrayWorkflowPanel = () => {
  const hasNsEditPermission = useReleaseOrderStore(state => state.hasNsEditPermission);
  const releaseOrder = useReleaseOrderStore(state => state.releaseOrder);
  const updateReleaseOrderStore = useReleaseOrderStore(state => state.update);
  const fetchAndUpdateReleaseOrder = useReleaseOrderStore(
    state => state.fetchAndUpdateReleaseOrder,
  );
  const mainWorkflowStage = useReleaseOrderStore(state => state.mainWorkflowStage);
  const smallFlowGrayRefreshTrigger = useReleaseOrderStore(
    state => state.smallFlowGrayRefreshTrigger,
  );
  const [taskStageList, setTaskStageList] = useState<TigaStage[]>([]);
  const [templateStageList, setTemplateStageList] = useState<TigaStage[]>([]);
  const [cmdList, setCmdList] = useState<CommandKey[]>([]);
  const timerRef = useRef<number | null>(null);
  const isMountedRef = useRef(false);

  const isFinished = useMemo(
    () => releaseOrder.status === 'RELEASED' || releaseOrder.status === 'CANCELED',
    [releaseOrder.status],
  );
  const currentStage = useMemo(() => taskStageList.find(stage => stage.current), [taskStageList]);

  // 判断小流量灰度是否在进行中
  const isSmallFlowGrayInProgress = useMemo(() => {
    return (
      getStageByType(releaseOrder.releaseOrderStages, 'SMALLFLOW_GRAY')?.status === 'IN_PROGRESS'
    );
  }, [releaseOrder.releaseOrderStages]);

  const fetchNodeData = useCallback(async () => {
    // 检查组件是否仍然挂载
    if (!isMountedRef.current || !releaseOrder.releaseVersion) return;

    try {
      const tigaResponse = await ReleaseOrderController.getTigaTaskStageList({
        releaseVersion: releaseOrder.releaseVersion,
      });

      // 再次检查组件是否仍然挂载
      if (!isMountedRef.current) return;

      if (tigaResponse.data && Object.keys(tigaResponse.data).length > 0) {
        setTemplateStageList([]);
        setTaskStageList((tigaResponse.data?.stages || []) as TigaStage[]);
        // 只展示部分可操作的命令
        const filteredCmds = (tigaResponse.data?.cmdList || []).filter(
          (cmd: string): cmd is CommandKey => cmd in CMD_LABEL_MAP,
        );
        setCmdList(filteredCmds);
      } else {
        const templatesResponse = await ReleaseOrderController.getGrayTemplates({
          releaseVersion: releaseOrder.releaseVersion,
        });
        // 检查组件是否仍然挂载
        if (!isMountedRef.current) return;

        updateReleaseOrderStore({ templateId: templatesResponse?.data?.[0]?.templateId || 0 });
        setTemplateStageList((templatesResponse?.data?.[0]?.stageList || []) as TigaStage[]);
        setTaskStageList([]);
        setCmdList([]);
      }
    } catch (error) {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current) return;

      console.error('未获取到节点信息');
      setTaskStageList([]);
      setCmdList([]);
      setTemplateStageList([]);
    }
  }, [releaseOrder.releaseVersion, updateReleaseOrderStore]);

  useEffect(() => {
    // 设置组件挂载状态
    isMountedRef.current = true;

    if (mainWorkflowStage === 'smallflow-gray') {
      fetchNodeData();
    }

    // 组件卸载时清理
    return () => {
      isMountedRef.current = false;
    };
  }, [mainWorkflowStage, fetchNodeData, smallFlowGrayRefreshTrigger]);

  // 定时器管理：当小流量灰度在进行中时，每5秒刷新一次
  useEffect(() => {
    // 清理之前的定时器
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // 如果小流量灰度在进行中，启动定时器
    if (isSmallFlowGrayInProgress) {
      timerRef.current = window.setInterval(() => {
        fetchNodeData();
      }, 5000); // 5秒
    }

    // 组件卸载或依赖变化时清理定时器
    return () => {
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isSmallFlowGrayInProgress, mainWorkflowStage, fetchNodeData]);

  const handleCommand = async (cmd: CommandKey, currentStageId?: number) => {
    try {
      await ReleaseOrderController.tigaGray(
        { releaseVersion: releaseOrder.releaseVersion! },
        {
          taskCmd: cmd as any,
          skipStageId: cmd === 'SKIP_STAGE' ? currentStageId : undefined,
        },
      );
      message.success(`${CMD_LABEL_MAP[cmd] || cmd}操作成功`);
      await fetchNodeData();
      setTimeout(async () => {
        // 延时1秒，刷新整体工单信息，确保状态及时更新
        await fetchAndUpdateReleaseOrder(releaseOrder.releaseVersion!);
      }, 1000);
    } catch (e) {
      const errorText = e instanceof Error ? e.message : '未知错误';
      message.error(`${CMD_LABEL_MAP[cmd] || cmd}操作失败：${errorText}`);
    }
  };

  const getNodeStatus = (stage?: TigaStage) => {
    switch (stage?.status) {
      case 'FAILED':
        return 'error';
      case 'INIT':
        return 'wait';
      case 'RUNNING':
        return 'process';
      default:
        return null;
    }
  };

  const getNodeIcon = (stage?: TigaStage) => {
    // 如果有 status 则使用对应 status 的默认展示，否则才使用自定义 icon
    if (getNodeStatus(stage)) return null;

    switch (stage?.status) {
      case 'CANCELED':
      case 'TIMEOUT':
        return <ClockCircleOutlined title="灰度超时" style={{ color: '#1677FF' }} />;
      case 'SUCCESS':
        return <CheckCircleOutlined title="灰度成功" style={{ color: '#1677FF' }} />;
      case 'PAUSED':
        return <PauseCircleOutlined title="暂停灰度" style={{ color: '#1677FF' }} />;
      case 'SKIPPED':
        return <MinusCircleOutlined title="跳过灰度" style={{ color: '#1677FF' }} />;
      default:
        return null;
    }
  };

  const generateSteps = () => {
    const stages = taskStageList.length > 0 ? taskStageList : templateStageList;

    if (stages.length === 0) return [];

    return stages.map((stage, index) => {
      const meta = stage.meta || {};
      const deviceStatistics = stage.deviceStatistics || {};
      const { circleType, deviceCnt, grayMaxSeconds } = meta;

      const grayTypeText = circleType === 'WHITELIST' ? '白名单灰度' : '定量灰度';
      const grayInfo = `${grayTypeText} ${deviceCnt || ''}`.trim();

      let durationText = '';
      if (stage.status === 'RUNNING' && meta.triggerGapSeconds) {
        durationText = `预计时长: ${Math.floor(meta.triggerGapSeconds / 60)}分钟`;
      } else if (grayMaxSeconds) {
        durationText = `预计时长: ${Math.floor(grayMaxSeconds / 60)}分钟`;
      }

      let startTime = '';
      if (stage.circleStartTime) {
        startTime = `圈选开始: ${moment(stage.circleStartTime).format('MM-DD HH:mm:ss')}`;
      }

      // 圈选设备数
      let process = '';
      if (
        deviceStatistics &&
        deviceStatistics.totalSendCnt !== null &&
        deviceStatistics.totalSendCnt !== undefined
      ) {
        process = `圈选设备数: ${deviceStatistics.totalSendCnt}/${deviceCnt} (当前/目标)`;
      }

      const isCurrent = !!stage.current;
      const descColorClass = isCurrent ? 'text-gray-600' : 'text-gray-400';

      return (
        <Steps.Step
          key={stage.id || index}
          title={<span className={'text-gray-600 font-bold'}>{meta.name}</span>}
          icon={getNodeIcon(stage)}
          subTitle={
            stage.status ? (
              stage.status === 'RUNNING' ? (
                <Tag color="green" icon={<LoadingOutlined />}>
                  运行中
                </Tag>
              ) : (
                <Tag color="blue">{getStageStatusDisplay(stage.status)}</Tag>
              )
            ) : null
          }
          description={
            <div className={`text-xs whitespace-nowrap ${descColorClass}`}>
              {grayInfo && <div>{grayInfo}</div>}
              {startTime && <div>{startTime}</div>}
              {durationText && <div>{durationText}</div>}
              {process && <div>{process}</div>}
            </div>
          }
          status={getNodeStatus(stage) || 'wait'}
        />
      );
    });
  };

  const renderActionButtons = () => {
    if (cmdList.length === 0 || !hasNsEditPermission || isFinished) return null;

    const primaryCmds = cmdList.filter(cmd => (primaryCmdList as string[]).includes(cmd));
    const otherCmds = cmdList.filter(cmd => !(primaryCmdList as string[]).includes(cmd));

    const dropdownItems = otherCmds.map(cmd => ({
      key: cmd,
      label: (
        <Popconfirm
          title={`确定${CMD_LABEL_MAP[cmd] || cmd}？`}
          onConfirm={() => handleCommand(cmd, currentStage?.id)}
        >
          <span>{CMD_LABEL_MAP[cmd] || cmd}</span>
        </Popconfirm>
      ),
    }));

    return (
      <div className="flex justify-center mt-4 gap-2">
        {primaryCmds.map(cmd => (
          <Popconfirm
            key={cmd}
            title={`确定${CMD_LABEL_MAP[cmd] || cmd}？`}
            onConfirm={() => handleCommand(cmd, currentStage?.id)}
          >
            <Button type="primary" size="small" disabled={isFinished}>
              {CMD_LABEL_MAP[cmd] || cmd}
            </Button>
          </Popconfirm>
        ))}
        {otherCmds.length > 0 && (
          <Dropdown menu={{ items: dropdownItems }} placement="bottomLeft">
            <Button icon={<MoreOutlined />} size="small" disabled={isFinished}>
              更多
            </Button>
          </Dropdown>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <div className="relative">
        {taskStageList.length === 0 && templateStageList.length === 0 && <Empty />}
        <div
          className="overflow-x-auto"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          <div className="flex justify-center min-w-max px-4 py-2">
            {(() => {
              const stages = taskStageList.length > 0 ? taskStageList : templateStageList;
              const currentIndex = stages.findIndex(s => s.current);
              const minWidthPx = Math.max(1000, stages.length * 255);
              return (
                <Steps
                  size="small"
                  className="w-full"
                  style={{ minWidth: `${minWidthPx}px` }}
                  current={currentIndex >= 0 ? currentIndex : undefined}
                >
                  {generateSteps()}
                </Steps>
              );
            })()}
          </div>
        </div>
        {renderActionButtons()}
      </div>
    </Card>
  );
};

export default SmallFlowGrayWorkflowPanel;

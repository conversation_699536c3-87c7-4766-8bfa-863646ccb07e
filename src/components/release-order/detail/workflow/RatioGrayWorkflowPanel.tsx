import { Button, Card, message, Popconfirm, Steps, Tag, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { useReleaseOrderStore } from '@/store';
import { GRAY_RATIO_UNIT, SWITCH_NS_GRAY_RATIO_LIST } from '@/constants/record';
import React, { useCallback, useEffect, useState } from 'react';
import { getFormatDate } from '@/utils/common';

import services from '@/services/orange-be';
import { arePreStagesCompleted, getStageByType } from '@/utils/releaseOrder';

const { ReleaseOrderController } = services;

const RatioGrayWorkflowPanel = () => {
  const mainWorkflowStage = useReleaseOrderStore(state => state.mainWorkflowStage);
  const releaseOrder = useReleaseOrderStore(state => state.releaseOrder);
  const updateReleaseOrder = useReleaseOrderStore(state => state.fetchAndUpdateReleaseOrder);
  // 通过进度接口获取的节点信息
  const [ratioGrayProgressNodes, setRatioGrayProgressNodes] = useState<
    API.RatioGrayProgressDTORatioGrayProgressNode[]
  >([]);
  const [releaseProgressNode, setReleaseProgressNode] = useState<
    API.RatioGrayProgressDTOReleaseProgressNode | undefined
  >(undefined);
  const isFinished = releaseOrder.status === 'RELEASED' || releaseOrder.status === 'CANCELED';
  const hasNsEditPermission = useReleaseOrderStore(state => state.hasNsEditPermission);

  // 通过接口获取分批发布的进度信息
  const fetchRatioGrayProgress = useCallback(async () => {
    if (
      mainWorkflowStage !== 'gray' ||
      !releaseOrder.releaseVersion ||
      releaseProgressNode?.agatewareTaskInfo?.status === 'SUCCESS'
    ) return;
    try {
      const res = await ReleaseOrderController.getRatioGrayProgress({
        releaseVersion: releaseOrder.releaseVersion,
      });
      const progress = res?.data;
      setRatioGrayProgressNodes(progress?.ratioGrayProgressNodes || []);
      setReleaseProgressNode(progress?.releaseProgressNode);
    } catch (e) {
      // 静默失败，避免频繁报错干扰
    }
  }, [
    mainWorkflowStage,
    releaseOrder.releaseVersion,
    releaseProgressNode?.agatewareTaskInfo?.status,
  ]);

  // 首次进入灰度面板和发布状态变化时拉取一次进度
  useEffect(() => {
    fetchRatioGrayProgress();
  }, [fetchRatioGrayProgress]);

  // 每 5 秒刷新一次进展数据（仅在灰度面板且未结束时）
  useEffect(() => {
    if (
      mainWorkflowStage !== 'gray' ||
      releaseOrder.status === 'CANCELED' ||
      releaseProgressNode?.agatewareTaskInfo?.status === 'SUCCESS'
    ) return;

    const timer = setInterval(() => {
      fetchRatioGrayProgress();
    }, 5000);
    return () => clearInterval(timer);
  }, [
    mainWorkflowStage,
    fetchRatioGrayProgress,
    releaseOrder.status,
    releaseProgressNode?.agatewareTaskInfo,
  ]);

  const handleReleaseOrderGray = async (ratio: number) => {
    try {
      await ReleaseOrderController.ratioGray(
        { releaseVersion: releaseOrder.releaseVersion },
        { grayRatio: ratio },
      );
      message.success('灰度发布成功');
      await updateReleaseOrder(releaseOrder.releaseVersion!);
      await fetchRatioGrayProgress();
    } catch (e) {
      message.error(`灰度发布失败：${e.message}`);
    }
  };

  const handleFullRelease = async () => {
    try {
      await ReleaseOrderController.publish({ releaseVersion: releaseOrder.releaseVersion });
      message.success('发布成功');
      await updateReleaseOrder(releaseOrder.releaseVersion!);
      await fetchRatioGrayProgress();
    } catch (e: any) {
      message.error(`发布失败：${e.message}`);
    }
  };

  // 检查 RATIO_GRAY 阶段的前置条件
  const checkRatioGrayPreconditions = () => {
    const { releaseOrderStages } = releaseOrder;

    // 检查 RATIO_GRAY 前面的阶段是否都已完成
    const preStagesCompleted = arePreStagesCompleted(releaseOrderStages, 'RATIO_GRAY');

    // 检查 RATIO_GRAY 阶段是否未完成
    const ratioGrayStage = getStageByType(releaseOrderStages, 'RATIO_GRAY');

    return {
      preStagesCompleted,
      ratioGrayStatus: ratioGrayStage?.status,
    };
  };

  const { preStagesCompleted, ratioGrayStatus } = checkRatioGrayPreconditions();

  // 基于进度接口判断是否有任何节点在发布中（已提交/已调度/运行中但未完成）
  const hasAnyNodePublishing = useCallback(() => {
    const anyGray = (ratioGrayProgressNodes || []).some(node => {
      const taskStatus = node.agatewareTaskInfo?.status;
      if (taskStatus === 'SUCCESS') return false;
      if (taskStatus === 'RUNNING') return true;
      if (node.startTime || node.scheduleTime) return true; // 已提交/已调度但未完成
      return false;
    });
    if (anyGray) return true;

    const fullTaskStatus = releaseProgressNode?.agatewareTaskInfo?.status;
    if (fullTaskStatus === 'RUNNING') return true;
    if (
      fullTaskStatus !== 'SUCCESS' &&
      (releaseProgressNode?.startTime || releaseProgressNode?.scheduleTime)
    ) return true;
    return false;
  }, [ratioGrayProgressNodes, releaseProgressNode]);

  type StageStatus = 'pending' | 'submitted' | 'deploying' | 'completed' | 'failed' | 'skipped';
  type SubStageStatus = 'wait' | 'process' | 'finish' | 'error';

  interface SubStage {
    id: string;
    name: string;
    status: SubStageStatus;
    desc?: string;
  }

  interface DeploymentStageVM {
    id: string;
    name: string;
    percentage: number;
    status: StageStatus;
    subStages: SubStage[];
    canStart: boolean;
    onStart?: () => void;
    isFull?: boolean;
    disabledReason?: string;
  }

  const initialSubStages: SubStage[] = [
    {
      id: 'submitted',
      name: '提交发布',
      status: 'wait',
    },
    {
      id: 'deploying',
      name: '任务发布',
      status: 'wait',
    },
    {
      id: 'completed',
      name: '发布完成',
      status: 'wait',
    },
  ];

  const getStatusIcon = (status: StageStatus) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#8c8c8c' }} />;
      case 'submitted':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'deploying':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'skipped':
        return <MinusCircleOutlined style={{ color: '#8c8c8c' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  const getStatusTag = (status: StageStatus) => {
    const configs: Record<StageStatus, { color: any; text: string }> = {
      pending: {
        color: 'default',
        text: '待发布',
      },
      submitted: {
        color: 'warning',
        text: '已提交',
      },
      deploying: {
        color: 'processing',
        text: '发布中',
      },
      completed: {
        color: 'success',
        text: '已完成',
      },
      failed: {
        color: 'error',
        text: '失败',
      },
      skipped: {
        color: 'default',
        text: '跳过',
      },
    };
    const config = configs[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getRatioStartState = (ratio: number): { canStart: boolean; reason?: string } => {
    const ratioCompare = ratio - (releaseOrder.grayRatio || 0);
    if (ratioCompare <= 0) return {
        canStart: false,
        reason: '当前进度已到达或超过该比例',
      };
    if (isFinished) return {
        canStart: false,
        reason: '该发布单已结束',
      };
    if (!hasNsEditPermission) return {
        canStart: false,
        reason: '您没有该命名空间的编辑权限',
      };
    if (!preStagesCompleted) return {
        canStart: false,
        reason: '前置阶段未全部完成',
      };
    if (ratioGrayStatus === 'SKIPPED') return {
        canStart: false,
        reason: '该阶段已被跳过',
      };
    if (ratioGrayStatus === 'SUCCESS') return {
        canStart: false,
        reason: '该阶段已完成',
      };
    if (hasAnyNodePublishing()) return {
        canStart: false,
        reason: '有发布任务进行中',
      };
    return { canStart: true };
  };

  const getFullStartState = (): { canStart: boolean; reason?: string } => {
    if (isFinished) return {
        canStart: false,
        reason: '该发布单已结束',
      };
    if (!hasNsEditPermission) return {
        canStart: false,
        reason: '您没有该命名空间的编辑权限',
      };
    if (ratioGrayStatus !== 'SKIPPED' && ratioGrayStatus !== 'SUCCESS') return {
        canStart: false,
        reason: '百分比阶段尚未完成',
      };
    if (hasAnyNodePublishing()) return {
        canStart: false,
        reason: '有发布任务进行中',
      };
    return { canStart: true };
  };

  const buildStages = (): DeploymentStageVM[] => {
    const stages: DeploymentStageVM[] = [];
    const currentGrayRatio = ratioGrayProgressNodes?.length
      ? ratioGrayProgressNodes[ratioGrayProgressNodes.length - 1]?.grayRatio || 0
      : 0;

    // 将进度列表转为按比例检索的映射，便于构建阶段
    const nodesByRatio = new Map<number, API.RatioGrayProgressDTORatioGrayProgressNode>();
    (ratioGrayProgressNodes || []).forEach(n => {
      if (n.grayRatio != null) nodesByRatio.set(Number(n.grayRatio), n);
    });

    SWITCH_NS_GRAY_RATIO_LIST.filter(r => r !== 0).forEach(ratio => {
      const percentage = (ratio * 100) / GRAY_RATIO_UNIT;
      const node = nodesByRatio.get(ratio);
      let status: StageStatus = 'pending';

      if (node) {
        const taskStatus = node?.agatewareTaskInfo?.status;
        if (taskStatus === 'SUCCESS') status = 'completed';
        else if (taskStatus === 'RUNNING') status = 'deploying';
        else if (node.startTime) status = 'submitted';
        else status = 'pending';
      } else if (ratio - currentGrayRatio <= 0) {
        status = 'skipped';
      }

      let subStages: SubStage[] = initialSubStages.map((stageItem, index) =>
        (index === 0
          ? {
              ...stageItem,
              status: ['submitted', 'deploying', 'completed'].includes(status) ? 'process' : 'wait',
              desc: node?.startTime ? getFormatDate(node.startTime) : undefined,
            }
          : index === 1
          ? {
              ...stageItem,
              status: ['deploying', 'completed'].includes(status) ? 'process' : 'wait',
              desc: node?.scheduleTime ? getFormatDate(node.scheduleTime) : undefined,
            }
          : {
              ...stageItem,
              status: status === 'completed' ? 'process' : 'wait',
              desc:
                status === 'completed' && node?.agatewareTaskInfo?.taskUpdateTime
                  ? getFormatDate(node.agatewareTaskInfo.taskUpdateTime)
                  : undefined,
            }),
      );

      const ratioStartState = getRatioStartState(ratio);
      const { canStart } = ratioStartState;
      const disabledReason = ratioStartState.reason;

      stages.push({
        id: `batch-${percentage}`,
        name: `${percentage}% 发布`,
        percentage,
        status,
        subStages,
        canStart,
        onStart: canStart ? () => handleReleaseOrderGray(ratio) : undefined,
        disabledReason,
      });
    });

    const fullStartState = getFullStartState();
    const canStartFull = fullStartState.canStart;
    const fullDisabledReason = fullStartState.reason;

    // 正式发布节点：子节点状态展示逻辑与灰度节点保持一致
    stages.push({
      id: 'full',
      name: '全量发布',
      percentage: 100,
      status: (() => {
        const taskStatus = releaseProgressNode?.agatewareTaskInfo?.status;
        if (taskStatus === 'SUCCESS') return 'completed' as StageStatus;
        if (taskStatus === 'RUNNING') return 'deploying' as StageStatus;
        if (releaseProgressNode?.startTime) return 'submitted' as StageStatus;
        return 'pending' as StageStatus;
      })(),
      subStages: initialSubStages.map((stageItem, index) => {
        const taskStatus = releaseProgressNode?.agatewareTaskInfo?.status;
        let status: StageStatus = 'pending';
        if (taskStatus === 'SUCCESS') status = 'completed';
        else if (taskStatus === 'RUNNING') status = 'deploying';
        else if (releaseProgressNode?.startTime) status = 'submitted';

        return index === 0
          ? {
              ...stageItem,
              status: ['submitted', 'deploying', 'completed'].includes(status) ? 'process' : 'wait',
              desc: releaseProgressNode?.startTime
                ? getFormatDate(releaseProgressNode.startTime)
                : undefined,
            }
          : index === 1
          ? {
              ...stageItem,
              status: ['deploying', 'completed'].includes(status) ? 'process' : 'wait',
              desc: releaseProgressNode?.scheduleTime
                ? getFormatDate(releaseProgressNode.scheduleTime)
                : undefined,
            }
          : {
              ...stageItem,
              status: status === 'completed' ? 'process' : 'wait',
              desc:
                status === 'completed' && releaseProgressNode?.agatewareTaskInfo?.taskUpdateTime
                  ? getFormatDate(releaseProgressNode.agatewareTaskInfo.taskUpdateTime)
                  : undefined,
            };
      }),
      canStart: canStartFull,
      onStart: canStartFull ? handleFullRelease : undefined,
      disabledReason: fullDisabledReason,
      isFull: true,
    });

    return stages;
  };

  const renderSubStages = (subStages: SubStage[]) => {
    const items = subStages.map(subStage => ({
      title: subStage.name,
      status: subStage.status,
      description: subStage.desc,
    }));

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Steps type="inline" direction="horizontal" items={items as any} />
      </div>
    );
  };

  const stages = buildStages();

  return (
    <Card className="w-full px-4 py-2" size="small">
      <div
        className="hide-scrollbar"
        style={{
          overflowX: 'auto',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <div
          style={{
            display: 'flex',
            minWidth: 'max-content',
          }}
        >
          {stages.map((stage, index) => (
            <div
              key={stage.id}
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Card
                size="small"
                headStyle={{ padding: '14px 18px' }}
                bodyStyle={{ padding: 12 }}
                style={{
                  width: 230,
                  flexShrink: 0,
                  border:
                    stage.status === 'deploying'
                      ? '2px solid #1890ff'
                      : stage.status === 'completed' || stage.status === 'skipped'
                      ? '2px solid #52c41a'
                      : undefined,
                }}
                title={
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      fontSize: 14,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 6,
                      }}
                    >
                      {getStatusIcon(stage.status as StageStatus)}
                      <span>{stage.name}</span>
                    </div>
                    {stage.status === 'pending'
                      ? (() => {
                          const btn = (
                            <Popconfirm
                              disabled={!stage.canStart}
                              title={`确定开始${stage.isFull ? '全量' : '灰度'}发布吗？`}
                              onConfirm={stage.onStart}
                            >
                              <Button
                                type="primary"
                                disabled={!stage.canStart}
                                size="small"
                                icon={<PlayCircleOutlined />}
                              >
                                开始发布
                              </Button>
                            </Popconfirm>
                          );
                          return !stage.canStart && stage.disabledReason ? (
                            <Tooltip title={stage.disabledReason}>
                              <span>{btn}</span>
                            </Tooltip>
                          ) : (
                            btn
                          );
                        })()
                      : getStatusTag(stage.status as StageStatus)}
                  </div>
                }
              >
                <div />
                {renderSubStages(stage.subStages)}
              </Card>

              {index < stages.length - 1 && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      width: 4,
                      height: 4,
                      borderRadius: '50%',
                      border: `1px solid ${stage.status === 'completed' ? '#52c41a' : '#d9d9d9'}`,
                      backgroundColor: 'white',
                      margin: '0 0 0 -2px',
                      zIndex: 9,
                    }}
                  />
                  <div
                    style={{
                      width: 24,
                      height: 2,
                      backgroundColor: stage.status === 'completed' ? '#52c41a' : '#d9d9d9',
                      borderRadius: 1,
                    }}
                  />
                  <div
                    style={{
                      width: 4,
                      height: 4,
                      borderRadius: '50%',
                      border: `1px solid ${stage.status === 'completed' ? '#52c41a' : '#d9d9d9'}`,
                      backgroundColor: 'white',
                      margin: '0 -2px 0 0',
                      zIndex: 9,
                    }}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default RatioGrayWorkflowPanel;

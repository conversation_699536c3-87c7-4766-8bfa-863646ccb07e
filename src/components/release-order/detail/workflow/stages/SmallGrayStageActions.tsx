'use client';

import React, { useState } from 'react';
import { Button, Form, Input, message, Modal, Popconfirm, Radio, Tooltip } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import useReleaseOrderStore from '@/models/releaseOrder';
import services from '@/services/orange-be';
import { arePreStagesCompleted, getStageByType } from '@/utils/releaseOrder';

const { ReleaseOrderController } = services;

const SmallGrayStageActions: React.FC = () => {
  const [startGrayLoading, setStartGrayLoading] = useState(false);
  const [isVerifyModalVisible, setIsVerifyModalVisible] = useState(false);
  const [form] = Form.useForm();

  const {
    releaseOrder,
    hasNsEditPermission,
    templateId,
    fetchAndUpdateReleaseOrder,
    triggerSmallFlowGrayRefresh,
    hasNsTestPermission,
  } = useReleaseOrderStore();

  const { releaseVersion, status, tigaTaskId, releaseOrderStages } = releaseOrder;
  const isFinished = status === 'RELEASED' || status === 'CANCELED';
  const disableEditOperation = !hasNsEditPermission || isFinished;

  // 获取各个阶段的状态
  const smallFlowGrayStage = getStageByType(releaseOrderStages, 'SMALLFLOW_GRAY');
  const verifyStage = getStageByType(releaseOrderStages, 'VERIFY');

  // 检查前置阶段是否完成
  const smallFlowGrayPreCompleted = arePreStagesCompleted(releaseOrderStages, 'SMALLFLOW_GRAY');
  const verifyPreCompleted = arePreStagesCompleted(releaseOrderStages, 'VERIFY');

  // 按钮显示逻辑
  const shouldShowStartGrayButton =
    !tigaTaskId &&
    smallFlowGrayPreCompleted &&
    smallFlowGrayStage?.status === 'INIT' &&
    !disableEditOperation;

  const shouldShowStartVerifyButton =
    verifyPreCompleted && verifyStage?.status === 'INIT' && !isFinished && !disableEditOperation;

  const shouldShowVerifyFeedbackButton =
    verifyPreCompleted &&
    verifyStage?.status &&
    verifyStage.status !== 'INIT' &&
    verifyStage.status !== 'SUCCESS' &&
    verifyStage.status !== 'SKIPPED' &&
    !isFinished &&
    // todo: 当前用户非发起审批人
    hasNsTestPermission;

  const handleVerify = async () => {
    try {
      const values = await form.validateFields();
      await ReleaseOrderController.verifyReply(
        {
          releaseVersion,
        },
        {
          verifyStatus: values.result === 'pass' ? 'PASS' : 'REFUSE',
          verifyMessage: values.report,
        },
      );
      message.success('验证反馈成功');
      await fetchAndUpdateReleaseOrder(releaseVersion!);
      setIsVerifyModalVisible(false);
      form.resetFields();
    } catch (e: any) {
      message.error(`验证反馈失败：${e.message}`);
    }
  };

  const showVerifyModal = () => {
    setIsVerifyModalVisible(true);
  };

  const handleVerifyCancel = () => {
    setIsVerifyModalVisible(false);
    form.resetFields();
  };

  const handleStartVerify = async () => {
    try {
      await ReleaseOrderController.startVerify({ releaseVersion });
      message.success('发起验证成功');
      await fetchAndUpdateReleaseOrder(releaseVersion!);
    } catch (e: any) {
      message.error(`发起验证失败：${e.message}`);
    }
  };

  const handleStartGray = async () => {
    if (!releaseVersion || !templateId) return;
    setStartGrayLoading(true);
    try {
      await ReleaseOrderController.tigaGray(
        {
          releaseVersion,
        },
        {
          taskCmd: 'START' as any,
          templateId,
        },
      );
      message.success('开始灰度成功');
      // 刷新发布单状态以获取最新的 tigaTaskId
      await fetchAndUpdateReleaseOrder(releaseVersion);
      // 触发小流量灰度工作流刷新
      triggerSmallFlowGrayRefresh();
    } catch (e: any) {
      message.error(e.message || '开始灰度失败');
    } finally {
      setStartGrayLoading(false);
    }
  };

  return (
    <div className="flex">
      {shouldShowStartGrayButton && (
        <Popconfirm title={'确定开始小流量灰度吗？'} onConfirm={handleStartGray}>
          <Button type="primary" className={'ml-2'} loading={startGrayLoading}>
            开始灰度
          </Button>
        </Popconfirm>
      )}

      {shouldShowStartVerifyButton && (
        <Popconfirm title={'确定发起验证吗？'} onConfirm={handleStartVerify}>
          <Button type={'primary'} className={'ml-2'}>
            发起验证
          </Button>
        </Popconfirm>
      )}

      {shouldShowVerifyFeedbackButton && (
        <Button type="primary" className={'ml-2'} onClick={showVerifyModal}>
          验证结果反馈
        </Button>
      )}

      {/* 小流量灰度观测数据刷新 */}
      {tigaTaskId && (
        <Tooltip title="刷新观测数据">
          <Button
            className={'ml-2'}
            icon={<ReloadOutlined />}
            aria-label="刷新观测数据"
            onClick={triggerSmallFlowGrayRefresh}
          />
        </Tooltip>
      )}
      <Modal
        maskClosable={false}
        title="验证结果反馈"
        open={isVerifyModalVisible}
        onOk={handleVerify}
        onCancel={handleVerifyCancel}
        okText="提交"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="result"
            label="验证结果"
            rules={[
              {
                required: true,
                message: '请选择验证结果',
              },
            ]}
          >
            <Radio.Group>
              <Radio value="pass">通过</Radio>
              <Radio value="fail">不通过</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="report"
            label="验证报告"
            rules={[
              {
                required: true,
                message: '请输入验证报告',
              },
            ]}
          >
            <Input.TextArea rows={4} placeholder="请输入验证报告内容" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SmallGrayStageActions;

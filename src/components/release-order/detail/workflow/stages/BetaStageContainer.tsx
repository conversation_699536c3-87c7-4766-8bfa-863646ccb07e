'use client';

import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { Button, Empty, List, Spin, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  MobileOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import useReleaseOrderStore from '@/models/releaseOrder';
import services from '@/services/orange-be';
import { getFormatDate } from '@/utils/common';
import QRCodeModal from '../../../QRCodeModal';
import { getEnv } from '@/utils/env';

const { ReleaseOrderController } = services;

// 创建 BETA 阶段的 Context
interface BetaStageContextType {
  betaScanLogs: Record<string, string>[];
  loading: boolean;
  refreshLogs: () => void;
}

const BetaStageContext = createContext<BetaStageContextType | null>(null);

// BETA Actions 组件
const BetaStageActions: React.FC = () => {
  const [isQrCodeModalVisible, setIsQrCodeModalVisible] = useState(false);
  const context = useContext(BetaStageContext);

  if (!context) {
    throw new Error('BetaStageActions must be used within BetaStageContainer');
  }

  const { refreshLogs } = context;
  const { releaseOrder, hasNsEditPermission } = useReleaseOrderStore();

  const { releaseVersion, status, appKey } = releaseOrder;

  const isFinished = status === 'RELEASED' || status === 'CANCELED';
  const disableEditOperation = !hasNsEditPermission || isFinished;

  const showQrCodeModal = () => {
    setIsQrCodeModalVisible(true);
  };

  const handleQrCodeCancel = () => {
    setIsQrCodeModalVisible(false);
  };

  return (
    <>
      {!disableEditOperation && (
        <div className="flex">
          <Button type="primary" className={'ml-2'} disabled={isFinished} onClick={showQrCodeModal}>
            快速扫码 BETA
          </Button>
          <Button icon={<ReloadOutlined />} className={'ml-2'} onClick={refreshLogs} />
        </div>
      )}

      <QRCodeModal
        open={isQrCodeModalVisible}
        onCancel={handleQrCodeCancel}
        releaseVersion={releaseVersion!}
        appKey={appKey!}
        isFinished={isFinished}
      />
    </>
  );
};

const ENV_MAP = {
  RELEASE: 'ONLINE',
  PRE: 'PRE',
  DAILY: 'DAILY',
};

// BETA Content 组件
const BetaStageContent: React.FC = () => {
  const context = useContext(BetaStageContext);

  if (!context) {
    throw new Error('BetaStageContent must be used within BetaStageContainer');
  }

  const { betaScanLogs, loading } = context;

  return (
    <div className="px-4">
      {loading ? (
        <div className="flex justify-center py-8">
          <Spin size="large" />
        </div>
      ) : betaScanLogs.length === 0 ? (
        <Empty className="p-4" description="暂无扫码强制拉取配置进行 BETA 记录" />
      ) : (
        <List
          dataSource={betaScanLogs}
          renderItem={(item, index) => (
            <List.Item key={index} className="border-b border-gray-100 last:border-b-0">
              <div className="w-full flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <ClockCircleOutlined className="mr-2 text-gray-500" />
                  <span>{getFormatDate(item.time)}</span>
                </div>

                <div className="flex items-center flex-2 justify-center">
                  <MobileOutlined className="mr-2 text-gray-500" />
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm mr-2">{item.utdid}</code>
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">{`${item.brand} ${item.model}`}</code>
                </div>

                <div className="flex items-center flex-1 justify-end">
                  {getEnv() === ENV_MAP[item.env as keyof typeof ENV_MAP] ? (
                    <div className="flex items-center">
                      <CheckCircleOutlined className="text-green-500 mr-2" />
                      <span className="text-green-500">验证环境正确</span>
                    </div>
                  ) : (
                    <Tooltip
                      title={`验证环境为：${
                        ENV_MAP[item.env as keyof typeof ENV_MAP]
                      }，与当前配置环境不一致`}
                    >
                      <WarningOutlined className="text-red-500 mr-2" />
                      <span className="text-red-500">验证环境不匹配</span>
                    </Tooltip>
                  )}
                </div>
              </div>
            </List.Item>
          )}
          size="large"
        />
      )}
    </div>
  );
};

// 容器组件
const BetaStageContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [betaScanLogs, setBetaScanLogs] = useState<Record<string, string>[]>([]);
  const [loading, setLoading] = useState(false);

  const { releaseOrder, update } = useReleaseOrderStore();
  const { releaseVersion } = releaseOrder;

  const fetchBetaScanLogs = useCallback(async () => {
    if (!releaseVersion) return;

    setLoading(true);
    try {
      const { data } = await ReleaseOrderController.getScanBetaLogs({
        releaseVersion,
        type: 'FORCE_BETA' as any,
        page: 1,
        size: 100,
      });

      const logs = data || [];
      setBetaScanLogs(logs);

      // 更新全局状态：是否有 BETA 扫码记录
      const hasLogs = logs.length > 0;
      update({ hasBetaScanLogs: hasLogs });
    } catch (error) {
      console.error('Failed to fetch beta scan logs:', error);
      setBetaScanLogs([]);
      update({ hasBetaScanLogs: false });
    } finally {
      setLoading(false);
    }
  }, [releaseVersion, update]);

  const refreshLogs = useCallback(() => {
    fetchBetaScanLogs();
  }, [fetchBetaScanLogs]);

  useEffect(() => {
    fetchBetaScanLogs();
  }, [releaseVersion]);

  const contextValue: BetaStageContextType = {
    betaScanLogs,
    loading,
    refreshLogs,
  };

  return <BetaStageContext.Provider value={contextValue}>{children}</BetaStageContext.Provider>;
};

// 导出组合组件
export const BetaStage = {
  Container: BetaStageContainer,
  Actions: BetaStageActions,
  Content: BetaStageContent,
};

export default BetaStage;

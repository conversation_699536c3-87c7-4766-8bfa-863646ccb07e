'use client';

import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { Button, Empty, Form, Input, message, Modal, Radio, Spin, Tag, Tooltip } from 'antd';
import { FileTextOutlined, QuestionCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import useReleaseOrderStore from '@/models/releaseOrder';
import services from '@/services/orange-be';
import { User } from '@/components/user';
import { getFormatDate } from '@/utils/common';
import { calculateApplyReleaseStatus, getStageByType } from '@/utils/releaseOrder';

const { ReleaseOrderController } = services;

const safeWindowOpen = (url: string): void => {
  try {
    window.open(url, '_blank', 'noopener,noreferrer');
  } catch (error) {
    console.error('Failed to open window:', error);
    message.error('打开链接失败');
  }
};

// 创建申请发布阶段的 Context
interface ApplyStageContextType {
  applyRecords: API.ReleaseOrderOperationDTO[];
  loading: boolean;
  refreshApplyRecords: () => Promise<void>;
  applyRelease: (applyData: API.ApplyReleaseDTO) => Promise<void>;
}

const ApplyStageContext = createContext<ApplyStageContextType | null>(null);

// 申请发布 Actions 组件
const ApplyStageActions: React.FC = () => {
  const [isApplyModalVisible, setIsApplyModalVisible] = useState(false);
  const [applyForm] = Form.useForm();
  const [applyLoading, setApplyLoading] = useState(false);
  const [refreshLoading, setRefreshLoading] = useState(false);

  const context = useContext(ApplyStageContext);
  if (!context) {
    throw new Error('ApplyStageActions must be used within ApplyStageContainer');
  }

  const { refreshApplyRecords, applyRelease } = context;

  const { releaseOrder, hasNsEditPermission } = useReleaseOrderStore();

  const { status } = releaseOrder;
  const isFinished = status === 'RELEASED' || status === 'CANCELED';
  const applyReleaseStage = getStageByType(releaseOrder.releaseOrderStages, 'APPLY_RELEASE');
  const disableEditOperation =
    !hasNsEditPermission ||
    isFinished ||
    applyReleaseStage?.status === 'SUCCESS' ||
    applyReleaseStage?.status === 'SKIPPED';

  const handleApply = async () => {
    try {
      setApplyLoading(true);
      const values = await applyForm.validateFields();

      await applyRelease({
        releaseLevel: values.releaseLevel,
        reason: values.applyReason,
      });

      message.success('申请发布成功');
      setIsApplyModalVisible(false);
      applyForm.resetFields();
    } catch (e: any) {
      message.error(`申请发布失败：${e.message}`);
    } finally {
      setApplyLoading(false);
    }
  };

  const handleRefreshCallback = async () => {
    try {
      setRefreshLoading(true);
      await refreshApplyRecords();
      message.success('刷新成功');
    } catch (e: any) {
      message.error(`刷新失败：${e.message}`);
    } finally {
      setRefreshLoading(false);
    }
  };

  const showApplyModal = () => {
    setIsApplyModalVisible(true);
  };

  const handleApplyCancel = () => {
    setIsApplyModalVisible(false);
    applyForm.resetFields();
  };

  return (
    <>
      {!disableEditOperation && (
        <div className="flex gap-2">
          <Button type="primary" onClick={showApplyModal}>
            申请发布
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefreshCallback}
            disabled={isFinished}
            loading={refreshLoading}
            title="刷新发布记录"
          />
        </div>
      )}

      {/* 申请发布模态框 */}
      <Modal
        maskClosable={false}
        title="申请发布"
        open={isApplyModalVisible}
        onOk={handleApply}
        onCancel={handleApplyCancel}
        okText="确认"
        cancelText="取消"
        confirmLoading={applyLoading}
      >
        <Form
          form={applyForm}
          layout="vertical"
          initialValues={{
            releaseLevel: 'NORMAL',
          }}
        >
          <Form.Item
            name="releaseLevel"
            label="发布类型"
            rules={[
              {
                required: true,
                message: '请选择发布类型',
              },
            ]}
          >
            <Radio.Group>
              <Radio value="NORMAL">
                普通发布
                <Tooltip title="约 10 分钟生效（推荐）">
                  <QuestionCircleOutlined className="ml-1 text-gray-400" />
                </Tooltip>
              </Radio>
              <Radio value="EMERGENT">
                紧急发布
                <Tooltip title="约 2 分钟生效。仅回滚、故障类允许申请紧急发布，紧急发布需要二级主管审批">
                  <QuestionCircleOutlined className="ml-1 text-gray-400" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="applyReason"
            label="申请原因"
            rules={[
              {
                required: true,
                message: '请输入申请原因',
              },
            ]}
          >
            <Input.TextArea rows={4} placeholder="请输入申请原因" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

// 申请发布 Content 组件
const ApplyStageContent: React.FC = () => {
  const context = useContext(ApplyStageContext);
  if (!context) {
    throw new Error('ApplyStageContent must be used within ApplyStageContainer');
  }

  const { applyRecords, loading } = context;

  return (
    <div>
      {loading ? (
        <div className="flex justify-center py-8">
          <Spin size="large" />
        </div>
      ) : applyRecords.length === 0 ? (
        <Empty className="p-4" description="暂无申请记录" />
      ) : (
        applyRecords.map(record => {
          let params: API.ApplyReleaseDTO = {
            releaseLevel: 'NORMAL',
            reason: '',
          };
          let result = {
            applyOrderUrl: '',
            orderDetailUrl: '',
          };

          try {
            const parsedParams = JSON.parse(record.params || '{}');
            params = { ...params, ...parsedParams };
          } catch {
            // ignore parse error
          }

          try {
            result = JSON.parse(record.result || '{}');
          } catch {
            // ignore parse error
          }

          const orderUrl = result.orderDetailUrl || result.applyOrderUrl;
          const cfStatus = calculateApplyReleaseStatus(result);

          return (
            <div key={record.id} className="border-b border-gray-200 p-4">
              <div className="flex justify-between items-center">
                <div className="flex-1">
                  <div className="flex items-center space-x-4 mb-2">
                    <span
                      className={
                        params.releaseLevel === 'EMERGENT' ? 'text-red-500' : 'text-gray-800'
                      }
                    >
                      {params.releaseLevel === 'EMERGENT' ? '紧急发布' : '普通发布'}
                    </span>
                    <Tag
                      color={
                        cfStatus === 'SUCCESS'
                          ? 'green'
                          : cfStatus === 'FAILED' || cfStatus === 'CANCELED'
                          ? 'red'
                          : result.orderDetailUrl
                          ? 'blue'
                          : 'yellow'
                      }
                    >
                      {cfStatus === 'SUCCESS'
                        ? '审批通过'
                        : cfStatus === 'FAILED'
                        ? '审批拒绝'
                        : cfStatus === 'CANCELED'
                        ? '审批取消'
                        : result.orderDetailUrl
                        ? '审批中'
                        : '待提交'}
                    </Tag>
                    <User empIds={[record.creator!]} showAvatar />
                    <span className="text-gray-500">申请于 {getFormatDate(record.gmtCreate!)}</span>
                  </div>
                  <div className="text-gray-500 flex items-center">
                    <FileTextOutlined />
                    <span className="ml-2">{params.reason}</span>
                  </div>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <Button
                    type="link"
                    size="small"
                    disabled={!orderUrl}
                    onClick={() => {
                      safeWindowOpen(orderUrl);
                    }}
                  >
                    查看申请单
                  </Button>
                </div>
              </div>
            </div>
          );
        })
      )}
    </div>
  );
};

// 容器组件
const ApplyStageContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [applyRecords, setApplyRecords] = useState<API.ReleaseOrderOperationDTO[]>([]);
  const [loading, setLoading] = useState(false);

  const { releaseOrder, fetchAndUpdateReleaseOrder, update } = useReleaseOrderStore();
  const { releaseVersion } = releaseOrder;

  // 获取申请发布记录
  const fetchApplyRecords = useCallback(async () => {
    if (!releaseVersion) return;

    setLoading(true);
    try {
      const { data } = await ReleaseOrderController.getOperations({
        releaseVersion,
        operationTypes: ['APPLY_RELEASE'],
      });
      const records = data || [];
      setApplyRecords(records);
    } catch (error) {
      console.error('Failed to fetch apply records:', error);
      setApplyRecords([]);
    } finally {
      setLoading(false);
    }
  }, [releaseVersion, update]);

  // 刷新申请记录（包含 changefree 回调）
  const refreshApplyRecords = useCallback(async () => {
    if (!releaseVersion) return;

    try {
      setLoading(true);
      // 调用 changefree 回调接口
      await ReleaseOrderController.changefreeCallback({
        releaseVersion,
      });

      // 刷新发布单信息
      await fetchAndUpdateReleaseOrder(releaseVersion);

      // 重新获取申请记录
      await fetchApplyRecords();
    } catch (error) {
      console.error('Failed to refresh apply records:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [releaseVersion, fetchAndUpdateReleaseOrder, fetchApplyRecords]);

  // 申请发布
  const applyRelease = useCallback(
    async (applyData: API.ApplyReleaseDTO) => {
      if (!releaseVersion) return;

      await ReleaseOrderController.applyRelease({ releaseVersion }, applyData);

      // 申请成功后刷新发布单状态和申请记录
      await fetchAndUpdateReleaseOrder(releaseVersion);
      await fetchApplyRecords();
    },
    [releaseVersion, fetchAndUpdateReleaseOrder, fetchApplyRecords],
  );

  useEffect(() => {
    fetchApplyRecords();
  }, [releaseVersion]);

  const contextValue: ApplyStageContextType = {
    applyRecords,
    loading,
    refreshApplyRecords,
    applyRelease,
  };

  return <ApplyStageContext.Provider value={contextValue}>{children}</ApplyStageContext.Provider>;
};

// 导出组合组件
export const ApplyStage = {
  Container: ApplyStageContainer,
  Actions: ApplyStageActions,
  Content: ApplyStageContent,
};

export default ApplyStage;

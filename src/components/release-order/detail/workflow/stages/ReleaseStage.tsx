'use client';

import React, { useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import useReleaseOrderStore from '@/models/releaseOrder';
import services from '@/services/orange-be';
import { arePreStagesCompleted, getStageByType } from '@/utils/releaseOrder';

const { ReleaseOrderController } = services;

const ReleaseStage: React.FC = () => {
    const [publishLoading, setPublishLoading] = useState(false);
    const {
        releaseOrder,
        hasNsEditPermission,
        fetchAndUpdateReleaseOrder,
    } = useReleaseOrderStore();

    const { releaseVersion, status, releaseOrderStages } = releaseOrder;
    const isFinished = status === 'RELEASED' || status === 'CANCELED';
    const disableEditOperation = !hasNsEditPermission || isFinished;

    // 获取 RELEASE 阶段状态
    const releaseStage = getStageByType(releaseOrderStages, 'RELEASE');

    // 检查前置阶段是否完成
    const releasePreCompleted = arePreStagesCompleted(releaseOrderStages, 'RELEASE');

    // 按钮显示逻辑：前置阶段都完成且 RELEASE 阶段为 INIT 时才显示
    const shouldShowPublishButton = releasePreCompleted &&
        releaseStage?.status === 'INIT' &&
        !isFinished &&
        !disableEditOperation;

    const handlePublish = async () => {
        try {
            setPublishLoading(true);
            await ReleaseOrderController.publish({ releaseVersion });
            message.success('发布成功');
            await fetchAndUpdateReleaseOrder(releaseVersion!);
        } catch (e: any) {
            message.error(`发布失败：${e.message}`);
        } finally {
            setPublishLoading(false);
        }
    };

    return (
        <>
            {shouldShowPublishButton && (
                <Popconfirm title={'确定要正式发布吗？'} onConfirm={handlePublish}>
                    <Button type="primary" loading={publishLoading}>
                        正式发布
                    </Button>
                </Popconfirm>
            )}
        </>
    );
};

export default ReleaseStage; 
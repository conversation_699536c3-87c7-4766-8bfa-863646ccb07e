'use client';

import React, { useEffect, useRef, useState } from 'react';
import useReleaseOrderStore from '@/models/releaseOrder';
import { getTigaObservationUrl } from '@/utils/link';
import { Alert, Empty } from 'antd';
import { getStageByType } from '@/utils/releaseOrder';
import { User } from '@/components/user';

const SmallGrayStageContent: React.FC = () => {
  const [iframeHeight, setIframeHeight] = useState('100vh');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const { releaseOrder, namespace, smallFlowGrayRefreshTrigger } = useReleaseOrderStore();
  const { tigaTaskId, releaseOrderStages } = releaseOrder;

  // 获取verify阶段状态
  const verifyStage = getStageByType(releaseOrderStages, 'VERIFY');

  // 判断是否需要展示verify状态信息
  const shouldShowVerifyStatus =
    verifyStage && (verifyStage.status === 'IN_PROGRESS' || verifyStage.status === 'FAILED');

  // 根据verify阶段状态获取对应的文案
  const getVerifyStatusMessage = () => {
    if (!verifyStage) return '';

    switch (verifyStage.status) {
      case 'IN_PROGRESS':
        return (<p className='flex items-center'>测试负责人 <div className='mx-2'><User empIds={namespace.testers} showAvatar size={'xsmall'} /></div> 验证中...</p>);
      case 'FAILED':
        return '验证不通过';
      default:
        return '';
    }
  };

  useEffect(() => {
    function handleMessage(event: MessageEvent) {
      if (event.data && typeof event.data.iframeHeight === 'number') {
        setIframeHeight(event.data.iframeHeight);
      }
    }

    function calculateModalPosition() {
      if (iframeRef.current) {
        const viewportHeight = window.innerHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // fixme: 待优化
        const modalTop = Math.max(0, viewportHeight / 2 + scrollTop - 800);

        iframeRef.current.contentWindow?.postMessage(
          {
            type: 'MODAL_POSITION_UPDATE',
            payload: { top: modalTop },
          },
          '*',
        );
      }
    }

    function handleScroll() {
      calculateModalPosition();
    }

    window.addEventListener('message', handleMessage);
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', calculateModalPosition);

    return () => {
      window.removeEventListener('message', handleMessage);
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', calculateModalPosition);
    };
  }, []);

  if (!tigaTaskId) {
    return (
      <>
        {shouldShowVerifyStatus && (
          <Alert
            message={getVerifyStatusMessage()}
            type={verifyStage?.status === 'IN_PROGRESS' ? 'info' : 'error'}
            showIcon
          />
        )}
        <Empty description="暂无观测数据" className="p-4" />
      </>
    );
  }

  return (
    <>
      {shouldShowVerifyStatus && (
        <Alert
          message={getVerifyStatusMessage()}
          type={verifyStage?.status === 'IN_PROGRESS' ? 'info' : 'error'}
          showIcon
        />
      )}
      {tigaTaskId && (
        <iframe
          ref={iframeRef}
          key={smallFlowGrayRefreshTrigger}
          src={getTigaObservationUrl(tigaTaskId)}
          style={{
            width: '100%',
            height: iframeHeight,
            border: 'none',
          }}
          title="观测数据"
          allowFullScreen
        />
      )}
    </>
  );
};

export default SmallGrayStageContent;

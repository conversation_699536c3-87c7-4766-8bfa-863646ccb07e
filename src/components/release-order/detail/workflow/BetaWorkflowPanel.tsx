import { Card } from 'antd';
import { RightCircleOutlined } from '@ant-design/icons';
import { useReleaseOrderStore } from '@/store';
import React from 'react';
import { Workflow } from '@ali/mc-uikit';
import { getStageByType } from '@/utils/releaseOrder';
import { WorkflowNodeProps } from '@ali/mc-uikit/esm/Workflow/FlowNode';

const BetaWorkflowPanel = () => {
  const update = useReleaseOrderStore(state => state.update);
  const releaseOrder = useReleaseOrderStore(state => state.releaseOrder);
  const subWorkflowStage = useReleaseOrderStore(state => state.subWorkflowStage);
  const hasBetaScanLogs = useReleaseOrderStore(state => state.hasBetaScanLogs);

  // 从 releaseOrderStages 中获取申请发布阶段的状态
  const applyReleaseStage = getStageByType(releaseOrder.releaseOrderStages, 'APPLY_RELEASE');

  const getApplyReleaseStatus = () => {
    switch (applyReleaseStage?.status) {
      case 'SUCCESS':
        return 'success';
      case 'IN_PROGRESS':
        return 'process';
      case 'FAILED':
      case 'CANCELED':
        return 'error';
      case 'SKIPPED':
        return 'success';
      default:
        return 'waiting';
    }
  };

  const workflowItems: Omit<
    WorkflowNodeProps<any>,
    'onClick' | 'onIconClick' | 'stage' | 'type'
  >[] = [
    {
      identifier: 'beta',
      icon: <RightCircleOutlined />,
      title: 'BETA 验证',
      status: hasBetaScanLogs ? 'success' : 'process',
      active: subWorkflowStage === 'beta',
    },
    {
      icon: <RightCircleOutlined />,
      title: '申请发布',
      status: getApplyReleaseStatus(),
      identifier: 'apply',
      active: subWorkflowStage === 'apply',
    },
  ];

  return (
    <Card className="w-full">
      <div
        className="overflow-x-auto"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <div className="min-w-max py-1">
          <Workflow
            items={workflowItems as any}
            onClick={key => update({ subWorkflowStage: key })}
          />
        </div>
      </div>
    </Card>
  );
};

export default BetaWorkflowPanel;

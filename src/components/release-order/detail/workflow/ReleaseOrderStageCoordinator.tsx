'use client';

import React, { useEffect, useState } from 'react';
import useReleaseOrderStore from '@/models/releaseOrder';
import { ApplyStageContainer, BetaStageContainer, SmallGrayStageActions, SmallGrayStageContent } from './stages';
import { But<PERSON>, Tooltip } from 'antd';
import { LinkOutlined, QuestionCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { getTigaMetricObservationUrl, getTigaTaskDetailUrl } from '@/utils/link';

/**
 * 发布单阶段协调组件
 *
 * 根据当前的工作流阶段（mainWorkflowStage 和 subWorkflowStage）
 * 渲染对应的阶段组件，实现了组件的模块化和职责分离
 */
const ReleaseOrderStageCoordinator: React.FC = () => {
  const [iframeHeight, setIframeHeight] = useState('100vh');
  const { mainWorkflowStage, subWorkflowStage, releaseOrder, templateId } = useReleaseOrderStore();
  const [iframeRefreshKey, setIframeRefreshKey] = useState(0);

  const observationUrl = getTigaMetricObservationUrl({
    taskId: releaseOrder.tigaTaskId,
    templateId,
    appKeys: releaseOrder.appKey,
    startTime: String(new Date(releaseOrder.gmtCreate!).valueOf()),
  });

  useEffect(() => {
    function handleMessage(event: MessageEvent) {
      if (event.data && typeof event.data.iframeHeight === 'number') {
        setIframeHeight(event.data.iframeHeight);
      }
    }

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // 阶段标题映射
  const stageTitles = {
    beta: 'BETA 验证记录',
    apply: '申请发布记录',
    'smallflow-gray': '观测数据',
    gray: '观测数据',
    verify: '观测数据',
    release: '观测数据',
  };

  // 渲染标题区域的操作按钮
  const renderStageActions = () => {
    switch (subWorkflowStage) {
      case 'beta':
        return <BetaStageContainer.Actions />;
      case 'apply':
        return <ApplyStageContainer.Actions />;
      default:
        break;
    }

    switch (mainWorkflowStage) {
      case 'smallflow-gray':
        return <SmallGrayStageActions />;
      // case 'release':
      //   return <ReleaseStage />;
      default:
        return null;
    }
  };

  // 渲染内容区域
  const renderStageContent = () => {
    switch (subWorkflowStage) {
      case 'beta':
        return <BetaStageContainer.Content />;
      case 'apply':
        return <ApplyStageContainer.Content />;
      case 'gray':
      // case 'release':
      case 'verify':
        return (
          <iframe
            key={iframeRefreshKey}
            src={observationUrl}
            style={{
              width: '100%',
              height: iframeHeight,
              border: 'none',
            }}
            title="观测数据"
            allowFullScreen
          />
        );
      default:
        break;
    }

    switch (mainWorkflowStage) {
      case 'smallflow-gray':
        return <SmallGrayStageContent />;
      default:
        return null;
    }
  };

  const isBetaStage = subWorkflowStage === 'beta';
  const isApplyStage = subWorkflowStage === 'apply';
  const shouldShowObservationRefresh =
    subWorkflowStage === 'gray' || subWorkflowStage === 'release' || subWorkflowStage === 'verify';

  const content = (
    <div className="bg-white rounded-lg border">
      {/* 标题和操作按钮区域 */}
      <div
        className="p-4 border-b justify-between items-center flex flex-row"
        style={{ backgroundColor: '#F6F8FA' }}
      >
        <h3 className="text-lg font-medium flex-1 flex items-center">
          {stageTitles[subWorkflowStage] || stageTitles[mainWorkflowStage] || '流程记录'}
          {subWorkflowStage === 'beta' && (
            <Tooltip title="请扫码并点击【强制加载配置内容】按钮进行 BETA 验证">
              <QuestionCircleOutlined className="ml-2 text-gray-500 cursor-help" />
            </Tooltip>
          )}
          {mainWorkflowStage === 'smallflow-gray' && releaseOrder.tigaTaskId && (
            <Tooltip title="查看 TIGA 任务详情">
              <Button
                type="text"
                size="small"
                icon={<LinkOutlined />}
                className="ml-2"
                onClick={() => {
                  window.open(
                    getTigaTaskDetailUrl(releaseOrder.tigaTaskId!),
                    '_blank',
                    'noopener,noreferrer',
                  );
                }}
              />
            </Tooltip>
          )}
        </h3>
        {renderStageActions()}
        {shouldShowObservationRefresh && (
          <Tooltip title="刷新观测数据">
            <Button
              className="ml-2"
              icon={<ReloadOutlined />}
              aria-label="刷新观测数据"
              onClick={() => setIframeRefreshKey(prev => prev + 1)}
            />
          </Tooltip>
        )}
      </div>

      {/* 内容区域 */}
      {renderStageContent()}
    </div>
  );

  // 根据阶段用相应的容器包装
  if (isBetaStage) {
    return <BetaStageContainer.Container>{content}</BetaStageContainer.Container>;
  }

  if (isApplyStage) {
    return <ApplyStageContainer.Container>{content}</ApplyStageContainer.Container>;
  }

  return content;
};

export default ReleaseOrderStageCoordinator;

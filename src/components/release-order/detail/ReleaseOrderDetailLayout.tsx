'use client';

import { Layout, message } from 'antd';
import ReleaseOrderDetailSider, { WORKFLOW_STAGES } from './ReleaseOrderDetailSider';
import ReleaseOrderDetailContent from './ReleaseOrderDetailContent';
import { useEffect } from 'react';
import { useReleaseOrderStore } from '@/store';

const { Content } = Layout;

interface ReleaseOrderDetailLayoutProps {
  releaseVersion: string;
}

export default function ReleaseOrderDetailLayout({
  releaseVersion,
}: ReleaseOrderDetailLayoutProps) {
  const updateReleaseOrder = useReleaseOrderStore(state => state.fetchAndUpdateReleaseOrder);
  const fetchNamespace = useReleaseOrderStore(state => state.fetchNamespace);
  const mainWorkflowStage = useReleaseOrderStore(state => state.mainWorkflowStage);
  const mainWorkflowStageTitle = WORKFLOW_STAGES.find(
    stage => stage.key === mainWorkflowStage,
  )?.title;

  useEffect(() => {
    (async () => {
      try {
        if (releaseVersion) {
          await updateReleaseOrder(releaseVersion);
          await fetchNamespace();
        }
      } catch (error) {
        message.error(`加载版本详情失败: ${error.message}`);
      }
    })();
  }, []);

  return (
    <Layout>
      <ReleaseOrderDetailSider />
      <Layout className="transition-all duration-300">
        <Content className="bg-gray-50">
          {mainWorkflowStageTitle && (
            <h3 className="px-6 pt-4 text-lg font-medium  text-gray-900">
              {mainWorkflowStageTitle}
            </h3>
          )}
          <ReleaseOrderDetailContent />
        </Content>
      </Layout>
    </Layout>
  );
}

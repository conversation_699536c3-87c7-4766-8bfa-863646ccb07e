import React, { useEffect, useState } from 'react';
import {
  Avatar,
  AvatarProps,
  Dropdown,
  Flex,
  MenuProps,
  Skeleton,
  theme,
  TooltipProps,
} from 'antd';

import { uniq } from 'lodash-es';
import classNames from 'classnames';

import { User as IUser } from '@ali/mc-services';
import UserTag from './UserTag';

import { getUserFromCache, isDepAccount, mockUserInfo, setUserCache } from './util';
import { ItemType } from 'antd/es/menu/interface';

import styles from './index.module.less';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';
import { queryUserByEmpIds } from '@/services/service';
import { trimEmpId } from '@/utils/common';

export interface UserProps {
  /*
   * 用户工号
   */
  empIds: string | string[] | undefined;

  /**
   * 是否显示头像
   */
  showAvatar?: boolean;

  /**
   * 尺寸
   */
  size?: AvatarSize | 'xsmall';

  /**
   * 指定头像的形状
   */
  shape?: AvatarProps['shape'];

  /**
   * 头像数量
   */
  avatarNum?: number;

  /**
   * 是否显示花名
   */
  showName?: boolean;

  /**
   * 组合展示，默认值 false
   * 设置为true时，showName 参数将失效，仅展示头像
   */
  group?: boolean;

  /**
   * 花名超长时，是否省略，默认值 true
   * 默认的花名展示最大宽度为 60px，可以兼容绝大部分的场景，但是对于部门账号、外文名称可能不友好
   */
  ellipsis?: boolean | number;

  placement?: TooltipProps['placement'];
}

export default function User(props: UserProps) {
  const {
    empIds,
    showAvatar = false,
    size: sizeProp,
    shape,
    placement = 'top',
    avatarNum = 4,
    group = false,
    showName = true,
    ellipsis = true,
  } = props;
  const { token: themeToken } = theme.useToken();

  const size = (sizeProp as string) === 'xsmall' ? themeToken.sizeMS : sizeProp;

  const [userInfos, setUserInfos] = useState<IUser[]>([]);

  const fetchUser = (value: string | string[]) => {
    const _empIds = Array.isArray(value) ? value : value.split(',');

    if (_empIds.length === 0) {
      setUserInfos([]);
      return;
    }

    const cached: IUser[] = [];
    const needFetchIds: string[] = [];
    uniq(_empIds).forEach(empId => {
      const _empId = trimEmpId(empId); // 有些工号会用 0 补全到 6 位
      if (isDepAccount(_empId)) {
        cached.push(mockUserInfo(_empId));
        return;
      }

      const data = getUserFromCache(_empId);
      if (data) {
        try {
          cached.push(data);
          return;
        } catch (err) {
          console.error(err);
        }
      } else {
        needFetchIds.push(_empId);
      }
    });

    if (needFetchIds.length === 0) {
      setUserInfos(cached);
    }

    needFetchIds.length > 0 &&
      queryUserByEmpIds({ empIds: needFetchIds }).then(res => {
        if (Object.values(res)?.length) {
          const users = Object.values(res).map((i: any) => {
            return {
              ...i,
              nickName: i.nickNameCn,
              workUrl: `https://work.alibaba-inc.com/nwpipe/u/${i.empId}`,
              email: i.emailAddr,
              avatar: `https://work.alibaba-inc.com/photo/${i.empId}.jpg?`,
              empId: trimEmpId(i.empId),
            };
          });
          setUserCache(users);
          setUserInfos(cached.concat(users));
          return;
        } else {
          setUserInfos(cached);
        }
      });
  };

  useEffect(() => {
    if (empIds) {
      fetchUser(empIds);
    }
  }, [empIds]);

  return (
    <Flex
      gap="small"
      align="center"
      wrap
      className={classNames({
        [styles['users']]: true,
        [styles['users-group']]: !!group,
      })}
    >
      {userInfos ? (
        userInfos.map((userInfo, index) => {
          if (index < avatarNum) {
            return (
              <UserTag
                key={`${userInfo.empId}`}
                data={userInfo}
                placement={placement}
                showAvatar={showAvatar || group}
                showName={(showName && !group) || (!showAvatar && userInfos.length <= avatarNum)}
                size={size as AvatarSize}
                ellipsis={ellipsis}
                shape={shape}
              />
            );
          }

          if (index === userInfos.length - 1) {
            const items: MenuProps['items'] = userInfos.map(item => {
              const { empId } = item;

              return {
                label: <UserTag data={item} showAvatar showName placement="right" shape={shape} />,
                key: empId,
              } as ItemType;
            });

            return (
              <Dropdown
                key="user-more-menu"
                menu={{
                  items,
                }}
                placement="bottomLeft"
              >
                <Avatar
                  className={styles.more}
                  size={showAvatar ? (size as AvatarSize) : 'small'}
                  shape={shape}
                >
                  +{userInfos.length - avatarNum}
                </Avatar>
              </Dropdown>
            );
          }

          return null;
        })
      ) : (
        <Skeleton.Avatar active size={size as any} shape={shape} />
      )}
    </Flex>
  );
}

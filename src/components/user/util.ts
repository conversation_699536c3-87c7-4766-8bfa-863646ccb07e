import { User as IUser, User } from '@ali/mc-services';
import { trimEmpId } from '@/utils/common';

const cacheKeyPrefix = '_user_';

export function getUserFromCache(empId: string): IUser | undefined {
  const _empId = trimEmpId(empId);
  const cacheKey = `${cacheKeyPrefix}${_empId}`;
  if (window.sessionStorage) {
    const data = window.sessionStorage.getItem(cacheKey);
    if (data) {
      try {
        return JSON.parse(data) as IUser;
      } catch (err) {
        console.error(err);
        window.sessionStorage.removeItem(cacheKey);
      }
    }
  }

  return undefined;
}

export function setUserCache(users: IUser[]): void {
  if (window.sessionStorage) {
    users.forEach(user => {
      try {
        window.sessionStorage.setItem(
          `${cacheKeyPrefix}${trimEmpId(user.empId as string)}`,
          JSON.stringify(user),
        );
      } catch (err) {
        console.error(err);
        window.sessionStorage.removeItem(`${cacheKeyPrefix}${trimEmpId(user.empId as string)}`);
      }
    });
  }
}

export function isDepAccount(empId: string): boolean {
  return !/^\d+/.test(empId) && !empId?.startsWith('WB') && !empId?.startsWith('TW_');
}

/**
 * 针对目前接口无法获取到部门账号的情况，mock 一下用户信息结构，方便使用.
 * @param empId 工号
 */
export function mockUserInfo(empId: string): User {
  return {
    id: undefined,
    empId,
    nickName: empId,
    name: empId,
    bu: undefined,
    email: undefined,
    supervisorEmpId: undefined,
    supervisorName: undefined,
    DDTalkId: undefined,
    DDNick: undefined,
    DDTalkUrl: undefined,
    workUrl: `https://work.alibaba-inc.com/nwpipe/u/${empId}`,
    avatar: 'https://cdog01.alibaba-inc.com/aliwork_avatar/TFS_TO_OSS/T1fxt7FtJlXXXXXXXX_80x80',
    isAdmin: false,
    hrStatus: 'A',
    buDeptNo: undefined,
    ddnick: undefined,
    ddtalkId: undefined,
    ddtalkUrl: undefined,
  } as User;
}

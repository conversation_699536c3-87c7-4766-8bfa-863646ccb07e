import React from 'react';
import { Space, Tooltip } from 'antd';
import { UserMap } from '@/types/user';

const EmployeeList = (props: {
  empIds: string[];
  empInfoMap: UserMap;
  filterExist?: boolean;
}) => {
  let {
    empIds,
    empInfoMap,
  } = props;

  // 过滤掉不存在的用户
  if (props.filterExist && empIds?.length) {
    const existEmpIds = empIds.filter(empId => empId && empInfoMap?.[empId]);
    // 当只剩下不存在的用户时，则不过滤
    if (existEmpIds?.length) {
      empIds = existEmpIds;
    }
  }

  return (
    empIds?.length ? <Space size={[0, 8]} wrap>
      {empIds.map(empId => {
        const employee = empInfoMap?.[empId];
        return (
          <Tooltip key={empId} title={employee ? `${employee.nickNameCn} - ${employee.empId}` : empId}>
            <a
              target="_blank"
              rel="noreferrer"
              href={`https://work.alibaba-inc.com/nwpipe/u/${empId}`}
              className="mr-2 text-blue-500 hover:text-blue-700"
            >
              {employee ? employee.nickNameCn : empId}
            </a>
          </Tooltip>
        );
      })}
    </Space> : null
  );
};

export default EmployeeList;

import React, { useCallback, useEffect, useState } from 'react';
import { Avatar, Flex, SelectProps, Space } from 'antd';
import { isEmpty, uniqBy } from 'lodash-es';
import { useRequest } from 'ahooks';

import styles from './index.module.less';
import StatusTag from '@ali/mc-uikit/esm/StatusTag';
import PrefixSelect from '@ali/mc-uikit/esm/PrefixSelect';
import { trimEmpId } from '@/utils/common';
import { fuzzySearchUsers, queryUserByEmpIds } from '@/services/service';

interface User {
  id: string | null;
  empId: string;
  name: string;
  emailAddr: string;
  nickNameCn: string;
  emailPrefix: string;
  supervisorEmpId: string | null;
  hrStatus: string;
  displayName: string;
}

interface GetUserByEmpIdsParams {
  empIds: string[];
  batchSize?: number;
}

interface FetchUserByKeywordParams {
  keyword: string;
}

export type UserSelectProps = SelectProps;

function transformValue(value: string | string[]): string[] {
  if (!value || isEmpty(value)) {
    return [];
  }

  const empIds: string[] = [];
  if (typeof value === 'string') {
    empIds.push(...value.split(',').filter(item => item));
  } else {
    empIds.push(...value);
  }

  return empIds.map(trimEmpId);
}

export default function UserSelect(props: UserSelectProps) {
  const { mode = undefined, value: propsValue, onChange: onChangeProp, ...rest } = props;

  const [users, setUsers] = useState<User[]>([]);
  const { runAsync: requestByEmpIds, loading: fetchingById } = useRequest<
    Record<string, User>,
    [GetUserByEmpIdsParams]
  >(queryUserByEmpIds);

  const { runAsync: requestByKeyword, loading: fetchingByKeyword } = useRequest<
    User[],
    [FetchUserByKeywordParams]
  >(fuzzySearchUsers, {
    throttleWait: 300,
  });

  const fetchByEmpIds = useCallback(
    (empIds: string[]) => {
      if (!empIds || isEmpty(empIds)) {
        setUsers([]);
        return;
      }

      let reqEmpIds: string[] = [];
      if (Array.isArray(empIds)) {
        reqEmpIds = [...empIds];
      } else {
        reqEmpIds.push(empIds);
      }

      return requestByEmpIds({ empIds: reqEmpIds }).then(res => {
        const userInfos = reqEmpIds.map(empId => {
          // 这里要兼容一下工号自动补0的情况，参考 https://ata.atatech.org/articles/11000255565
          const user = Object.values(res || {}).find(
            item => trimEmpId(item.empId) === trimEmpId(empId),
          );
          if (user) {
            return user;
          }
          return { empId } as User;
        });

        setUsers(prev => {
          return uniqBy(prev.concat(userInfos), 'empId');
        });
      });
    },
    [requestByEmpIds],
  );

  useEffect(() => {
    const _value = transformValue(propsValue || '');
    fetchByEmpIds(_value);
  }, [fetchByEmpIds, propsValue]);

  const onSearch = async (keyword: string) => {
    if (keyword) {
      return requestByKeyword({
        keyword,
      }).then(res => {
        setUsers(prev => {
          const currentEmpIds = transformValue(propsValue || '');
          const currentUserInfos = prev.filter(item => currentEmpIds.includes(item.empId!));
          return uniqBy(currentUserInfos.concat(res || []), 'empId');
        });
      });
    }

    return [];
  };

  const onChange = (value: string, option: any) => {
    onChangeProp && onChangeProp(value, option);
  };

  const tagRender: SelectProps['tagRender'] = tagProps => {
    const { label, value, closable, onClose } = tagProps;
    const user = users.find(item => item.empId === value);

    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <StatusTag
        color="default"
        icon={
          user ? (
            <Avatar
              src={`https://img.alicdn.com/tfs/TB1.ZBecq67gK0jSZFHXXa9jVXa-288-288.png?avatar=${user.emailPrefix}`}
              size={16}
            />
          ) : null
        }
        className={styles.userTag}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        bordered={false}
        muted
      >
        {label}
      </StatusTag>
    );
  };

  return (
    // @ts-ignore
    <PrefixSelect
      className={styles.userSelect}
      showSearch
      allowClear
      popupClassName={styles.dropdown}
      placeholder={props.placeholder || '输入工号或者花名搜索用户'}
      style={{
        minWidth: '180px',
        width: props.style?.width || '100%',
      }}
      {...rest}
      tagRender={tagRender}
      loading={fetchingById || fetchingByKeyword}
      mode={mode}
      value={propsValue ? transformValue(propsValue) : undefined}
      onChange={onChange}
      onSearch={onSearch}
      filterOption={false}
      popupMatchSelectWidth={false}
      menuItemSelectedIcon={<></>}
      optionLabelProp="label"
      options={users.map(user => {
        return {
          label: `${user.displayName}(${user.empId})`,
          value: user.empId,
          user,
        };
      })}
      optionRender={oriOption => {
        const { displayName, empId, name, emailAddr } = oriOption.data.user;
        return (
          <Flex gap="small" className={styles.option} align="center">
            <Avatar src={`//work.alibaba-inc.com/photo/${empId}.80x80.jpg`} />
            <Space direction="vertical" size={0}>
              {`${displayName}(${name}) - ${empId}`}
              {emailAddr}
            </Space>
          </Flex>
        );
      }}
    />
  );
}

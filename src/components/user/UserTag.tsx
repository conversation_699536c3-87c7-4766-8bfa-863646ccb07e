import React from 'react';
import { Avatar, AvatarProps, Flex, Popover, theme, TooltipProps } from 'antd';
import CloseOutlined from '@ant-design/icons/CloseOutlined';

import type { User as IUser } from '@ali/mc-services';

import styles from './index.module.less';

const ddtalkIcon = 'https://img.alicdn.com/imgextra/i2/O1CN01B2L5om1vFXsRxYpVD_!!6000000006143-55-tps-16-16.svg';
const alineiwai = 'https://img.alicdn.com/imgextra/i1/O1CN01LokGPG1EhSXMaHE90_!!6000000000383-2-tps-64-64.png';

export type UserTagProps = {
    /**
     * 用户信息
     */
    data: IUser;

    /**
     * 是否可关闭
     */
    closable?: boolean;

    /**
     * 关闭回调
     */
    onClose?: () => void;

    /**
     * 头像尺寸
     */
    size?: AvatarProps['size'];

    /**
     * 头像形状
     */
    shape?: AvatarProps['shape'];

    /**
     * 是否显示头像
     */
    showAvatar?: boolean;

    /**
     * 是否显示花名
     */
    showName?: boolean;

    /**
     * 是否省略
     */
    ellipsis?: boolean | number;

    /**
     * 弹层位置
     */
    placement?: TooltipProps['placement'];
};
export default function UserTag(props: UserTagProps) {
    const {
        closable,
        onClose,
        size,
        showAvatar,
        showName = true,
        placement = 'top',
        shape = 'circle',
        ellipsis,
    } = props;

    const { token } = theme.useToken();

    const getUserTag = (userInfo: IUser) => {
        const {
            nickName,
            avatar,
            empId,
            workUrl,
            bu,
            DDTalkUrl,
            DDTalkId,
            name,
            hrStatus = 'A',
        } = userInfo;

        const title = (
          <Flex className={styles.title} justify="space-between">
            <Flex vertical flex="1">
              <Flex className={styles.nickNameBox} align="flex-start" gap="small">
                <span>{hrStatus === 'I' ? `${nickName}(已离职)` : nickName}</span>
                <Flex>
                  {DDTalkUrl && DDTalkId && (
                    <Avatar
                      className={styles.avatar}
                      shape="square"
                      size="small"
                      src={ddtalkIcon}
                      onClick={() => {
                                        window.open(`${DDTalkUrl}`, '_self');
                                    }}
                    />
                            )}
                  {workUrl && (
                    <Avatar
                      shape="square"
                      className={styles.avatar}
                      size="small"
                      src={alineiwai}
                      onClick={() => {
                                        window.open(workUrl);
                                    }}
                    />
                            )}
                </Flex>
              </Flex>
              <span className={styles.name}>{`${name} (${empId})`}</span>
            </Flex>
            <Avatar size="large" shape="square" src={avatar} />
          </Flex>
        );

        const content = <div className={styles.depDesc}>{bu}</div>;

        if (closable) {
            return (
              <div className={styles.userBtnGroup}>
                <Popover
                  trigger="hover"
                        // title={title}
                  content={
                    <Flex vertical>
                      {title}
                      {content}
                    </Flex>
                        }
                  placement={placement}
                >
                  <span className={styles.userName}>{nickName || name || empId}</span>
                </Popover>
                <CloseOutlined onClick={onClose} />
              </div>
            );
        }

        return (
          <Popover
            trigger="hover"
                // title={title}
            content={
              <Flex vertical gap="small">
                {title}
                {content}
              </Flex>
                }
            placement={placement}
            overlayClassName={styles.popover}
          >
            <Flex
              justify="flex-start"
              gap={token.paddingXXS}
              align="center"
              style={{
                        cursor: 'pointer',
                    }}
            >
              {showAvatar && (
                <Avatar
                  size={size}
                  src={avatar}
                  shape={shape}
                />
                    )}
              {showName && (
                <div
                  className={styles.trigger}
                  style={{
                                maxWidth: ellipsis ? (ellipsis === true ? 70 : ellipsis) : 'initial', // 这里能兼容大部分的 4 字名字，但是对于超长的部门账号、外文名称可能不太友好
                            }}
                >
                  {nickName || name || empId}
                </div>
                    )}
            </Flex>
          </Popover>
        );
    };

    return <div className={styles.user}>{getUserTag(props.data)}</div>;
}

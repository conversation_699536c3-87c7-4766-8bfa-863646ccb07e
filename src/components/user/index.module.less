.users {
  &-group {
    > div:not(:first-child),
    > span:not(:first-child) {
      margin-left: calc(0px - var(--mc-padding-sm));
    }

    :global {
      .mc-avatar {
        border-color: var(--mc-color-border-secondary);
      }
    }
  }

  .more {
    border-radius: 0;
    font-weight: var(--mc-font-weight-strong);
    border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-link);
    color: var(--mc-color-link) !important;
    background: transparent;
    cursor: pointer;
  }

  .trigger {
    flex: 1;
    line-height: var(--mc-line-height-sm);
    white-space: 'nowrap';
    overflow: 'hidden';
    text-overflow: 'ellipsis';
    max-width: 'initial';
    cursor: 'pointer';
  }
}

.popover {
  width: 276px;

  .title {
    .nickNameBox {
      > span:is(:first-child) {
        font-size: var(--mc-font-size-lg);
      }

      .avatar {
        cursor: pointer;
      }
    }

    .name {
      color: var(--mc-color-text-secondary);
      font-weight: normal;
    }
  }

  .depDesc {
    color: var(--mc-color-text-secondary);
    font-size: var(--mc-font-size-sm);
  }
}

.dropdown {
  .option {
    > :is(:last-child) > :is(:last-child) {
      color: var(--mc-color-text-secondary);
      font-weight: normal;
    }
  }
}

.userSelect {
  :global {
    .mc-select-selection-overflow-item {
      > span {
        display: inline-flex;
        // display: flex;
        align-items: center;

        .mc-tag {
          display: flex;
          align-items: center;
          padding-inline-start: calc(var(--mc-border-radius-sm) - var(--mc-margin-xs)); // 目前不同尺寸的tag 高度暂时不变，后续需要调整
        }
      }
    }
  }

  &:global {
    &.mc-select-sm {
      :global {
        .mc-select-selection-overflow-item {
          > span .mc-tag {
            height: 16px !important;
          }
        }
      }
    }
  }
}

import React from 'react';
import { Divider, Empty, Input, Tag } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { getTagColor } from '@/utils/condition';

interface ConditionSelectorProps {
  availableConditions: API.ConditionDetailDTO[];
  selectedConditions: API.ConditionDetailDTO[];
  searchCondition: string;
  onSearchChange: (value: string) => void;
  onSelectCondition: (condition: API.ConditionDetailDTO) => void;
  onCreateNewCondition: () => void;
}

const ConditionSelector: React.FC<ConditionSelectorProps> = ({
  availableConditions,
  selectedConditions,
  searchCondition,
  onSearchChange,
  onSelectCondition,
  onCreateNewCondition,
}) => {
  // 剩余未使用条件
  const filteredConditions = availableConditions
    .filter(
      c =>
        !selectedConditions.some(i => {
          return i.conditionId ? i.conditionId === c.conditionId : i.name === c.name;
        }),
    )
    .filter(c => c.name!.toLowerCase().includes(searchCondition.toLowerCase()));

  return (
    <div className="p-2 w-64">
      <Input
        placeholder="搜索条件"
        prefix={<SearchOutlined />}
        value={searchCondition}
        onChange={e => onSearchChange(e.target.value)}
        className="mb-2"
      />
      <div className="max-h-60 overflow-y-auto">
        {filteredConditions.length > 0 ? (
          filteredConditions.map(condition => (
            <div
              key={condition.conditionId}
              className="py-2 px-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => onSelectCondition(condition)}
            >
              <Tag color={getTagColor(condition)}>{condition.name}</Tag>
            </div>
          ))
        ) : (
          <Empty description="没有可用条件" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
      <Divider style={{ margin: '8px 0' }} />
      <div
        className="py-2 px-2 hover:bg-gray-100 cursor-pointer flex items-center"
        onClick={onCreateNewCondition}
      >
        <PlusOutlined className="mr-2" /> 创建新条件
      </div>
    </div>
  );
};

export default ConditionSelector;

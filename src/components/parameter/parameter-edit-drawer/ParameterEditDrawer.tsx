'use client';

import React from 'react';
import { Button, Divider, Drawer, Form, Input, message, Popover, Select, Tag } from 'antd';
import { MenuOutlined, PlusOutlined } from '@ant-design/icons';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import ConditionEditor, { ConditionData } from '../../condition/ConditionEditor';
import ConditionSelector from './ConditionSelector';
import ConditionValueInput from './ConditionValueInput';
import { useParameterEdit } from '@/hooks/useParameterEdit';
import { PARAMETER_TYPES } from '@/constants/parameter';
import { renderParameterTypeIcon } from '@/utils/parameter';
import type { ParameterEditDrawerProps, ParameterValueType } from '@/types/parameter';
import { JsonEditor } from '@/components';
import { GrRevert, GrTrash } from 'react-icons/gr';
import { COLORS } from '@/constants/condition';
import { getTagColor } from '@/utils/condition';
import { checkParameterKeyExists } from '@/services/orange-be/ParameterController';

const { TextArea } = Input;
const { Option } = Select;

const ParameterEditDrawer: React.FC<ParameterEditDrawerProps> = ({
  visible,
  onClose,
  editingParameter,
  onSave,
  availableConditions,
  mode = 'create',
  handleAddNewCondition,
  namespaceId,
}) => {
  const {
    form,
    parameter,
    setParameter,
    jsonEditorVisible,
    setJsonEditorVisible,
    currentJsonValue,
    setCurrentJsonValue,
    setCurrentJSONEditingConditionName,
    conditionSelectorVisible,
    setConditionSelectorVisible,
    searchCondition,
    setSearchCondition,
    newConditionModalVisible,
    setNewConditionModalVisible,
    handleConditionValueChange,
    handleConditionDeleted,
    handleRestoreCondition,
    handleJsonChange,
  } = useParameterEdit({
    editingParameter: mode === 'edit' ? editingParameter : undefined,
    visible,
  });

  const [viewConditionModalVisible, setViewConditionModalVisible] = React.useState(false);
  const [selectedCondition, setSelectedCondition] = React.useState<ConditionData | undefined>(
    undefined,
  );

  const handleSaveParameter = () => {
    form
      .validateFields()
      .then(() => {
        try {
          parameter && onSave({ ...parameter, ...form.getFieldsValue() });
          onClose();
        } catch (error: any) {
          message.error(error.message);
        }
      })
      .catch(error => {
        message.error(error.errorFields[0].errors[0]);
      });
  };

  const openJsonEditor = (conditionName: string) => {
    setCurrentJSONEditingConditionName(conditionName);
    setCurrentJsonValue(
      parameter.parameterConditions?.find(c => c.conditionName === conditionName)?.value || '',
    );
    setJsonEditorVisible(true);
  };

  const handleAddCondition = () => {
    setConditionSelectorVisible(!conditionSelectorVisible);
  };

  const handleSelectCondition = (condition: API.ConditionDetailDTO) => {
    const selectCondition = availableConditions.find(c =>
      (c.conditionId ? c.conditionId === condition.conditionId : c.name === condition.name),
    );
    if (selectCondition) {
      const newParameterConditions = [
        {
          conditionName: selectCondition.name!,
          conditionId: selectCondition.conditionId,
          conditionColor: selectCondition.color,
          value: '',
          changeType: 'CREATE' as const,
        },
        ...(parameter.parameterConditions || []),
      ];

      setParameter({
        ...parameter,
        parameterConditions: newParameterConditions,
      });

      form.setFieldsValue({
        [selectCondition.name!]: '',
      });
    }
    setConditionSelectorVisible(false);
  };

  const onClickAddNewCondition = () => {
    setConditionSelectorVisible(false);
    setNewConditionModalVisible(true);
  };

  const handleSaveNewCondition = (conditionData: ConditionData) => {
    if (availableConditions.findIndex(i => i.name === conditionData.name) !== -1) {
      message.error('已存在相同条件名的条件');
      return;
    }

    // 在现有参数条件列表头部追加
    const newParameterConditions = [
      {
        conditionName: conditionData.name,
        conditionColor: conditionData.color,
        value: '',
        changeType: 'CREATE' as const,
      },
      ...(parameter.parameterConditions || []),
    ];

    setParameter({
      ...parameter,
      parameterConditions: newParameterConditions,
    });

    form.setFieldsValue({
      [conditionData.name || '']: '',
    });

    // 调用 handleAddNewCondition 将条件添加到命名空间的条件列表中
    handleAddNewCondition?.({
      name: conditionData.name,
      color: COLORS[conditionData.color]?.value || conditionData.color,
      expression: {
        operator: 'AND',
        children: conditionData.expressionList,
      },
    });

    setNewConditionModalVisible(false);
  };

  const sortParameterConditions = (result: any) => {
    if (!result.destination || !parameter.parameterConditions?.length) {
      return;
    }

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;
    if (sourceIndex === destinationIndex) {
      return;
    }

    const newConditions = [...parameter.parameterConditions];
    const [removed] = newConditions.splice(sourceIndex, 1);
    newConditions.splice(destinationIndex, 0, removed);

    setParameter({
      ...parameter,
      parameterConditions: newConditions,
    });
  };

  const defaultConditionalValue = parameter.parameterConditions?.find(i => i.conditionId === '*')!;
  const noneDefaultConditionalValue =
    parameter.parameterConditions?.filter(i => i.conditionId !== '*') || [];

  const getTitle = () => {
    switch (mode) {
      case 'create':
        return '新增参数';
      case 'edit':
        return '编辑参数';
      default:
        return '参数';
    }
  };

  const handleViewCondition = (conditionName: string) => {
    const condition = availableConditions.find(c => c.name === conditionName);
    if (condition) {
      setSelectedCondition({
        id: condition.conditionId!,
        name: condition.name!,
        color: condition.color || '',
        expressionList: (condition.expression?.children || []).map((item: any) => ({
          id: `criteria-${Date.now()}-${Math.random()}`,
          key: item.key,
          operator: item.operator,
          value: item.value,
        })),
      });
      setViewConditionModalVisible(true);
    }
  };

  const existingParameter = !!parameter.parameterId;

  return (
    <>
      <Drawer
        title={getTitle()}
        placement="right"
        width={720}
        onClose={onClose}
        open={visible}
        extra={
          <Button type="primary" onClick={handleSaveParameter}>
            保存
          </Button>
        }
      >
        <Form form={form} layout="vertical" initialValues={parameter}>
          <div className="flex gap-4">
            <Form.Item
              name="parameterKey"
              label="参数名"
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: '请输入参数名',
                },
                {
                  max: 64,
                  message: '参数名最长64个字符',
                },
                {
                  validator: async (_, value) => {
                    // 仅在创建模式下进行唯一性校验
                    if (mode !== 'create') {
                      return Promise.resolve();
                    }
                    const trimmed = (value || '').trim();
                    if (!trimmed) {
                      return Promise.resolve();
                    }
                    try {
                      const res = await checkParameterKeyExists({
                        namespaceId,
                        parameterKey: trimmed,
                      });
                      if (res?.data) {
                        return Promise.reject(new Error('该参数名已存在'));
                      }
                      return Promise.resolve();
                    } catch (e) {
                      // 忽略网络/接口错误，不阻塞用户输入
                      return Promise.resolve();
                    }
                  },
                },
              ]}
              className="flex-1"
            >
              <Input maxLength={64} placeholder="请输入参数名" disabled={mode !== 'create'} />
            </Form.Item>

            <Form.Item
              name="valueType"
              label="参数类型"
              rules={[
                {
                  required: true,
                  message: '请选择参数类型',
                },
              ]}
              className="w-40"
            >
              <Select
                disabled={mode !== 'create'}
                onChange={value => {
                  const clearedParameterConditions =
                    parameter.parameterConditions?.map(condition => ({
                      ...condition,
                      value: '',
                      changeType: condition.changeType || 'UPDATE',
                    })) || [];

                  setParameter({
                    ...parameter,
                    valueType: value as ParameterValueType,
                    parameterConditions: clearedParameterConditions,
                  });

                  const formValues: any = {};
                  parameter.parameterConditions?.forEach(condition => {
                    if (condition.conditionName) {
                      formValues[condition.conditionName] = '';
                    }
                  });
                  form.setFieldsValue(formValues);
                }}
              >
                {PARAMETER_TYPES.map(type => (
                  <Option key={type.value} value={type.value}>
                    <div className="flex items-center">
                      {renderParameterTypeIcon(type.value, {
                        width: 16,
                        height: 16,
                      })}
                      <span className="ml-2">{type.label}</span>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            label="参数描述"
            name="description"
            rules={[
              {
                required: true,
                message: '请输入参数描述',
              },
              {
                max: 250,
                message: '参数描述最长250个字符',
              },
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请输入描述"
              maxLength={250}
              disabled={existingParameter}
            />
          </Form.Item>

          <Divider orientation="center">条件</Divider>

          <div className="mb-4 flex">
            <Popover
              content={
                <ConditionSelector
                  availableConditions={availableConditions}
                  selectedConditions={
                    parameter.parameterConditions?.map(c => ({
                      conditionId: c.conditionId!,
                      name: c.conditionName!,
                      color: c.conditionColor,
                    })) || []
                  }
                  searchCondition={searchCondition}
                  onSearchChange={setSearchCondition}
                  onSelectCondition={handleSelectCondition}
                  onCreateNewCondition={onClickAddNewCondition}
                />
              }
              trigger="click"
              open={conditionSelectorVisible}
              onOpenChange={open => {
                if (open) {
                  setSearchCondition('');
                }
                setConditionSelectorVisible(open);
              }}
              placement="bottomLeft"
            >
              <Button type="dashed" onClick={handleAddCondition} icon={<PlusOutlined />}>
                添加条件
              </Button>
            </Popover>
          </div>

          <DragDropContext onDragEnd={sortParameterConditions}>
            <Droppable droppableId="droppable-conditions">
              {provided => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {noneDefaultConditionalValue.map((conditionalValue, index) => {
                    const { conditionName } = conditionalValue;
                    const isDeleted = conditionalValue.changeType === 'DELETE';

                    return (
                      <Draggable key={conditionName} draggableId={conditionName!} index={index}>
                        {p => (
                          <div
                            ref={p.innerRef}
                            {...p.draggableProps}
                            className="flex items-center mb-4 parameter-condition-item"
                          >
                            <div className="flex items-center mr-2 w-[200px]">
                              <div {...p.dragHandleProps}>
                                <MenuOutlined className="text-gray-400 mr-2 cursor-move" />
                              </div>
                              <Popover content="点击查看条件详情">
                                <Tag
                                  color={getTagColor(conditionalValue)}
                                  className={`${
                                    isDeleted ? 'line-through' : ''
                                  } max-w-[160px] truncate cursor-pointer`}
                                  title={conditionName}
                                  onClick={() => handleViewCondition(conditionName!)}
                                >
                                  {conditionName}
                                </Tag>
                              </Popover>
                            </div>
                            <div className="flex-1 relative">
                              <ConditionValueInput
                                condition={conditionalValue}
                                valueType={parameter.valueType || 'STRING'}
                                isDeleted={isDeleted}
                                isViewMode={false}
                                onValueChange={value =>
                                  handleConditionValueChange(conditionName!, value)}
                                onOpenJsonEditor={() => openJsonEditor(conditionName!)}
                              />
                            </div>
                            <div className="w-[32px]">
                              <Button
                                type="text"
                                icon={isDeleted ? <GrRevert /> : <GrTrash />}
                                onClick={() => {
                                  isDeleted
                                    ? handleRestoreCondition(conditionalValue.conditionId!)
                                    : handleConditionDeleted(conditionalValue);
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </Draggable>
                    );
                  })}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          <div className="flex items-center mb-4" style={{ marginBottom: '16px' }}>
            <div className="flex items-center mr-2 w-[200px]">
              <div className="w-[24px]" />
              <Tag className="max-w-[160px] truncate" title="默认">
                默认
              </Tag>
            </div>
            <div className="flex-1 relative">
              <ConditionValueInput
                condition={defaultConditionalValue}
                valueType={parameter.valueType || 'STRING'}
                isDeleted={false}
                isViewMode={false}
                onValueChange={value => handleConditionValueChange('默认', value)}
                onOpenJsonEditor={() => openJsonEditor('默认')}
              />
            </div>
            <div className="w-[32px]" />
          </div>
        </Form>
      </Drawer>

      <JsonEditor
        visible={jsonEditorVisible}
        onClose={() => setJsonEditorVisible(false)}
        value={currentJsonValue}
        onChange={handleJsonChange}
      />

      <ConditionEditor
        visible={newConditionModalVisible}
        onClose={() => setNewConditionModalVisible(false)}
        onSave={handleSaveNewCondition}
        mode="create"
        existingConditions={availableConditions.map(condition => ({
          id: condition.conditionId!,
          name: condition.name!,
          color: condition.color || '',
          expressionList: (condition.expression?.children || []) as any,
        }))}
        namespaceId={namespaceId}
      />

      <ConditionEditor
        visible={viewConditionModalVisible}
        onClose={() => setViewConditionModalVisible(false)}
        mode="view"
        initialData={selectedCondition}
        namespaceId={namespaceId}
      />
    </>
  );
};

export default ParameterEditDrawer;

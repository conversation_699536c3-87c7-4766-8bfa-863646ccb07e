import React from 'react';
import { Form, Input, InputNumber, Select } from 'antd';
import { ExpandAltOutlined } from '@ant-design/icons';
import type { ParameterCondition, ParameterValueType } from '@/types/parameter';

const { Option } = Select;

interface ConditionValueInputProps {
  condition: ParameterCondition;
  valueType: ParameterValueType;
  isDeleted: boolean;
  isViewMode: boolean;
  onValueChange: (value: string) => void;
  onOpenJsonEditor?: () => void;
}

const ConditionValueInput: React.FC<ConditionValueInputProps> = ({
  condition,
  valueType,
  isDeleted,
  isViewMode,
  onValueChange,
  onOpenJsonEditor,
}) => {
  const { conditionName = '' } = condition;

  switch (valueType) {
    case 'BOOLEAN':
      return (
        <Form.Item
          name={conditionName}
          rules={[
            {
              required: true,
              message: '请选择布尔值',
            },
          ]}
          className="m-0"
        >
          <Select
            disabled={isDeleted || isViewMode}
            className="w-full"
            onChange={value => onValueChange(String(value))}
            placeholder="请选择布尔值"
          >
            <Option value="true">true</Option>
            <Option value="false">false</Option>
          </Select>
        </Form.Item>
      );
    case 'DOUBLE':
      return (
        <Form.Item
          name={conditionName}
          rules={[
            {
              required: true,
              message: '请输入浮点数',
            },
            {
              pattern: /^-?\d+(\.\d+)?$/,
              message: '请输入合法的浮点数',
            },
            {
              validator: (_, value) => {
                // 仅统计数字位数，忽略符号位和小数点
                if (value && String(value).replace(/[.\-]/g, '').length > 15) {
                  return Promise.reject(new Error('数字长度不能超过15位'));
                }
                return Promise.resolve();
              },
            },
          ]}
          className="m-0"
        >
          <InputNumber
            disabled={isDeleted || isViewMode}
            className="w-full"
            placeholder="请输入浮点数，最长 15 位数字"
            onChange={value => onValueChange(String(value ?? ''))}
            step={0.1}
            stringMode
          />
        </Form.Item>
      );
    case 'LONG':
      return (
        <Form.Item
          name={conditionName}
          rules={[
            {
              required: true,
              message: '请输入整数',
            },
            {
              pattern: /^-?\d+$/,
              message: '请输入整数',
            },
            {
              // 自定义长度校验：仅统计数字位数，忽略符号位
              validator: (_, value) => {
                if (value && String(value).replace(/\-/g, '').length > 19) {
                  return Promise.reject(new Error('数字长度不能超过19位'));
                }
                return Promise.resolve();
              },
            },
          ]}
          className="m-0"
        >
          <InputNumber
            disabled={isDeleted || isViewMode}
            className="w-full"
            placeholder="请输入整数，最长 19 位数字"
            onChange={value => onValueChange(String(value ?? ''))}
            step={1}
            stringMode
          />
        </Form.Item>
      );
    case 'JSON':
      return (
        <Form.Item
          className="m-0"
          name={conditionName}
          validateTrigger="onBlur"
          rules={[
            {
              required: true,
              message: '请输入参数值',
            },
            () => ({
              validator(_, value) {
                try {
                  if (value) {
                    const parsed = JSON.parse(value);
                    if (typeof parsed !== 'object' || parsed === null) {
                      return Promise.reject(new Error('请输入合法 JSON 参数值'));
                    }
                  }
                  return Promise.resolve();
                } catch (error) {
                  return Promise.reject(new Error('请输入合法 JSON 参数值'));
                }
              },
            }),
          ]}
        >
          <Input
            disabled={isDeleted || isViewMode}
            placeholder="请输入JSON"
            onChange={e => onValueChange(e.target.value)}
            addonAfter={
              onOpenJsonEditor && (
                <ExpandAltOutlined
                  onClick={() => !isViewMode && onOpenJsonEditor()}
                  style={{ cursor: isViewMode ? 'not-allowed' : 'pointer' }}
                />
              )
            }
          />
        </Form.Item>
      );
    default:
      return (
        <Form.Item
          name={conditionName}
          rules={[
            {
              required: true,
              message: '请输入字符串',
              type: 'string',
              max: 500,
            },
          ]}
          className="m-0"
        >
          <Input
            disabled={isDeleted || isViewMode}
            placeholder="请输入字符串，最长 500"
            onChange={e => onValueChange(e.target.value)}
          />
        </Form.Item>
      );
  }
};

export default ConditionValueInput;

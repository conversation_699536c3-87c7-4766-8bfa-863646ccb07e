'use client';

import React, { useState } from 'react';
import { Button, Input, message, Modal, Popover } from 'antd';
import { FaWandMagicSparkles } from 'react-icons/fa6';
import ParameterDiffView, { ParameterCompare } from './ParameterDiffView';
import type { Parameter } from '@/types/parameter';
import services from '@/services/orange-be';

interface PublishChangesModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (changeDescription: string) => void;
  changedParameters: Parameter[];
  originalParameters: Parameter[];
  newConditions?: any[];
}

const MIN_DESC_LENGTH = 5;
const MAX_DESC_LENGTH = 100;
const { AIController } = services;

const PublishChangesModal: React.FC<PublishChangesModalProps> = ({
  visible,
  onClose,
  onConfirm,
  changedParameters,
  originalParameters,
  newConditions = [],
}) => {
  const [changeDescription, setChangeDescription] = useState('');
  const [descriptionGenerating, setDescriptionGenerating] = useState(false);

  // 检查变更描述是否满足最小长度要求
  const isChangeDescriptionValid =
    changeDescription.trim().length >= MIN_DESC_LENGTH &&
    changeDescription.trim().length <= MAX_DESC_LENGTH;

  // Transform the data format to match the diff view requirements
  const transformData = () => {
    const parameterChanges: ParameterCompare[] = changedParameters.map(changedParam => {
      const originalParam = Array.isArray(originalParameters)
        ? originalParameters.find(p => p.parameterId === changedParam.parameterId)
        : null;

      // 计算条件顺序变更
      const originalConditions = originalParam?.parameterConditions || [];
      const changedConditions = changedParam.parameterConditions || [];

      // 创建条件ID到索引的映射
      const originalIndexMap = new Map(
        originalConditions.map((cond, index) => [cond.conditionId, index]),
      );
      const changedIndexMap = new Map(
        changedConditions.map((cond, index) => [cond.conditionId, index]),
      );

      return {
        parameterKey: changedParam.parameterKey || '',
        valueType: changedParam.valueType || 'STRING',
        changeType: changedParam.changeType,
        originalConditions:
          originalConditions.map(cond => {
            return {
              conditionId: cond.conditionId || '',
              conditionName: cond.conditionName || '',
              value: cond.value || '',
              color: cond.conditionColor,
              changeType: cond.changeType,
            };
          }) || [],
        changedConditions:
          changedConditions.map(cond => {
            const originalIndex = originalIndexMap.get(cond.conditionId);
            const newIndex = changedIndexMap.get(cond.conditionId);
            return {
              conditionId: cond.conditionId || '',
              conditionName: cond.conditionName || '',
              value: cond.value || '',
              color: cond.conditionColor,
              changeType: cond.changeType,
              isOrderChanged: originalIndex !== undefined && originalIndex !== newIndex,
              originalIndex: originalIndex,
              newIndex: newIndex,
            };
          }) || [],
      };
    });

    // 仅保留在 changedParameters 的 changedConditions 中被引用到的新增条件（通过 id 或 name 匹配）
    const usedConditionIds = new Set<string>();
    const usedConditionNames = new Set<string>();

    changedParameters.forEach(param => {
      const paramConditions = (param as any)?.parameterConditions || [];
      paramConditions.forEach((cond: any) => {
        if (cond && cond.conditionId) {
          usedConditionIds.add(cond.conditionId);
        }
        if (cond && cond.conditionName) {
          usedConditionNames.add(cond.conditionName);
        }
      });
    });

    const referencedNewConditions = newConditions.filter(cond => {
      return (cond?.id && usedConditionIds.has(cond?.id)) || (cond.name && usedConditionNames.has(cond.name));
    });

    return {
      parameterChanges,
      conditionChanges: referencedNewConditions.map(cond => ({
        id: cond.id || '',
        name: cond.name || '',
        color: cond.color || '',
        expression: cond.expression,
        changeType: 'CREATE' as const,
      })),
    };
  };

  const { parameterChanges, conditionChanges } = transformData();

  const handleGenerateDescription = async () => {
    if (descriptionGenerating) {
      return; // 防止重复调用
    }

    setDescriptionGenerating(true);
    try {
      const changes = {
        parameterChanges,
        conditionChanges,
      };

      const response = await AIController.idealabPredict({
        appCode: 'pBjyANBNeVl',
        variableMap: {
          changes: JSON.stringify(changes),
        },
      });

      setChangeDescription(response.data);
    } catch (error) {
      message.error('生成变更描述失败');
    } finally {
      setDescriptionGenerating(false);
    }
  };

  return (
    <Modal
      title="配置变更"
      open={visible}
      onCancel={onClose}
      footer={
        <div className="flex items-center justify-between w-full">
          <div className="flex-1 mr-4">
            <Input
              maxLength={MAX_DESC_LENGTH}
              minLength={MIN_DESC_LENGTH}
              placeholder={`请输入本次变更描述，至少 ${MIN_DESC_LENGTH} 个字`}
              value={changeDescription}
              onChange={e => setChangeDescription(e.target.value)}
              addonAfter={
                // @ts-ignore
                <FaWandMagicSparkles
                  onClick={handleGenerateDescription}
                  style={{
                    cursor: descriptionGenerating ? 'not-allowed' : 'pointer',
                    color: descriptionGenerating ? '#d9d9d9' : '#1677ff',
                  }}
                  title={descriptionGenerating ? '生成中...' : '生成描述'}
                />
              }
            />
          </div>
          <div>
            <Button className="mr-2" onClick={onClose}>
              取消
            </Button>
            <Popover
              content={
                isChangeDescriptionValid ? null : `请先输入变更描述，至少 ${MIN_DESC_LENGTH} 个字`
              }
              open={isChangeDescriptionValid ? false : undefined}
            >
              <Button
                type="primary"
                disabled={!isChangeDescriptionValid}
                onClick={() => onConfirm(changeDescription)}
              >
                确定发布
              </Button>
            </Popover>
          </div>
        </div>
      }
      width={1200}
      className="p-0"
    >
      <ParameterDiffView parameterChanges={parameterChanges} conditionChanges={conditionChanges} />
    </Modal>
  );
};

export default PublishChangesModal;

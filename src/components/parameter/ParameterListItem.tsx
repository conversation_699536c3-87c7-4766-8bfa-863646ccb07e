'use client';

import React, { useState } from 'react';

import { history } from 'ice';
import { Button, Checkbox, Dropdown, Input, List, Tag, Tooltip } from 'antd';
import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  FileTextOutlined,
  MoreOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { Parameter, ParameterCondition } from '@/types/parameter';
import { getBgColor, renderParameterTypeIcon } from '@/utils/parameter';
import {
  PARAMETER_TYPE_MAP,
  parameterChangeType2Color,
  parameterChangeType2Text,
} from '@/constants/parameter';
import { ContentViewer } from '@/components';
import { User } from '@/components/user';
import { useUserStore } from '@/store';
import { getFormatDate } from '@/utils/common';
import { getTagColor } from '@/utils/condition';
import ParameterDirectUpdateDTO = API.ParameterDirectUpdateDTO;

interface ParameterListItemProps {
  item: Parameter;
  isExpanded: boolean;
  isSelected: boolean;
  onToggleExpand: (key: string) => void;
  onEdit: (parameter: Parameter) => void;
  onUpdate: (parameter: ParameterDirectUpdateDTO) => Promise<void>;
  onDelete: (key: string) => void;
  onSelect: (key: string, checked: boolean) => void;
  onConditionClick: (condition: ParameterCondition) => void;
}

const ParameterListItem: React.FC<ParameterListItemProps> = ({
  item,
  isExpanded,
  isSelected,
  onToggleExpand,
  onEdit,
  onUpdate,
  onDelete,
  onSelect,
  onConditionClick,
}) => {
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [description, setDescription] = useState(item.description || '');
  const isChanged = !!item.changeType;
  const hasConditions = !!item.parameterConditions?.length;
  const inPublish = !!item.inPublishReleaseOrder;
  const { parameterKey } = item;
  const currentUser = useUserStore(state => state.currentUser);
  const initParameter = item.status === 'INIT';

  const handleDescriptionSave = async () => {
    await onUpdate({
      parameterId: item.parameterId!,
      description,
    });
    setIsEditingDescription(false);
  };

  const getTypeIcon = () => {
    const type = PARAMETER_TYPE_MAP[item.valueType!];
    if (!type) return null;

    return (
      <Tooltip title={type.label}>
        {renderParameterTypeIcon(item.valueType!, {
          width: 16,
          height: 16,
        })}
      </Tooltip>
    );
  };

  const mainLine = (
    <div className="flex w-full items-center">
      <div className="flex items-start gap-1 flex-1">
        <div className="flex items-center">
          <Checkbox
            className="mr-1"
            disabled={!isChanged}
            checked={isSelected}
            onChange={e => onSelect(parameterKey!, e.target.checked)}
          />
          {hasConditions && !initParameter ? (
            <RightOutlined
              className={`text-gray-500 p-1 transform transition-transform ${
                isExpanded ? 'rotate-90' : ''
              }`}
              onClick={() => onToggleExpand(parameterKey!)}
            />
          ) : (
            <div className="w-5" /> // 占位，保持对齐
          )}
        </div>
        {/* 参数信息 */}
        <div className="flex-1">
          <div className="flex items-center">
            {getTypeIcon()}
            <span
              className={`ml-2 font-medium ${
                item.changeType === 'DELETE' ? 'line-through text-gray-500' : ''
              }`}
            >
              {parameterKey}
            </span>
            {item.changeType && (
              <Tag color={parameterChangeType2Color[item.changeType]} className={'ml-2'}>
                {parameterChangeType2Text[item.changeType]}
              </Tag>
            )}
            {inPublish && (
              <Tooltip title={`点击跳转发布单: RO-${item.inPublishReleaseOrder?.releaseVersion}`}>
                <Tag
                  color="orange"
                  className={'ml-2 cursor-pointer'}
                  onClick={() => {
                    history?.push(
                      `/workspace/switch/release-orders/${item.inPublishReleaseOrder?.releaseVersion}`,
                    );
                  }}
                >
                  发布中
                </Tag>
              </Tooltip>
            )}
          </div>

          <div className="flex items-center mt-1 text-xs text-gray-500">
            <User
              empIds={item.changeType ? [currentUser.empId!] : [item.latestPublishUser!]}
              showAvatar
              size={'xsmall'}
            />
            <span className="ml-2">
              {item.changeType
                ? `修改于 ${item.latestModifyTime}`
                : initParameter
                ? '暂未发布上线'
                : `发布于 ${getFormatDate(item.gmtModified!)}`}
            </span>
          </div>

          <div className="mt-1 text-xs text-gray-500 flex items-center">
            <FileTextOutlined className="mr-1" />
            {isEditingDescription ? (
              <div className="flex items-center flex-1">
                <Input
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  className="w-1/2"
                  autoFocus
                  onPressEnter={handleDescriptionSave}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => {
                    setDescription(item.description || '');
                    setIsEditingDescription(false);
                  }}
                  className="ml-1 text-gray-500"
                />
              </div>
            ) : (
              <div className="flex items-center flex-1 group">
                <span className="truncate max-w-md ml-1">{item.description}</span>
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  size="small"
                  className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => setIsEditingDescription(true)}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 条件个数 */}
      <div className="flex items-center">
        <div className="mr-14">
          <div className="text-xs">{item.parameterConditions?.length} 个条件值</div>
        </div>
      </div>

      {/* 操作列 */}
      <div className="flex items-center text-xs">
        <Tooltip title={inPublish ? '发布中禁止编辑' : ''}>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(item)}
            style={{ color: inPublish ? '#999' : '' }}
            disabled={item.changeType === 'DELETE' || inPublish}
          >
            <span>编辑</span>
          </Button>
        </Tooltip>
        <Dropdown
          disabled={inPublish}
          menu={{
            items: [
              {
                key: 'delete',
                label: '删除',
                icon: <DeleteOutlined />,
                onClick: () => onDelete(parameterKey!),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} disabled={item.changeType === 'DELETE'} />
        </Dropdown>
      </div>
    </div>
  );

  return (
    <List.Item className={`${getBgColor(item.changeType)} w-full flex-col gap-2`}>
      {mainLine}

      {isExpanded && !initParameter && item.parameterConditions && (
        <List
          bordered
          className={'w-full'}
          dataSource={item.parameterConditions}
          renderItem={(conditionalValue: ParameterCondition) => (
            <List.Item
              key={conditionalValue.conditionName}
              className={`${conditionalValue.changeType === 'DELETE' ? 'opacity-60' : ''}`}
            >
              <div className="flex items-center text-xs text-gray-500">
                <User
                  empIds={
                    conditionalValue.changeType
                      ? [currentUser.empId!]
                      : [conditionalValue.latestPublishUser!]
                  }
                  showAvatar
                  size={'xsmall'}
                />
                <span className={'ml-2'}>
                  {conditionalValue.changeType
                    ? `修改于 ${item.latestModifyTime}`
                    : `发布于 ${getFormatDate(conditionalValue.gmtModified!)}`}
                </span>
                {conditionalValue.changeType && (
                  <Tag
                    color={parameterChangeType2Color[conditionalValue.changeType]}
                    className={'ml-2'}
                  >
                    {parameterChangeType2Text[conditionalValue.changeType]}
                  </Tag>
                )}
              </div>
              <div className="flex items-center justify-end w-2/3">
                <div className="mr-2">
                  <Tooltip title={conditionalValue.conditionId === '*' ? null : '点击查看条件详情'}>
                    <Tag
                      style={{
                        cursor: conditionalValue.conditionId === '*' ? 'not-allowed' : 'pointer',
                      }}
                      onClick={() => {
                        if (conditionalValue.conditionId !== '*') {
                          onConditionClick(conditionalValue);
                        }
                      }}
                      color={getTagColor(conditionalValue)}
                      className={
                        conditionalValue.changeType === 'DELETE'
                          ? 'line-through cursor-pointer'
                          : 'cursor-pointer'
                      }
                    >
                      {conditionalValue.conditionName}
                    </Tag>
                  </Tooltip>
                </div>
                <div className="w-1/2">
                  <ContentViewer content={conditionalValue.value!} contentType={item.valueType!} />
                </div>
              </div>
            </List.Item>
          )}
        />
      )}
    </List.Item>
  );
};

export default ParameterListItem;

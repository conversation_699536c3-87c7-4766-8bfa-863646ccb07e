'use client';

import React, { useState } from 'react';
import { Badge, Popover, Spin, Tabs, Tag } from 'antd';
import {
  EditOutlined,
  MinusOutlined,
  PlusOutlined,
  RightOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { ContentViewer } from '@/components/common';
import { ParameterValueType } from '@/types/parameter';
import { CONDITION_KEYS, DEFAULT_CONDITION_COLOR, OPERATORS } from '@/constants/condition';
import { useConditionExpression } from '@/hooks/useConditionExpression';
import { getTagColor } from '@/utils/condition';

const { TabPane } = Tabs;

// Constants
const CHANGE_TYPES = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ORDER: 'ORDER',
} as const;

const TAB_KEYS = {
  PARAMETERS: 'parameters',
  CONDITIONS: 'conditions',
} as const;

const TABLE_COLUMNS = {
  NAME: {
    width: '20%',
    label: '参数名',
  },
  CONDITION: {
    width: '25%',
    label: '条件',
  },
  VALUE: {
    width: '55%',
    label: '值',
  },
} as const;

// Type definitions
interface ParameterCondition {
  conditionId?: string;
  conditionName: string;
  value: string;
  color?: string;
  changeType?: (typeof CHANGE_TYPES)[keyof typeof CHANGE_TYPES];
  isOrderChanged?: boolean;
  originalIndex?: number;
  newIndex?: number;
}

export interface ParameterCompare {
  parameterKey: string;
  expanded?: boolean;
  changeType?: (typeof CHANGE_TYPES)[keyof typeof CHANGE_TYPES];
  hasOrderChanges?: boolean;
  valueType: ParameterValueType;
  originalConditions?: ParameterCondition[];
  changedConditions?: ParameterCondition[];
}

interface ConditionCompare {
  id: string;
  name: string;
  color: string;
  expression: any;
  changeType: (typeof CHANGE_TYPES)[keyof typeof CHANGE_TYPES];
}

interface ParameterDiffViewProps {
  parameterChanges: ParameterCompare[];
  conditionChanges?: ConditionCompare[];
  loading?: boolean;
  showVersionInfo?: boolean;
  previousVersion?: string;
  currentVersion?: string;
  previousNamespace?: {
    conditionSnapshots?: Array<{
      conditionId: string;
      name: string;
      expression?: any;
    }>;
  };
}

// Helper components
const ChangeIcon: React.FC<{ changeType: keyof typeof CHANGE_TYPES }> = ({ changeType }) => {
  const iconProps = {
    className: `mr-2 ${
      changeType === CHANGE_TYPES.CREATE
        ? 'text-green-500'
        : changeType === CHANGE_TYPES.DELETE
        ? 'text-red-500'
        : changeType === CHANGE_TYPES.UPDATE
        ? 'text-yellow-500'
        : 'text-blue-500'
    }`,
  };

  switch (changeType) {
    case CHANGE_TYPES.CREATE:
      return <PlusOutlined {...iconProps} />;
    case CHANGE_TYPES.DELETE:
      return <MinusOutlined {...iconProps} />;
    case CHANGE_TYPES.UPDATE:
      return <EditOutlined {...iconProps} />;
    case CHANGE_TYPES.ORDER:
      return <SwapOutlined {...iconProps} />;
    default:
      return null;
  }
};

const EmptyLine: React.FC = () => (
  <tr className="border-t border-gray-100">
    <td className="p-3" colSpan={3}>
      <div style={{ height: 22 }} />
    </td>
  </tr>
);

const ParameterRow: React.FC<{
  param: ParameterCompare | null;
  isExpanded: boolean;
  onToggle: () => void;
  showChangeIcon?: boolean;
}> = ({ param, isExpanded, onToggle, showChangeIcon = false }) => {
  if (!param) {
    return <EmptyLine />;
  }

  const getRowClassName = () => {
    if (param.changeType === CHANGE_TYPES.CREATE) return 'bg-green-50';
    if (param.changeType === CHANGE_TYPES.DELETE) return 'bg-red-50';
    if (param.changeType === CHANGE_TYPES.UPDATE) return 'bg-yellow-50';
    return '';
  };

  return (
    <tr className={`border-t border-gray-100 ${showChangeIcon ? getRowClassName() : ''}`}>
      <td className="p-3" colSpan={3}>
        <div className="flex items-center cursor-pointer" onClick={onToggle}>
          <RightOutlined
            className={`mr-2 transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}
          />
          {showChangeIcon && param.changeType && <ChangeIcon changeType={param.changeType} />}
          {showChangeIcon && param.hasOrderChanges && (
            <ChangeIcon changeType={CHANGE_TYPES.ORDER} />
          )}
          <span className={param.changeType === CHANGE_TYPES.DELETE ? 'line-through' : ''}>
            {param.parameterKey}
          </span>
        </div>
      </td>
    </tr>
  );
};

// 条件表达式查找组件
// fixme
// @ts-ignore
const ConditionExpressionLookup: React.FC<{
  conditionId: string;
  conditionChanges?: ConditionCompare[];
  previousNamespace?: {
    conditionSnapshots?: Array<{
      conditionId: string;
      name: string;
      expression?: any;
    }>;
  };
  isOldVersion: boolean;
}> = ({ conditionId, conditionChanges, previousNamespace, isOldVersion }) => {
  if (conditionId === '*') {
    return null;
  }
  // 优先从 conditionChanges 中查找（右边列）
  const conditionChange = conditionChanges?.find(c => c.id === conditionId);

  // 从 previousNamespace.conditionSnapshots 中查找（左边列或兜底）
  const conditionSnapshot = previousNamespace?.conditionSnapshots?.find(
    c => c.conditionId === conditionId,
  );

  // 解析字符串表达式为对象格式
  const parseExpression = (expr: any) => {
    if (!expr) return null;

    if (typeof expr === 'string') {
      try {
        const parsed = JSON.parse(expr);
        // 确保解析后的对象有必要的字段
        if (parsed && typeof parsed === 'object' && (parsed.key || parsed.children)) {
          return parsed;
        }
        return null;
      } catch (e) {
        console.warn('Failed to parse expression:', expr, e);
        return null;
      }
    }

    // 如果已经是对象，确保有必要的字段
    if (expr && typeof expr === 'object' && (expr.key || expr.children)) {
      return expr;
    }

    return null;
  };

  // 确定使用哪个表达式
  let expression = null;
  if (isOldVersion) {
    // 左边列：优先从 previousNamespace.conditionSnapshots 中查找
    expression = parseExpression(conditionSnapshot?.expression);
  } else {
    // 右边列：优先从 conditionChanges 中查找，找不到再从 previousNamespace.conditionSnapshots 中查找
    expression = parseExpression(conditionChange?.expression || conditionSnapshot?.expression);
  }

  // 如果都没有找到，使用 hook 调用 getByConditionId
  const {
    expression: fetchedExpression,
    loading,
    error,
  } = useConditionExpression({
    conditionId: !expression ? conditionId : '',
  });

  if (expression) {
    return (
      <div className="font-mono bg-gray-50 px-3 py-2 rounded flex-1">
        {formatExpression(expression)}
      </div>
    );
  }

  if (loading) {
    return <div className="text-sm text-gray-500">加载中...</div>;
  }

  if (error) {
    return <div className="text-sm text-red-500">加载失败</div>;
  }

  if (fetchedExpression) {
    return (
      <div className="font-mono bg-gray-50 px-3 py-2 rounded flex-1">
        {formatExpression(fetchedExpression)}
      </div>
    );
  }

  return <div className="text-sm text-gray-500">暂无表达式</div>;
};

const ConditionRow: React.FC<{
  condition: ParameterCondition;
  valueType: ParameterValueType;
  isOldVersion: boolean;
  paramChangeType?: (typeof CHANGE_TYPES)[keyof typeof CHANGE_TYPES];
  conditionChanges?: ConditionCompare[];
  previousNamespace?: {
    conditionSnapshots?: Array<{
      conditionId: string;
      name: string;
      expression?: any;
    }>;
  };
}> = ({
  condition,
  valueType,
  isOldVersion,
  paramChangeType,
  conditionChanges,
  previousNamespace,
}) => {
  const getContentChangeClass = () => {
    if (isOldVersion) {
      if (condition.changeType === CHANGE_TYPES.DELETE) return 'bg-red-50';
      if (condition.changeType === CHANGE_TYPES.UPDATE) return 'bg-yellow-50';
    } else {
      if (condition.changeType === CHANGE_TYPES.CREATE) return 'bg-green-50';
      if (condition.changeType === CHANGE_TYPES.UPDATE) return 'bg-yellow-50';
    }
    return '';
  };

  const rowClass =
    paramChangeType === CHANGE_TYPES.UPDATE
      ? 'bg-yellow-50'
      : paramChangeType === CHANGE_TYPES.CREATE
      ? 'bg-green-50'
      : getContentChangeClass();

  return (
    <tr className={`border-t border-gray-100 ${rowClass}`}>
      <td className="p-3">
        {condition.changeType === CHANGE_TYPES.CREATE && (
          <ChangeIcon changeType={CHANGE_TYPES.CREATE} />
        )}
        {condition.changeType === CHANGE_TYPES.UPDATE && (
          <ChangeIcon changeType={CHANGE_TYPES.UPDATE} />
        )}
        {/* {condition.isOrderChanged && !condition.changeType && ( */}
        {/*   <> */}
        {/*     <ChangeIcon changeType={CHANGE_TYPES.ORDER} /> */}
        {/*     <span className="text-xs text-blue-500"> */}
        {/*       {condition.originalIndex !== undefined && condition.originalIndex !== -1 */}
        {/*         ? `#${condition.originalIndex + 1} → #${condition.newIndex! + 1}` */}
        {/*         : ''} */}
        {/*     </span> */}
        {/*   </> */}
        {/* )} */}
      </td>
      <td className="p-3">
        <Popover
          open={condition.conditionId === '*' ? false : undefined}
          content={
            <ConditionExpressionLookup
              conditionId={condition.conditionId || ''}
              conditionChanges={conditionChanges}
              previousNamespace={previousNamespace}
              isOldVersion={isOldVersion}
            />
          }
        >
          <Tag
            color={getTagColor(condition)}
            className={condition.changeType === CHANGE_TYPES.DELETE ? 'line-through' : ''}
          >
            {condition.conditionName}
          </Tag>
        </Popover>
      </td>
      <td className="py-1 px-3">
        <ContentViewer content={condition.value} contentType={valueType} />
      </td>
    </tr>
  );
};

const ParametersTable: React.FC<{
  parameters: ParameterCompare[];
  expandedParameters: Record<string, boolean>;
  onToggleExpand: (key: string) => void;
  isOldVersion?: boolean;
  conditionChanges?: ConditionCompare[];
  previousNamespace?: {
    conditionSnapshots?: Array<{
      conditionId: string;
      name: string;
      expression?: any;
    }>;
  };
}> = ({
  parameters,
  expandedParameters,
  onToggleExpand,
  isOldVersion = false,
  conditionChanges,
  previousNamespace,
}) => (
  <div className="border rounded-md">
    <table className="w-full table-fixed">
      <thead>
        <tr className="bg-gray-50">
          <th
            className="p-3 text-left font-medium text-gray-600"
            style={{ width: TABLE_COLUMNS.NAME.width }}
          >
            {TABLE_COLUMNS.NAME.label}
          </th>
          <th
            className="p-3 text-left font-medium text-gray-600"
            style={{ width: TABLE_COLUMNS.CONDITION.width }}
          >
            {TABLE_COLUMNS.CONDITION.label}
          </th>
          <th
            className="p-3 text-left font-medium text-gray-600"
            style={{ width: TABLE_COLUMNS.VALUE.width }}
          >
            {TABLE_COLUMNS.VALUE.label}
          </th>
        </tr>
      </thead>
      <tbody>
        {parameters.map((param, index) => (
          <React.Fragment key={`${isOldVersion ? 'old' : 'new'}-${param.parameterKey}-${index}`}>
            <ParameterRow
              param={
                isOldVersion
                  ? param.changeType === CHANGE_TYPES.CREATE
                    ? null
                    : param
                  : param.changeType === CHANGE_TYPES.DELETE
                  ? null
                  : param
              }
              isExpanded={!!expandedParameters[param.parameterKey]}
              onToggle={() => onToggleExpand(param.parameterKey)}
              showChangeIcon={!isOldVersion || param.changeType === CHANGE_TYPES.DELETE}
            />
            {expandedParameters[param.parameterKey] &&
              (isOldVersion ? param.originalConditions : param.changedConditions)?.map(
                (condition, idx) => {
                  if (
                    (isOldVersion && param.changeType === CHANGE_TYPES.CREATE) ||
                    (!isOldVersion && param.changeType === CHANGE_TYPES.DELETE)
                  ) {
                    return <EmptyLine key={idx} />;
                  }

                  if (isOldVersion && condition.changeType === CHANGE_TYPES.CREATE) return null;
                  if (!isOldVersion && condition.changeType === CHANGE_TYPES.DELETE) return null;

                  return (
                    <ConditionRow
                      key={`${isOldVersion ? 'old' : 'new'}-${param.parameterKey}-${
                        condition.conditionName
                      }-${idx}`}
                      condition={condition}
                      valueType={param.valueType}
                      isOldVersion={isOldVersion}
                      paramChangeType={param.changeType}
                      conditionChanges={conditionChanges}
                      previousNamespace={previousNamespace}
                    />
                  );
                },
              )}
          </React.Fragment>
        ))}
      </tbody>
    </table>
  </div>
);

// 获取操作符显示文本
const getOperatorText = (operator: string, key: string): string => {
  if (['app_ver', 'os_ver'].includes(key)) {
    return OPERATORS[operator as keyof typeof OPERATORS] || operator;
  }
  if (['m_brand', 'm_model'].includes(key)) {
    return ['==', '!=', '~=', '!~'].includes(operator)
      ? OPERATORS[operator as keyof typeof OPERATORS]
      : operator;
  }
  if (key === 'did_hash') {
    return operator === '==' ? OPERATORS['=='] : operator;
  }
  return operator;
};

// 格式化条件表达式，提高可读性
const formatExpression = (expression: any): React.ReactNode => {
  if (!expression) return <span className="text-gray-500">无表达式</span>;

  // 如果是叶子节点（没有children的节点）
  if (!expression.children) {
    const { key } = expression;
    const { operator } = expression;
    const { value } = expression;

    if (!key || !operator || value === undefined) {
      return <span className="text-red-500">表达式格式错误</span>;
    }

    return (
      <span className="inline-flex items-center">
        <span className="font-medium text-blue-600">
          {CONDITION_KEYS[key as keyof typeof CONDITION_KEYS] || key}
        </span>
        <span className="mx-1 text-gray-500">{getOperatorText(operator, key)}</span>
        <span className="text-green-600">{value}</span>
      </span>
    );
  }

  // 如果是根节点或中间节点
  if (expression.children && expression.children.length === 1) {
    return formatExpression(expression.children[0]);
  }

  if (expression.children && expression.children.length > 1) {
    return (
      <div className="flex flex-col gap-1">
        {expression.children.map((child: any, index: number) => (
          <div key={index} className="flex items-center">
            {index > 0 && (
              <span className="mx-2 text-gray-500 font-medium">
                {getOperatorText(expression.operator, '')}
              </span>
            )}
            {formatExpression(child)}
          </div>
        ))}
      </div>
    );
  }

  return <span className="text-gray-500">表达式格式错误</span>;
};

const ConditionsTable: React.FC<{ conditions: ConditionCompare[] }> = ({ conditions }) => (
  <div>
    {conditions.length ? (
      <div className="border rounded-md">
        <table className="w-full table-fixed">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium text-gray-600" style={{ width: '30%' }}>
                {TABLE_COLUMNS.NAME.label}
              </th>
              <th className="p-3 text-left font-medium text-gray-600" style={{ width: '70%' }}>
                条件
              </th>
            </tr>
          </thead>
          <tbody>
            {conditions.map(condition => (
              <tr
                key={`condition-${condition.id}`}
                className="border-t border-gray-100 bg-green-50"
              >
                <td className="p-3">
                  <div className="flex items-center">
                    <PlusOutlined className="text-green-500 mr-2" />
                    <span>{condition.name}</span>
                    <div
                      className="w-3 h-3 rounded-sm ml-2"
                      style={{ backgroundColor: condition.color || DEFAULT_CONDITION_COLOR }}
                    />
                  </div>
                </td>
                <td className="p-3">
                  <div className="font-mono bg-gray-50 px-3 py-2 rounded">
                    {formatExpression(condition.expression)}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    ) : (
      <div className="text-center text-gray-500 py-8">没有条件变更</div>
    )}
  </div>
);

const ParameterDiffView: React.FC<ParameterDiffViewProps> = ({
  parameterChanges,
  conditionChanges = [],
  loading = false,
  showVersionInfo = false,
  previousVersion,
  currentVersion,
  previousNamespace,
}) => {
  const [activeTab, setActiveTab] = useState<string>(TAB_KEYS.PARAMETERS);
  const [expandedParameters, setExpandedParameters] = useState<Record<string, boolean>>({});

  const toggleExpand = (paramName: string) => {
    setExpandedParameters(prev => ({
      ...prev,
      [paramName]: !prev[paramName],
    }));
  };

  // 计算参数变化数量
  const parameterChangeCount = parameterChanges.filter(
    param =>
      param.changeType === CHANGE_TYPES.CREATE ||
      param.changeType === CHANGE_TYPES.UPDATE ||
      param.changeType === CHANGE_TYPES.DELETE ||
      param.hasOrderChanges,
  ).length;

  // 计算条件变化数量
  const conditionChangeCount = conditionChanges.filter(
    condition =>
      condition.changeType === CHANGE_TYPES.CREATE ||
      condition.changeType === CHANGE_TYPES.UPDATE ||
      condition.changeType === CHANGE_TYPES.DELETE,
  ).length;

  return (
    <div className="px-6">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span className={'flex items-center'}>
              参数
              <Badge
                count={parameterChangeCount}
                size={'small'}
                className={'ml-1 inline-flex items-center justify-center'}
              />
            </span>
          }
          key={TAB_KEYS.PARAMETERS}
        />
        <TabPane
          tab={
            <span className={'flex items-center'}>
              条件
              <Badge
                count={conditionChangeCount}
                size={'small'}
                className={'ml-1 inline-flex items-center justify-center'}
              />
            </span>
          }
          key={TAB_KEYS.CONDITIONS}
        />
      </Tabs>

      <div className="border-gray-200">
        {loading ? (
          <div className="p-10 flex justify-center">
            <Spin size="large" tip="加载中..." />
          </div>
        ) : activeTab === TAB_KEYS.PARAMETERS ? (
          <div>
            {showVersionInfo && (
              <div className="flex mb-1 gap-4">
                <div className="flex flex-1">
                  {previousVersion && <div className="flex-1">NS-{previousVersion}</div>}
                </div>
                <div className="flex-1">当前版本 {currentVersion}</div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <ParametersTable
                parameters={parameterChanges}
                expandedParameters={expandedParameters}
                onToggleExpand={toggleExpand}
                isOldVersion
                previousNamespace={previousNamespace}
              />
              <ParametersTable
                parameters={parameterChanges}
                expandedParameters={expandedParameters}
                onToggleExpand={toggleExpand}
                conditionChanges={conditionChanges}
                previousNamespace={previousNamespace}
              />
            </div>
          </div>
        ) : (
          <ConditionsTable conditions={conditionChanges} />
        )}
      </div>
    </div>
  );
};

export default ParameterDiffView;

'use client';

import type React from 'react';
import { Button, Input } from 'antd';

interface ParameterActionBarProps {
    searchText: string;
    onSearchTextChange: (value: string) => void;
    onSearch: () => void;
    onAddParameter: () => void;
    onPublishChanges: () => void;
    hasSelectedParameters: boolean;
}

const ParameterActionBar: React.FC<ParameterActionBarProps> = ({
                                                                   searchText,
                                                                   onSearchTextChange,
                                                                   onSearch,
                                                                   onAddParameter,
                                                                   onPublishChanges,
                                                                   hasSelectedParameters,
                                                               }) => {
    return (
      <div className="flex justify-between items-center pb-4">
        <Input.Search
          placeholder="输入参数名关键字搜索"
          className="max-w-md"
          value={searchText}
          onChange={(e) => onSearchTextChange(e.target.value)}
          onSearch={onSearch}
          enterButton
        />
        <div>
          <Button type="default" className="mr-2" onClick={onAddParameter}>
            添加参数
          </Button>
          <Button type="primary" onClick={onPublishChanges} disabled={!hasSelectedParameters}>
            发布变更
          </Button>
        </div>
      </div>
    );
};

export default ParameterActionBar;

'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, List, Popover, Typography } from 'antd';
import { BellOutlined } from '@ant-design/icons';
import DOMPurify from 'dompurify';

const { Title } = Typography;

interface Notification {
  id: number;
  title: string;
  content: string;
}

const mockNotifications: Notification[] = [
  {
    id: 1,
    title: '百分比灰度发布功能',
    content:
      "<strong>支持用户在一次发布过程中通过切换灰度百分比来进行配置的分批推送</strong><br>点击>>><a class='text-blue-500 hover:text-blue-700' href='https://aliyuque.antfin.com/wireless-orange/open/mpvkkl37b26v1qiz#xAZ5o' target='_blank'>文档</a>了解更多",
  },
];

export default function NotificationList() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [lastViewedId, setLastViewedId] = useState(0);

  useEffect(() => {
    // 获取通知（这里使用模拟数据）
    // fixme: 暂时关闭
    // setNotifications(mockNotifications);

    // 从 localStorage 读取最后查看的通知 ID
    const storedLastViewedId = localStorage.getItem('lastViewedNotificationId');
    setLastViewedId(storedLastViewedId ? parseInt(storedLastViewedId, 10) : 0);

    // 如果有新通知，自动打开通知列表
    // eslint-disable-next-line max-len
    if (
      mockNotifications.length > 0 &&
      mockNotifications[0].id > (storedLastViewedId ? parseInt(storedLastViewedId, 10) : 0)
    ) {
      // setIsOpen(true);
    }
  }, []);

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpen(newOpen);
    if (newOpen) {
      updateLastViewedId();
    }
  };

  const updateLastViewedId = () => {
    if (notifications.length > 0) {
      const newLastViewedId = notifications[0].id;
      setLastViewedId(newLastViewedId);
      localStorage.setItem('lastViewedNotificationId', newLastViewedId.toString());
    }
  };

  const hasNewNotifications = notifications.length > 0 && notifications[0].id > lastViewedId;

  const sanitizeHTML = (html: string) => {
    return {
      __html: DOMPurify.sanitize(html),
    };
  };

  const content = (
    <List
      itemLayout="vertical"
      dataSource={notifications}
      renderItem={item => (
        <List.Item>
          <List.Item.Meta title={<Title level={5}>{item.title}</Title>} />
          <div dangerouslySetInnerHTML={sanitizeHTML(item.content)} />
        </List.Item>
      )}
    />
  );

  return (
    <Popover
      content={content}
      title={<Title level={4}>平台公告</Title>}
      trigger="click"
      open={isOpen}
      onOpenChange={handleOpenChange}
      placement="bottomRight"
      overlayStyle={{
        width: '500px',
        maxHeight: '400px',
        overflow: 'auto',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      }}
    >
      <Badge dot={hasNewNotifications}>
        <Button icon={<BellOutlined />} shape="circle" />
      </Badge>
    </Popover>
  );
}

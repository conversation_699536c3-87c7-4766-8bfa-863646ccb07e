import React, { useEffect, useState } from 'react';
import { Card } from 'antd';
import { ChatHeader } from './ChatHeader';
import { ChatContainer } from './ChatContainer';
import { queryMessages } from '@/services/service';
import { v4 as uuidv4 } from 'uuid';

interface ChatWidgetProps {
  user: {
    avatar: string;
    userid: string;
  };
  setChatOpen: (open: boolean) => void;
}

const ideaLabAppCode = 'zbIadczoLDa';

declare const window: {
  xf?: {
    launch: () => void;
  };
};

const ChatWidget: React.FC<ChatWidgetProps> = ({ user, setChatOpen }) => {
  const [sessionId, setSessionId] = useState<string>('');
  const [chatHistory, setChatHistory] = useState<any[]>([]);

  useEffect(() => {
    let storedSessionId = localStorage.getItem('chatSessionId');
    if (!storedSessionId) {
      storedSessionId = uuidv4();
      localStorage.setItem('chatSessionId', storedSessionId);
    }
    setSessionId(storedSessionId);
    fetchChatHistory(storedSessionId);
  }, []);

  const fetchChatHistory = async (sid: string) => {
    if (!sid) return;

    try {
      const chatList = await queryMessages({
        sessionId: sid,
        appCode: ideaLabAppCode,
        pageSize: 100,
        pageNo: 1,
      });
      setChatHistory(chatList);
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
    }
  };

  const handleNewSession = () => {
    const newSessionId = uuidv4();
    localStorage.setItem('chatSessionId', newSessionId);
    setSessionId(newSessionId);
    setChatHistory([]);
  };

  const handleFeedback = () => {
    window.xf?.launch && window.xf.launch();
  };

  const handleRequest = async (messages: any[]) => {
    if (!messages.at(-1)?.content) {
      return;
    }

    const response: any = await fetch(
      `https://aistudio.alibaba-inc.com/api/aiapp/run/${ideaLabAppCode}/latest`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          accept: 'application/json',
          'X-AK': 'fc3b6bfad18a1f3b5ecfac8aa3c1248f',
        },
        body: JSON.stringify({
          stream: true,
          empId: user.userid,
          sessionId: sessionId,
          question: messages.at(-1)?.content,
        }),
      },
    );

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const encoder = new TextEncoder();
    const readableStream = new ReadableStream({
      async start(controller) {
        let accumulatedData = '';

        function push() {
          reader
            .read()
            .then(({ done, value }) => {
              if (done) {
                controller.close();
                return;
              }
              const chunk = decoder.decode(value, { stream: true });
              const message = chunk.replace('data: ', '').trim();
              if (message === '[DONE]') {
                controller.close();
                return;
              }

              try {
                const parsed = JSON.parse(message);
                const appendedData = parsed.data.content.slice(accumulatedData.length);
                accumulatedData = parsed.data.content;
                controller.enqueue(encoder.encode(appendedData));
              } catch (e) {
                // console.log('处理流数据失败', message, e);
              }
              push();
            })
            .catch(err => {
              // console.error('读取流中的数据时发生错误', err);
              controller.error(err);
            });
        }

        push();
      },
    });
    return new Response(readableStream);
  };

  return (
    <div className="fixed right-8 bottom-28 z-50">
      <Card style={{ width: 400 }} bodyStyle={{ padding: 0 }}>
        <ChatHeader
          onFeedback={handleFeedback}
          onNewSession={handleNewSession}
          onClose={() => setChatOpen(false)}
        />
        <ChatContainer
          userAvatar={user.avatar}
          chatHistory={chatHistory}
          sessionId={sessionId}
          onRequest={handleRequest}
        />
      </Card>
    </div>
  );
};

export default ChatWidget;

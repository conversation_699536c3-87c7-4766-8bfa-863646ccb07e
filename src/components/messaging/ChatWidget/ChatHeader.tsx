import React from 'react';
import { But<PERSON>, Space, Typography, Row, Col } from 'antd';
import { MessageOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface ChatHeaderProps {
  onFeedback: () => void;
  onNewSession: () => void;
  onClose: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ onFeedback, onNewSession, onClose }) => (
  <Row className="border-b p-4" align="middle" justify="space-between">
    <Col>
      <Title level={4} style={{ margin: 0 }}>智能助手</Title>
    </Col>
    <Col>
      <Space>
        {window.xf &&
          <Button
            icon={<MessageOutlined />}
            onClick={onFeedback}
            className="hidden sm:inline-flex"
          >
            我要反馈
          </Button>}
        <Button
          icon={<PlusOutlined />}
          onClick={onNewSession}
          className="hidden sm:inline-flex"
        >
          新建会话
        </Button>
        <Button
          icon={<CloseOutlined />}
          onClick={onClose}
          shape="circle"
        />
      </Space>
    </Col>
  </Row>
);


import React, { memo } from 'react';
import { ProChat } from '@ant-design/pro-chat';

interface ChatContainerProps {
  userAvatar: string;
  chatHistory: any[];
  sessionId: string;
  onRequest: (messages: any[]) => Promise<Response | undefined>;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  userAvatar,
  chatHistory,
  sessionId,
  onRequest,
}) => (
  <div className="h-[600px]">
    <ProChat
      assistantMeta={{
        avatar:
          'https://img.alicdn.com/imgextra/i2/O1CN01AuoiNO1tYCwRJpBgH_!!6000000005913-2-tps-397-397.png',
      }}
      userMeta={{
        avatar: userAvatar,
      }}
      style={{ height: '100%' }}
      helloMessage="你好！我是 Orange 智能助手，请问有什么可以帮助你？"
      chats={chatHistory || []}
      markdownProps={{
        components: {
          a: memo(props => (
            <a href={props.href} target="_blank" rel="noopener noreferrer">
              {props.children}
            </a>
          )),
        },
      }}
      request={onRequest}
    />
  </div>
);

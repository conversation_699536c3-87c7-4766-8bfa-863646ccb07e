import React, { useEffect, useState } from 'react';
import { Tooltip, Typography } from 'antd';
import ParameterValueType = API.ParameterValueType;

const { Text } = Typography;

interface ContentViewerProps {
  content: string;
  contentType: ParameterValueType;
  maxWidth?: string;
}

const ContentViewer: React.FC<ContentViewerProps> = ({ content, contentType, maxWidth = 'xs' }) => {
  const [formattedContent, setFormattedContent] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  const showTooltip = contentType !== 'BOOLEAN' && content.length > 20;

  useEffect(() => {
    try {
      switch (contentType) {
        case 'JSON':
          const parsed = typeof content === 'string' ? JSON.parse(content) : content;
          setFormattedContent(JSON.stringify(parsed, null, 2));
          break;
        case 'BOOLEAN':
          const boolValue = content.toLowerCase();
          if (boolValue !== 'true' && boolValue !== 'false') {
            throw new Error('Invalid boolean value');
          }
          setFormattedContent(boolValue);
          break;
        default:
          setFormattedContent(content);
      }
      setIsValid(true);
    } catch (error) {
      console.error(`Invalid ${contentType} content:`, error);
      setFormattedContent(content);
      setIsValid(false);
    }
  }, [content, contentType]);
  const tooltipTitle = (
    <div className="w-full">
      <div className="max-h-80 overflow-auto bg-gray-50 p-3 rounded-md">
        <pre className="text-xs font-mono text-gray-800 whitespace-pre-wrap">
          {formattedContent}
        </pre>
      </div>
    </div>
  );
  const maxWidthClass = `max-w-${maxWidth}`;
  return (
    <div>
      {showTooltip ? (
        <Tooltip
          title={tooltipTitle}
          color="#fff"
          overlayInnerStyle={{ maxWidth: '500px' }}
          placement="top"
        >
          <div
            className={`${maxWidthClass} bg-gray-50 hover:bg-gray-100 transition-colors duration-200 rounded-md px-2 py-1 cursor-pointer border border-gray-200 items-center flex`}
          >
            <Text
              ellipsis={{ tooltip: false }}
              className={`font-mono text-xs ${isValid ? 'text-gray-700' : 'text-red-500'}`}
            >
              {isValid ? content : `无效的 ${contentType} 数据`}
            </Text>
          </div>
        </Tooltip>
      ) : (
        <div
          className={`${maxWidthClass} bg-gray-50 hover:bg-gray-100 transition-colors duration-200 rounded-md px-2 py-1 border border-gray-200 items-center flex`}
        >
          <Text className={`font-mono text-xs ${isValid ? 'text-gray-700' : 'text-red-500'}`}>
            {isValid ? content : `无效的 ${contentType} 数据`}
          </Text>
        </div>
      )}
    </div>
  );
};

export default ContentViewer;

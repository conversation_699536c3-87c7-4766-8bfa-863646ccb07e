import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Select, Spin, message } from 'antd';
import type { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';

interface RemoteSelectorProps<T> extends Omit<SelectProps<string | string[]>, 'options' | 'onChange'> {
  multiple?: boolean;
  getData: (keyword: string) => Promise<{ content: T[] }>;
  getValue: (item: T) => string;
  getLabel: (item: T) => string;
  value?: string | string[];
  onChange?: (value: string | string[], options: T[]) => void;
  defaultOptions?: T[];
  debounceTime?: number;
}

function RemoteSelector<T>({
  multiple = false,
  getData,
  getValue,
  getLabel,
  value,
  onChange,
  defaultOptions = [],
  debounceTime = 300,
  ...restProps
}: RemoteSelectorProps<T>) {
  const [options, setOptions] = useState<T[]>([]);
  const [fetching, setFetching] = useState(false);
  const [cachedLabels, setCachedLabels] = useState<Record<string, string>>({});

  const updateCachedLabels = useCallback((items: T[]) => {
    setCachedLabels(prev => {
      const newLabels: Record<string, string> = { ...prev };
      items.forEach((item) => {
        const itemValue = getValue(item);
        newLabels[itemValue] = getLabel(item);
      });
      return newLabels;
    });
  }, [getValue, getLabel]);

  useEffect(() => {
    updateCachedLabels(defaultOptions);
    setOptions(defaultOptions);
  }, [defaultOptions, updateCachedLabels]);

  const fetchData = useCallback(async (keyword: string) => {
    try {
      setFetching(true);
      const res = await getData(keyword);
      const newOptions = res.content || [];
      setOptions(newOptions);
      updateCachedLabels(newOptions);
    } catch (error) {
      message.error('加载数据失败');
      setOptions([]);
    } finally {
      setFetching(false);
    }
  }, [getData, updateCachedLabels]);

  const handleSearch = useMemo(
    () => debounce((keyword: string) => {
      if (!keyword) {
        setOptions(defaultOptions);
        return;
      }
      fetchData(keyword);
    }, debounceTime),
    [defaultOptions, fetchData, debounceTime],
  );

  const handleChange = useCallback((newValue: string | string[]) => {
    if (onChange) {
      const selectedOptions = Array.isArray(newValue)
        ? options.filter(item => newValue.includes(getValue(item)))
        : options.filter(item => getValue(item) === newValue);
      onChange(newValue, selectedOptions);
    }
  }, [onChange, options, getValue]);

  const memoizedOptions = useMemo(() =>
    options.map((item) => ({
      key: getValue(item),
      value: getValue(item),
      label: getLabel(item),
    })),
    [options, getValue, getLabel],
  );

  const renderSelectedOptions = useMemo(() => {
    if (!value) return null;

    if (Array.isArray(value)) {
      return value.map((v) => (
        <Select.Option key={v} value={v}>
          {cachedLabels[v] || v}
        </Select.Option>
      ));
    }

    if (!options.find(item => getValue(item) === value)) {
      return (
        <Select.Option key={value} value={value}>
          {cachedLabels[value] || value}
        </Select.Option>
      );
    }

    return null;
  }, [value, options, getValue, cachedLabels]);

  return (
    <Select
      mode={multiple ? 'multiple' : undefined}
      value={value}
      onChange={handleChange}
      onSearch={handleSearch}
      filterOption={false}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      style={{ minWidth: '200px' }}
      options={memoizedOptions}
      showSearch
      allowClear
      {...restProps}
    >
      {renderSelectedOptions}
    </Select>
  );
}

export default RemoteSelector;

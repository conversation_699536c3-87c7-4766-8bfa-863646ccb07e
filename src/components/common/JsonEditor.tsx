'use client';

import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal, Button, Space } from 'antd';
import { FormatPainterOutlined } from '@ant-design/icons';

import Editor, { loader } from '@monaco-editor/react';

loader.config({
  paths: {
    vs: 'https://g.alicdn.com/code/lib/monaco-editor/0.43.0/min/vs',
  },
});

interface JsonEditorProps {
  visible: boolean;
  onClose: () => void;
  value: string;
  onChange: (value: string) => void;
}

const JsonEditor: React.FC<JsonEditorProps> = ({ visible, onClose, value, onChange }) => {
  const [editorValue, setEditorValue] = useState(value || '{}');
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const validateJson = (jsonString: string) => {
    try {
      if (jsonString.trim() === '') {
        setIsValid(false);
        setErrorMessage('JSON 不能为空');
        return false;
      }

      JSON.parse(jsonString);
      setIsValid(true);
      setErrorMessage('');
      return true;
    } catch (error) {
      setIsValid(false);
      setErrorMessage(`JSON 无效： ${(error as Error).message}`);
      return false;
    }
  };

  useEffect(() => {
    const initialValue = value || '{}';
    // When opening the editor, format the JSON for better readability
    if (visible && initialValue) {
      try {
        const parsed = JSON.parse(initialValue);
        const formatted = JSON.stringify(parsed, null, 2);
        setEditorValue(formatted);
        validateJson(formatted);
      } catch (error) {
        // If parsing fails, use the original value
        setEditorValue(initialValue);
        validateJson(initialValue);
      }
    } else {
      setEditorValue(initialValue);
      validateJson(initialValue);
    }
  }, [value, visible]);

  const handleFormat = () => {
    try {
      const parsed = JSON.parse(editorValue);
      const formatted = JSON.stringify(parsed, null, 2);
      setEditorValue(formatted);
      // Save compressed format to parent
      const compressed = JSON.stringify(parsed);
      onChange(compressed);
      setIsValid(true);
      setErrorMessage('');
    } catch (error) {
      // If formatting fails, do nothing - the error is already shown
      console.error('Format error:', error);
    }
  };

  const handleEditorChange = (_value: string | undefined) => {
    if (!_value) return;

    setEditorValue(_value);
    validateJson(_value);

    // Only update parent if JSON is valid, and save in compressed format
    if (validateJson(_value)) {
      try {
        const parsed = JSON.parse(_value);
        const compressed = JSON.stringify(parsed);
        onChange(compressed);
      } catch (error) {
        // If compression fails, use original value
        onChange(_value);
      }
    }
  };

  return (
    <Modal
      title={
        <Space>
          <span>JSON 编辑器</span>
          <Button
            type="text"
            size="small"
            icon={<FormatPainterOutlined />}
            onClick={handleFormat}
            disabled={!isValid}
            title="格式化JSON"
          >
            格式化
          </Button>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      bodyStyle={{ padding: 0 }}
    >
      {!isValid && (
        <Alert
          description={errorMessage}
          type="warning"
          showIcon
          style={{
            margin: 0,
            borderRadius: 0,
          }}
        />
      )}
      <div style={{ height: '500px' }}>
        <Editor
          height="500px"
          language="json"
          value={editorValue}
          onChange={handleEditorChange}
          options={{
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            automaticLayout: true,
          }}
        />
      </div>
    </Modal>
  );
};

export default JsonEditor;

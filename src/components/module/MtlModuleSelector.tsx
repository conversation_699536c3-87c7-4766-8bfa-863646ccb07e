import React, { useCallback } from 'react';
import { RemoteSelector } from '@/components/common';
import { fuzzySearchModule } from '@/services/service';
import { MtlModule } from '@/types/namespace';

interface MtlModuleSelectorProps {
  appKey: string;
  multiple?: boolean;
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  defaultOptions?: MtlModule[];
  required?: boolean;
  disabled?: boolean;
}

const MtlModuleSelector: React.FC<MtlModuleSelectorProps> = (props: MtlModuleSelectorProps) => {
  const { appKey } = props;

  const getData = useCallback(
    async (keyword: string) => {
      if (!appKey) return { content: [] };
      try {
        const content = await fuzzySearchModule(appKey, keyword);
        return { content };
      } catch (e) {
        console.error('Error fetching modules:', e);
        return { content: [] };
      }
    },
    [appKey],
  );

  return (
    <RemoteSelector<MtlModule>
      {...props}
      key={appKey}
      disabled={!appKey || props.disabled}
      getData={getData}
      getValue={(module: MtlModule) => module?.moduleId}
      getLabel={(module: MtlModule) => module?.name}
    />
  );
};

export default MtlModuleSelector;

import React from 'react';
import { Space, Tooltip } from 'antd';
import { MtlModule } from '@/types/namespace';

const MtlModuleList = (props: {
  moduleIds: string[];
  moduleInfoMap: Record<string, MtlModule>;
}) => {
  let {
    moduleIds,
    moduleInfoMap,
  } = props;

  return (
    moduleIds?.length ? <Space size={[0, 8]} wrap>
      {moduleIds.map(moduleId => {
        const module = moduleInfoMap?.[moduleId];
        return (
          <Tooltip key={moduleId} title={module ? module.name : moduleId}>
            <a
              target="_blank"
              rel="noreferrer"
              href={module?.mtlAddress}
              className="mr-2 text-blue-500 hover:text-blue-700"
            >
              {module ? module.name : moduleId}
            </a>
          </Tooltip>
        );
      })}
    </Space> : null
  );
};

export default MtlModuleList;

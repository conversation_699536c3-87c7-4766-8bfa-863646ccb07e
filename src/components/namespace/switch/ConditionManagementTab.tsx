'use client';

import React, { useEffect, useState } from 'react';
import { Button, Empty, Input, List, Popover, Spin, Tag, Tooltip } from 'antd';
import {
  CloseOutlined,
  ControlOutlined,
  DeleteOutlined,
  EditOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { User } from '@/components/user';
import { getFormatDate } from '@/utils/common';
import { COLORS, CONDITION_KEYS, DEFAULT_CONDITION_COLOR, OPERATORS } from '@/constants/condition';
import { useNamespaceConditions } from '@/hooks/useNamespaceConditions';
import ConditionDetailDTO = API.ConditionDetailDTO;

interface ConditionManagementTabProps {
  namespaceId: string;
}

// 获取操作符显示文本
const getOperatorText = (operator: string, key: string): string => {
  if (['app_ver', 'os_ver'].includes(key)) {
    return OPERATORS[operator as keyof typeof OPERATORS] || operator;
  }
  if (['m_brand', 'm_model'].includes(key)) {
    return ['==', '!=', '~=', '!~'].includes(operator)
      ? OPERATORS[operator as keyof typeof OPERATORS]
      : operator;
  }
  if (key === 'did_hash') {
    return operator === '==' ? OPERATORS['=='] : operator;
  }
  return operator;
};

const ConditionItem: React.FC<{
  condition: ConditionDetailDTO;
  onEdit?: (condition: ConditionDetailDTO) => void;
  onDelete?: (condition: ConditionDetailDTO) => void;
  onUpdate: (condition: API.ConditionDirectUpdateDTO) => Promise<void>;
}> = ({ condition, onEdit, onDelete, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(condition.name);
  const [selectedColor, setSelectedColor] = useState(condition.color || DEFAULT_CONDITION_COLOR);
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);

  const handleSave = async () => {
    await onUpdate({
      conditionId: condition.conditionId!,
      name: newName,
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setNewName(condition.name);
    setIsEditing(false);
  };

  const handleColorChange = async (color: string) => {
    setSelectedColor(color);
    await onUpdate({
      conditionId: condition.conditionId!,
      color,
    });
    setIsColorPickerOpen(false);
  };

  const colorPickerContent = (
    <div className="p-2">
      <div className="grid grid-cols-4 gap-2">
        {Object.entries(COLORS).map(([key, { value }]) => (
          <div
            key={key}
            className="w-6 h-6 rounded-sm cursor-pointer hover:ring-2 hover:ring-blue-500"
            style={{ backgroundColor: value || DEFAULT_CONDITION_COLOR }}
            onClick={() => handleColorChange(value)}
          />
        ))}
      </div>
    </div>
  );

  const hasPublishingParameter = (conditionItem: ConditionDetailDTO): boolean => {
    return conditionItem.parameterConditions?.some(param => param.status === 'INIT') ?? false;
  };

  // 格式化条件表达式，提高可读性
  const formatExpression = (expression: any): React.ReactNode => {
    if (!expression) return null;

    // 如果是叶子节点（没有children的节点）
    if (!expression.children) {
      return (
        <span className="inline-flex items-center">
          <span className="font-medium text-blue-600">
            {CONDITION_KEYS[expression.key as keyof typeof CONDITION_KEYS] || expression.key}
          </span>
          <span className="mx-1 text-gray-500">
            {getOperatorText(expression.operator, expression.key)}
          </span>
          <span className="text-green-600">{expression.value}</span>
        </span>
      );
    }

    // 如果是根节点或中间节点
    if (expression.children.length === 1) {
      return formatExpression(expression.children[0]);
    }

    return (
      <div className="flex flex-col gap-1">
        {expression.children.map((child: any, index: number) => (
          <div key={index} className="flex items-center">
            {index > 0 && (
              <span className="mx-2 text-gray-500 font-medium">
                {getOperatorText(expression.operator, '')}
              </span>
            )}
            {formatExpression(child)}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="w-full flex flex-col">
      <div className="flex justify-between items-center mb-2">
        <div className="flex-1">
          <div className="flex items-center">
            <Popover
              content={colorPickerContent}
              trigger="click"
              open={isColorPickerOpen}
              onOpenChange={setIsColorPickerOpen}
            >
              <div
                className="w-3 h-3 rounded-sm mr-2 cursor-pointer hover:ring-2 hover:ring-blue-500"
                style={{ backgroundColor: selectedColor }}
              />
            </Popover>
            {isEditing ? (
              <div className="flex items-center">
                <Input
                  value={newName}
                  onChange={e => setNewName(e.target.value)}
                  className="w-64"
                  autoFocus
                  onPressEnter={handleSave}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={handleCancel}
                  className="ml-1 text-gray-500"
                />
              </div>
            ) : (
              <div className="group">
                <span className="mr-2 text-lg font-medium">{condition.name}</span>
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => setIsEditing(true)}
                  className="ml-2 text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity"
                />
              </div>
            )}
            {hasPublishingParameter(condition) && (
              <Tag color="orange" className="ml-2">
                发布中
              </Tag>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            disabled
            type="text"
            icon={<EditOutlined />}
            onClick={() => onEdit?.(condition)}
          />
          <Button
            type="text"
            disabled
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDelete?.(condition)}
          />
        </div>
      </div>

      <div className="flex items-center text-xs text-gray-500 mb-2">
        <User empIds={[condition.creator!]} showAvatar size="xsmall" />
        <span className="ml-2 mr-4">创建于 {getFormatDate(condition.gmtCreate!)}</span>
        <User empIds={[condition.modifier!]} showAvatar size="xsmall" />
        <span className="ml-2">更新于 {getFormatDate(condition.gmtModified!)}</span>
      </div>

      {condition.parameterConditions && condition.parameterConditions.length > 0 && (
        <div className="flex items-center text-xs text-gray-500 mb-2">
          <Tooltip title="使用该条件的参数">
            <ControlOutlined className="flex-shrink-0" />
          </Tooltip>
          <div className="flex flex-wrap gap-1 ml-2">
            {condition.parameterConditions.map((param, idx) => (
              <Tooltip key={idx} title={param.value}>
                <Tag
                  className="parameter text-gray-500 text-xs border-none py-1 rounded"
                  color={param.status === 'INIT' ? 'processing' : 'green'}
                  style={{ borderRadius: '4px' }}
                >
                  {param.parameterKey}
                </Tag>
              </Tooltip>
            ))}
          </div>
        </div>
      )}

      <div className="flex items-center text-xs text-gray-500">
        <FilterOutlined className="flex-shrink-0" />
        <div className="ml-2 font-mono bg-gray-50 px-3 py-2 rounded flex-1">
          {formatExpression(condition.expression)}
        </div>
      </div>
    </div>
  );
};

const ConditionManagementTab: React.FC<ConditionManagementTabProps> = ({ namespaceId }) => {
  const {
    conditions,
    loading,
    searchText,
    pagination,
    setSearchText,
    fetchConditions,
    handleSearch,
    handlePageChange,
    updateCondition,
  } = useNamespaceConditions({ namespaceId });

  // 初始化数据
  useEffect(() => {
    fetchConditions();
  }, [fetchConditions]);

  const handleSearchInput = (value: string) => {
    setSearchText(value);
    handleSearch();
  };

  const handleEdit = (condition: ConditionDetailDTO) => {
    // TODO: 实现编辑功能
    console.log('Edit condition:', condition);
  };

  const handleDelete = (condition: ConditionDetailDTO) => {
    // TODO: 实现删除功能
    console.log('Delete condition:', condition);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Input.Search
          placeholder="输入条件名关键字搜索"
          className="max-w-md flex-1 flex"
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          onSearch={handleSearchInput}
          enterButton
        />
        <div className="flex-0 flex">
          {/* fixme：后面开放 */}
          {/* <Button className="py-2 mr-2" disabled> */}
          {/*   添加条件 */}
          {/* </Button> */}
          {/* <Button type="primary" className="py-2" disabled> */}
          {/*   发布变更 */}
          {/* </Button> */}
        </div>
      </div>

      <div>
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spin />
          </div>
        ) : conditions.length === 0 ? (
          <Empty />
        ) : (
          <List
            bordered
            dataSource={conditions}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              align: 'center',
              onChange: handlePageChange,
              showSizeChanger: true,
              showTotal: total => `共 ${total} 条`,
            }}
            renderItem={condition => (
              <List.Item>
                <ConditionItem
                  condition={condition}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onUpdate={updateCondition}
                />
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default ConditionManagementTab;

'use client';

import React, { useEffect, useState } from 'react';
import { Alert, Checkbox, List, Spin } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import {
  type ConditionData,
  ParameterActionBar,
  ParameterEditDrawer,
  ParameterListItem,
  PublishChangesModal,
} from '@/components/parameter';
import { useNamespaceParameters } from '@/hooks/useNamespaceParameters';
import { useNamespaceConditions } from '@/hooks/useNamespaceConditions';
import { useNamespaceReleaseOrders } from '@/hooks/useNamespaceReleaseOrders';
import type { Parameter, ParameterCondition } from '@/types/parameter';
import { ConditionEditor } from '@/components';

interface ParameterManagementTabProps {
  namespaceId: string;
}

const ParameterManagementTab: React.FC<ParameterManagementTabProps> = ({ namespaceId }) => {
  // UI状态
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [editingParameter, setEditingParameter] = useState<Parameter | null>(null);
  const [conditionModalData, setConditionModalData] = useState<ConditionData | undefined>(
    undefined,
  );

  // 使用拆分后的hooks
  const parametersHook = useNamespaceParameters({ namespaceId });
  const conditionsHook = useNamespaceConditions({ namespaceId });
  const releaseOrdersHook = useNamespaceReleaseOrders({ namespaceId });

  const {
    parameterListData,
    originalParameters,
    selectedParameterKeys,
    expandedRows,
    loading,
    searchText,
    pagination,
    strongAlertMessage,
    setSearchText,
    setStrongAlertMessage,
    fetchParameters,
    updateParameter,
    handleSearch,
    handlePageChange,
    toggleExpand,
    toggleExpandAll,
    handleSaveParameter,
    handleDeleteParameter,
    handleSelectParameter,
    handleSelectAll,
  } = parametersHook;

  const { conditions, newConditions, fetchAllConditions, handleAddNewCondition } = conditionsHook;

  const { handleCreateReleaseOrder } = releaseOrdersHook;

  // 初始化数据
  useEffect(() => {
    fetchParameters(pagination.current, pagination.size, searchText);
    fetchAllConditions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [namespaceId]);

  // 事件处理
  const handleAddParameter = () => {
    setEditingParameter(null);
    setDrawerVisible(true);
  };

  const handleEditParameter = (parameter: Parameter) => {
    setEditingParameter(JSON.parse(JSON.stringify(parameter)));
    setDrawerVisible(true);
  };

  const handlePublishChanges = () => {
    if (selectedParameterKeys.length === 0) {
      return;
    }
    setPublishModalVisible(true);
  };

  const handleParameterSelect = (key: string, checked: boolean) => {
    handleSelectParameter(key, checked);
  };

  const handleParameterDelete = (key: string) => {
    handleDeleteParameter(key);
  };

  const handleConfirmPublish = async (description: string) => {
    const selectedParameters = parameterListData.filter(p =>
      selectedParameterKeys.includes(p.parameterKey!),
    );
    await handleCreateReleaseOrder(description, selectedParameters, newConditions);
    setPublishModalVisible(false);
  };

  // 列表头部
  const header = (
    <div className="flex items-center w-full">
      <div>
        <Checkbox
          onChange={e => handleSelectAll(e.target.checked)}
          checked={
            selectedParameterKeys.length > 0 &&
            selectedParameterKeys.length === parameterListData.filter(p => !!p.changeType).length
          }
        />
      </div>
      <div className="flex-1 flex items-center mx-1">
        {toggleExpandAll && (
          <RightOutlined
            className={`text-gray-500 p-1 transform transition-transform ${expandedRows.length === parameterListData.length ? 'rotate-90' : ''
              }`}
            onClick={toggleExpandAll}
          />
        )}
        <span className="font-medium ml-1">参数名</span>
      </div>
      <div className="w-2/3 flex justify-end">
        <div className="w-1/3 text-right">
          <span className="font-medium mr-6">条件</span>
        </div>
        <div className="w-1/2 text-center">
          <span className="font-medium">值</span>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {strongAlertMessage && (
        <Alert
          className="mb-3"
          message={strongAlertMessage}
          type="warning"
          showIcon
          closable
          onClose={() => setStrongAlertMessage(null)}
        />
      )}
      <ParameterActionBar
        searchText={searchText}
        onSearchTextChange={setSearchText}
        onSearch={handleSearch}
        onAddParameter={handleAddParameter}
        onPublishChanges={handlePublishChanges}
        hasSelectedParameters={selectedParameterKeys.length > 0}
      />

      <List
        header={header}
        loading={{
          spinning: loading,
          indicator: <Spin size="large" tip="加载中..." />,
        }}
        bordered
        dataSource={parameterListData}
        renderItem={item => (
          <ParameterListItem
            key={item.parameterKey}
            item={item}
            isExpanded={expandedRows.includes(item.parameterKey!)}
            isSelected={selectedParameterKeys.includes(item.parameterKey!)}
            onToggleExpand={toggleExpand}
            onUpdate={updateParameter}
            onEdit={handleEditParameter}
            onDelete={handleParameterDelete}
            onSelect={handleParameterSelect}
            onConditionClick={(parameterCondition: ParameterCondition) => {
              const condition = conditions.find(c => c.name === parameterCondition.conditionName);
              if (condition) {
                setConditionModalData({
                  id: parameterCondition.conditionId!,
                  name: parameterCondition.conditionName!,
                  color: parameterCondition.conditionColor || '',
                  expressionList: (condition.expression?.children || []) as any,
                });
              }
            }}
          />
        )}
        pagination={{
          position: 'bottom',
          align: 'center',
          total: pagination.total,
          current: pagination.current,
          pageSize: pagination.size,
          onChange: handlePageChange,
          showSizeChanger: true,
          showTotal: total => `共有 ${total} 个`,
        }}
      />

      <ParameterEditDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        editingParameter={editingParameter}
        onSave={parameter => handleSaveParameter(parameter, editingParameter ? 'edit' : 'create')}
        availableConditions={conditions}
        mode={editingParameter ? 'edit' : 'create'}
        handleAddNewCondition={handleAddNewCondition}
        namespaceId={namespaceId}
      />

      <PublishChangesModal
        visible={publishModalVisible}
        onClose={() => setPublishModalVisible(false)}
        onConfirm={handleConfirmPublish}
        changedParameters={parameterListData.filter(p =>
          selectedParameterKeys.includes(p.parameterKey!),
        )}
        originalParameters={originalParameters}
        newConditions={newConditions}
      />

      <ConditionEditor
        visible={!!conditionModalData}
        onClose={() => setConditionModalData(undefined)}
        onSave={() => { }}
        mode={'view'}
        initialData={conditionModalData}
        namespaceId={namespaceId}
      />
    </>
  );
};

export default ParameterManagementTab;

'use client';

import React, { useEffect, useState } from 'react';
import { Button, Modal, Space } from 'antd';
import { EyeOutlined, FileOutlined, NodeIndexOutlined } from '@ant-design/icons';
import { DataEntryList } from '@ali/mc-uikit';
import * as NamespaceController from '@/services/orange-be/NamespaceController';
import { User } from '@/components/user';
import { getFormatDate } from '@/utils/common';
import { Link } from 'ice';
import { getReleaseOrderDetailUrl } from '@/utils/link';
import Editor, { loader } from '@monaco-editor/react';
import * as ToolController from '@/services/orange-be/ToolController';

loader.config({
  paths: {
    vs: 'https://g.alicdn.com/code/lib/monaco-editor/0.43.0/min/vs',
  },
});

interface VersionHistoryTabProps {
  namespaceId: string;
}

const VersionHistoryTab: React.FC<VersionHistoryTabProps> = ({ namespaceId }) => {
  const [loading, setLoading] = useState(false);
  const [versionHistories, setVersionHistories] = useState<API.NamespaceVersionDTO[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [jsonContent, setJsonContent] = useState<string>('');
  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [namespace, setNamespace] = useState<API.NamespaceDTO | null>(null);

  const fetchNamespace = async () => {
    const response = await NamespaceController.getByNamespaceId({ namespaceId });
    setNamespace(response.data);
  };

  const fetchVersionHistories = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const response = await NamespaceController.histories({
        namespaceId,
        page,
        size: pageSize,
      });
      setVersionHistories(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.total || 0,
      }));
    } catch (error) {
      console.error('Failed to fetch version histories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewIndex = async (resourceId: string) => {
    try {
      const response = await ToolController.getResourceContent(
        {
          resourceId,
        },
        { ignoreError: true },
      );
      if (response.data) {
        const index = response.data;
        const config = index.namespaces.find(i => i.name === namespace!.name);
        const configResourceId = config?.release?.resourceId;
        if (configResourceId) {
          const configResponse = await ToolController.getResourceContent(
            {
              resourceId: configResourceId,
            },
            { ignoreError: true },
          );
          if (configResponse.data) {
            setJsonContent(JSON.stringify(configResponse.data, null, 2));
            setJsonModalVisible(true);
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch resource content:', error);
    }
  };

  useEffect(() => {
    fetchVersionHistories(pagination.current, pagination.pageSize);
    fetchNamespace();
  }, [namespaceId, pagination.current, pagination.pageSize]);

  const handlePageChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize,
    }));
  };

  return (
    <>
      <DataEntryList
        loading={loading}
        dataSource={versionHistories}
        pagination={{
          ...pagination,
          onChange: handlePageChange,
          showSizeChanger: true,
        }}
        renderItem={(item: API.NamespaceVersionDTO) => (
          <div className="flex justify-between items-center p-4 border-b border-gray-200 last:border-b-0">
            <div className="flex-1">
              <div className="text-md">
                <span className="mr-4 font-medium">NS-{item.namespaceChangeVersion}</span>
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-2">
                <FileOutlined className="mr-2" />
                <Link
                  to={getReleaseOrderDetailUrl(item.releaseVersion!)}
                  className="mr-4 underline"
                >
                  发布单（RO-{item.releaseVersion}）详情
                </Link>
                <User empIds={[item.creator!]} showAvatar size={'xsmall'} />
                <span className="ml-2 mr-4">发布于 {getFormatDate(item.gmtCreate!)}</span>
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-2">
                <NodeIndexOutlined className="mr-2" />
                <span>
                  {item.indexGmtCreate
                    ? `索引发布于 ${getFormatDate(item.indexGmtCreate!)}`
                    : '索引暂未发布'}
                </span>
              </div>
            </div>
            <Space>
              <Button
                type="link"
                disabled={!item.indexGmtCreate}
                className={'text-sm'}
                icon={<EyeOutlined />}
                onClick={() => handleViewIndex(item.indexResourceId!)}
              >
                查看版本内容
              </Button>
              {/* 迭代二支持 */}
              {/* <Button */}
              {/*   type="link" */}
              {/*   className={'text-sm'} */}
              {/*   disabled */}
              {/*   icon={<RollbackOutlined />} */}
              {/* >回滚到该版本</Button> */}
            </Space>
          </div>
        )}
      />
      <Modal
        title="配置内容"
        open={jsonModalVisible}
        onCancel={() => setJsonModalVisible(false)}
        footer={null}
        width={800}
        bodyStyle={{ padding: 0 }}
      >
        <div style={{ height: '600px' }}>
          <Editor
            height="600px"
            language="json"
            value={jsonContent}
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 14,
              automaticLayout: true,
              formatOnPaste: true,
              formatOnType: true,
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default VersionHistoryTab;

'use client';

import type React from 'react';
import { useEffect } from 'react';
import { Button, Form, Input, Modal } from 'antd';

import { SwitchNamespace } from '@/types/namespace';
import { UserSelect } from '@/components/user';
import { MtlModuleSelector } from '@/components';

const { TextArea } = Input;

interface SwitchNamespaceEditorProps {
  visible: boolean;
  title: string;
  namespace?: SwitchNamespace | null;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  appKey: string;
}

const SwitchNamespaceEditor: React.FC<SwitchNamespaceEditorProps> = ({
  visible,
  title,
  namespace,
  onCancel,
  onSubmit,
  appKey,
}) => {
  const [form] = Form.useForm();
  const isEdit = !!namespace;

  useEffect(() => {
    if (visible) {
      if (isEdit && namespace) {
        form.setFieldsValue({
          name: namespace.name,
          bizType: namespace.bizType,
          bizId: namespace.bizId,
          owners: namespace.owners,
          testers: namespace.testers,
          description: namespace.description,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, namespace, form, isEdit]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then(values => {
        onSubmit({ bizType: 'MODULE', ...values });
      })
      .catch(err => {
        console.log(err);
      });
  };

  return (
    <Modal
      centered
      maskClosable={false}
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel} className="mr-2">
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          help="命名空间名必填，且必须是合法的变量名（字母、数字、下划线、$、中划线，不能以数字开头）"
          label="命名空间名"
          rules={[
            {
              required: true,
              message: '请输入命名空间名',
            },
            {
              pattern: /^[a-zA-Z_$-][a-zA-Z0-9_$-]*$/,
              message: '命名空间名必须是合法的变量名',
            },
          ]}
        >
          <Input disabled={isEdit} />
        </Form.Item>
        <Form.Item
          name="bizId"
          label="关联模块"
          rules={[
            {
              required: true,
              message: '请选择关联模块',
            },
          ]}
        >
          <MtlModuleSelector
            appKey={appKey}
            disabled={isEdit}
            multiple={false}
            defaultOptions={
              isEdit && namespace?.bizId
                ? [
                    {
                      moduleId: namespace.bizId!,
                      name: namespace.bizName!,
                    },
                  ]
                : []
            }
          />
        </Form.Item>

        <Form.Item
          name="owners"
          label="管理员"
          help="至少填写 2 名管理员"
          rules={[
            {
              required: true,
              message: '至少填写 2 名管理员',
              type: 'array',
              min: 2,
            },
          ]}
        >
          <UserSelect
            mode="multiple"
            value={namespace?.owners}
            onChange={value => {
              form.setFieldsValue({ owners: value });
            }}
          />
        </Form.Item>

        <Form.Item
          name="testers"
          label="测试负责人"
          rules={[
            {
              required: true,
              message: '请选择测试负责人',
            },
          ]}
        >
          <UserSelect
            mode="multiple"
            value={namespace?.testers}
            onChange={value => {
              form.setFieldsValue({ testers: value });
            }}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
          rules={[
            {
              required: true,
              message: '请输入描述',
            },
          ]}
        >
          <TextArea rows={6} placeholder="请输入描述" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SwitchNamespaceEditor;

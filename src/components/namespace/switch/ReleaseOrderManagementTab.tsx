'use client';

import React, { useEffect } from 'react';
import { Pagination, Tag, message } from 'antd';
import { FileTextOutlined, CopyOutlined } from '@ant-design/icons';
import { Link } from 'ice';
import { User } from '@/components/user';
import { getFormatDate } from '@/utils/common';
import { ORDER_STATUS_2_TAG_COLOR, STAGE_TYPE_CONFIG } from '@/constants/releaseOrder';
import { useNamespaceReleaseOrders } from '@/hooks/useNamespaceReleaseOrders';
import { getReleaseOrderDetailUrl } from '@/utils/link';

interface ReleaseOrderManagementTabProps {
  namespaceId: string;
}

const ReleaseOrderManagementTab: React.FC<ReleaseOrderManagementTabProps> = ({ namespaceId }) => {
  const {
    releaseOrders,
    activeStatusTab,
    pagination,
    fetchReleaseOrders,
    fetchStatusCounts,
    handlePageChange,
    handleStatusTabChange,
    getReleaseOrderCounts,
  } = useNamespaceReleaseOrders({ namespaceId });

  // 初始化数据
  useEffect(() => {
    fetchReleaseOrders();
    fetchStatusCounts();
  }, [fetchReleaseOrders, fetchStatusCounts]);

  // 监听状态Tab和分页变化，重新获取数据
  useEffect(() => {
    fetchReleaseOrders();
  }, [activeStatusTab, pagination.current, pagination.pageSize, fetchReleaseOrders]);

  // 复制发布单ID
  const handleCopyReleaseOrderId = (releaseVersion: string) => {
    navigator.clipboard.writeText(releaseVersion).then(() => {
      message.success('发布单 ID 已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  return (
    <div>
      <div className="bg-white rounded shadow">
        <div className="p-3 border-b bg-gray-50">
          <div className="flex items-center">
            <span
              className={`mr-4 cursor-pointer ${activeStatusTab === 'all' ? 'text-blue-600 font-medium' : ''
                }`}
              onClick={() => handleStatusTabChange('all')}
            >
              {getReleaseOrderCounts().total} 全部
            </span>
            <span
              className={`mr-4 cursor-pointer ${activeStatusTab === 'inProgress' ? 'text-blue-600 font-medium' : ''
                }`}
              onClick={() => handleStatusTabChange('inProgress')}
            >
              {getReleaseOrderCounts().inProgress} 发布中
            </span>
            <span
              className={`cursor-pointer ${activeStatusTab === 'completed' ? 'text-blue-600 font-medium' : ''
                }`}
              onClick={() => handleStatusTabChange('completed')}
            >
              {getReleaseOrderCounts().completed} 已完成
            </span>
          </div>
        </div>

        {!releaseOrders ? (
          <div className="p-8 text-center text-gray-500">加载中...</div>
        ) : (
          <>
            {releaseOrders.map((order, index) => (
              <div key={index} className="p-4 border-b hover:bg-gray-50 transition-colors">
                <div className="flex justify-between mb-2">
                  <div className="flex-1 justify-center">
                    <Link
                      to={getReleaseOrderDetailUrl(order.releaseVersion!)}
                      className="mr-4 font-medium"
                    >
                      {order.description || `RO-${order.releaseVersion}`}
                    </Link>
                    <Tag color={ORDER_STATUS_2_TAG_COLOR(order.status!)}>
                      {STAGE_TYPE_CONFIG(order.status, order.currentStageType!) +
                        (order.status === 'IN_PROGRESS' &&
                          order.currentStageType === 'RATIO_GRAY' &&
                          order.grayRatio
                          ? ` ${(order.grayRatio / 100000) * 100}%`
                          : '')}
                    </Tag>
                  </div>
                </div>
                <div className="flex items-center text-xs text-gray-500 mb-2">
                  <span
                    className="mr-4 cursor-pointer hover:text-blue-600 flex items-center"
                    onClick={() => handleCopyReleaseOrderId(order.releaseVersion!)}
                    title="点击复制发布单ID"
                  >
                    RO-{order.releaseVersion}
                  </span>
                  <User empIds={[order.creator!]} showAvatar size={'xsmall'} />
                  <span className="ml-2 mr-4">创建于 {getFormatDate(order.gmtCreate!)}</span>
                  {order.gmtCreate !== order.gmtModified && (
                    <>
                      <User empIds={[order.modifier!]} showAvatar size={'xsmall'} />
                      <span className="ml-2">更新于 {getFormatDate(order.gmtModified!)}</span>
                    </>
                  )}
                </div>
              </div>
            ))}
            <div className="p-4 flex justify-center">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger
                showTotal={total => `共 ${total} 条`}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ReleaseOrderManagementTab;

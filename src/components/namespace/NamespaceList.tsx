import React from 'react';
import { Button, List, Row, Tag, Typography } from 'antd';
import {
  AppstoreOutlined,
  EditOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  MobileOutlined,
  SlidersOutlined,
} from '@ant-design/icons';
import { Link } from 'ice';
import { User } from '@/components/user';
import { NamespaceBO, SwitchNamespace } from '@/types/namespace';
import { getTBAppName } from '@/utils/common';
import { LOAD_LEVEL_CONFIG, TYPE_CONFIG } from '@/constants/namespace';
import { hasNsEditPermission, hasNsTestPermission } from '@/utils/permission';

interface NamespaceListProps {
  loading?: boolean;
  namespaces?: NamespaceBO[];
  total?: number;
  currentPage?: number;
  pageSize?: number;
  onPageChange?: (page: number, size?: number) => void;
  onEdit?: (namespace: SwitchNamespace) => void;
  showAppKey?: boolean;
  renderExtraInfo?: (item: any) => React.ReactNode;
  renderExtraActions?: (item: any) => React.ReactNode[];
  nameUrlBuilder?: (item: any) => string;
}

const { Text } = Typography;

export default function NamespaceList({
  loading = false,
  namespaces = [],
  total = 0,
  currentPage = 1,
  pageSize = 20,
  onPageChange = () => {},
  onEdit,
  showAppKey = false,
  renderExtraInfo,
  renderExtraActions,
  nameUrlBuilder,
}: NamespaceListProps) {
  const displayLoading = !!loading;

  // 渲染列表项
  const renderListItem = (item: any) => {
    const hasEditPermission = hasNsEditPermission(item);
    const hasViewPermission = hasNsTestPermission(item) || hasNsEditPermission(item);

    // 构建操作按钮列表
    const actions: React.ReactNode[] = [];

    // 默认的编辑按钮
    if (onEdit && hasEditPermission) {
      actions.push(
        <Button key="edit" type="link" icon={<EditOutlined />} onClick={() => onEdit(item)}>
          编辑
        </Button>,
      );
    }

    // 添加通过回调传入的额外操作
    if (renderExtraActions) {
      const extraActions = renderExtraActions(item);
      if (extraActions && extraActions.length > 0) {
        actions.push(...extraActions);
      }
    }

    return (
      <List.Item
        key={item.namespaceId}
        className="px-6 py-4 border-b border-gray-200 last:border-b-0"
        actions={actions}
      >
        <div className="flex-1 flex flex-col gap-2">
          <div className="flex items-center text-base font-medium">
            <FolderOpenOutlined className="mr-2 flex-shrink-0" />
            {hasViewPermission && nameUrlBuilder ? (
              <Link to={nameUrlBuilder(item)}>{item.name}</Link>
            ) : (
              <span>{item.name}</span>
            )}
            {/* 显示类型和加载级别标签 */}
            {item.type !== undefined && (
              <Tag className="ml-2">类型：{(TYPE_CONFIG[item.type] || {}).label}</Tag>
            )}
            {item.loadLevel !== undefined && (
              <Tag color={(LOAD_LEVEL_CONFIG[item.loadLevel] || {}).color} className="ml-1">
                加载级别：{(LOAD_LEVEL_CONFIG[item.loadLevel] || {}).label}
              </Tag>
            )}
          </div>

          <Row className={'text-gray-500 text-sm'} align={'middle'}>
            <Text className={'mr-2'} type="secondary">
              管理员
            </Text>
            {item.owners?.includes && <User empIds={item.owners} showAvatar size={'xsmall'} />}
            <Text className={'mr-2 ml-5'} type="secondary">
              测试负责人
            </Text>
            {item.testers?.includes && <User empIds={item.testers} showAvatar size={'xsmall'} />}
          </Row>

          <Row className="text-gray-500 text-sm" align={'middle'}>
            {showAppKey && (
              <>
                <MobileOutlined />
                <Text className={'ml-2 mr-4'} type="secondary">
                  {getTBAppName(item.appKey!) || item.appKey}
                </Text>
              </>
            )}
            {item.bizName && (
              <>
                <AppstoreOutlined />
                <Text className={'ml-2 mr-4'} type="secondary">
                  {item.bizName}
                </Text>
              </>
            )}
            <FileTextOutlined />
            <Text className={'ml-2 mr-4'} type="secondary">
              {item.description || item.detail}
            </Text>
            {item.parameterCount !== undefined && (
              <>
                <SlidersOutlined />
                <Text className={'ml-2 mr-4'} type="secondary">
                  参数 {item.parameterCount} 个
                </Text>
              </>
            )}
          </Row>
          {/* 渲染额外的信息 */}
          {renderExtraInfo && renderExtraInfo(item)}
        </div>
      </List.Item>
    );
  };

  return (
    <div>
      <List
        className="border border-gray-200 rounded-md pb-4"
        itemLayout="horizontal"
        loading={displayLoading}
        dataSource={namespaces}
        pagination={
          namespaces?.length
            ? {
                current: currentPage,
                pageSize: pageSize,
                total: total,
                onChange: onPageChange,
                position: 'bottom',
                align: 'center',
                showSizeChanger: true,
                showTotal: _total => `共有 ${_total} 个`,
              }
            : false
        }
        renderItem={renderListItem}
      />
    </div>
  );
}

import React, { useEffect, useState } from 'react';
import { Form, Input, message, Modal, Select, Tag } from 'antd';
import { LOAD_LEVEL_CONFIG, TYPE_CONFIG } from '@/constants/namespace';
import { isSubstring, sortAppKeys } from '@/lib/utils';
import { queryAppList } from '@/services/app';
import { UserSelect } from '@/components/user';
import { MtlModule, NamespaceBO } from '@/types/namespace';
import { addNamespace, updateNamespace } from '@/services/namespace';
import { useUserStore } from '@/store';
import { User } from '@/types/user';
import { getCurrentTBAppKeys } from '@/utils/common';
import { MtlModuleSelector } from '@/components';

const { Option } = Select;
const { TextArea } = Input;

// 淘宝iPhone客户端-ios(AppKey:21380790)、淘宝主客户端Android-android(AppKey:21646297)
export const FORCE_UPDATE_MODULE_APPKY = getCurrentTBAppKeys();

interface TextNamespaceEditorProps {
  visible: boolean;
  initialData: NamespaceBO | undefined;
  appList?: any[];
  showModal: (show: boolean) => void;
  mtlModuleMap?: { [key: string]: MtlModule };
  onSuccess?: () => void;
}

const TextNamespaceEditor: React.FC<TextNamespaceEditorProps> = (
  props: TextNamespaceEditorProps,
) => {
  const { visible, initialData, showModal, mtlModuleMap = {}, onSuccess } = props;

  const [form] = Form.useForm();
  const appKey = Form.useWatch('appKeyOrGroup', form);
  const currentUser: User = useUserStore(state => state.currentUser);
  const [appList, setAppList] = useState(props.appList || []);

  const isEdit = !!initialData;

  useEffect(() => {
    (async () => {
      if (!props.appList) {
        setAppList(await queryAppList());
      }
    })();
  }, []);

  const modules =
    initialData && initialData.modules
      ? initialData.modules
          .split(',')
          .filter(i => i)
          .map(i => parseInt(i))
      : [];

  useEffect(() => {
    if (visible) {
      if (initialData) {
        form.setFieldsValue(
          Object.assign({}, initialData, {
            owners: initialData.owners ? initialData.owners.split(',') : [],
            testers: initialData.testers ? initialData.testers.split(',') : [],
            modules,
          }),
        );
      } else {
        form.resetFields();
        form.setFieldValue('owners', [currentUser.empId]);
      }
    }
  }, [visible, initialData, form]);

  const handleSubmitNamespace = async (data: any) => {
    const params = Object.assign({}, data, {
      namespaceId: initialData?.namespaceId,
      appKey: data.appKeyOrGroup,
      testers: data.testers?.join(','),
      owners: data.owners?.join(','),
      modules: data.modules?.join(','),
    });

    try {
      if (initialData?.namespaceId) {
        // 更新
        await updateNamespace(params);
        message.success('更新成功');
      } else {
        // 新建
        await addNamespace(params);
        message.success('新建成功');
      }
      showModal(false);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      message.error(`${initialData?.namespaceId ? '更新' : '新建'}失败：${error.message}`);
    }
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    showModal(false);
  };

  return (
    <Modal
      centered
      maskClosable={false}
      title={isEdit ? '修改配置信息' : '新增配置信息'}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={550}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmitNamespace}>
        <Form.Item
          name="appKeyOrGroup"
          label="归属应用"
          rules={[
            {
              required: true,
              message: '请选择归属应用',
            },
          ]}
        >
          <Select
            disabled={isEdit}
            placeholder="输入 AppKey 或者名称进行查找"
            showSearch
            onChange={() => {
              form.setFieldValue('modules', null);
            }}
            filterOption={(input, option) => isSubstring(input, option!.children)}
          >
            {/* 手淘置顶 */}
            {sortAppKeys(getCurrentTBAppKeys(), appList).map(item => (
              <Option value={item.appKey}>{`${item.appKey}-${item.appName}`}</Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="name"
          label="名称"
          rules={[
            {
              required: true,
              message: '请输入名称',
            },
          ]}
          extra="一经创建便不允许修改，请注意大小写"
        >
          <Input disabled={isEdit} placeholder="请输入 namespace 名称，不能重复" />
        </Form.Item>

        <Form.Item
          name="type"
          label="类型"
          rules={[
            {
              required: true,
              message: '请选择类型',
            },
          ]}
        >
          <Select disabled={isEdit}>
            {Object.entries(TYPE_CONFIG).map(([key, value]) => {
              return (
                <Option value={Number(key)}>
                  <Tag color={value.color}>{value.label}</Tag> {value.desc}
                </Option>
              );
            })}
          </Select>
        </Form.Item>

        <Form.Item
          name="loadLevel"
          label="加载级别"
          rules={[
            {
              required: true,
              message: '请选择加载级别',
            },
          ]}
          extra={
            initialData ? (
              <p>
                如果需要修改加载级别，请走
                <a
                  target="_blank"
                  className="text-blue-500 hover:text-blue-700"
                  href={`/index.htm?#/namespace/namespace/revise/${initialData.namespaceId}`}
                >
                  数据订正
                </a>
              </p>
            ) : (
              '如需设置为其他加载级别，请在完成配置新建后点击配置编辑进行修改'
            )
          }
        >
          <Select disabled={isEdit}>
            {Object.entries(isEdit ? LOAD_LEVEL_CONFIG : { 0: LOAD_LEVEL_CONFIG[0] }).map(
              ([key, value]) => {
                return (
                  <Option value={Number(key)}>
                    <Tag color={value.color}>{value.label}</Tag> {value.desc}
                  </Option>
                );
              },
            )}
          </Select>
        </Form.Item>

        <Form.Item
          label="摩天轮模块"
          required={FORCE_UPDATE_MODULE_APPKY.includes(appKey)}
          name="modules"
        >
          <MtlModuleSelector
            appKey={appKey}
            disabled={!appKey}
            multiple
            defaultOptions={modules?.length ? modules.map(moduleId => mtlModuleMap[moduleId]) : []}
          />
        </Form.Item>

        <Form.Item
          label="管理员"
          name="owners"
          extra="至少填写 2 名管理员"
          rules={[
            {
              required: true,
              message: '至少填写 2 名管理员',
              type: 'array',
              min: 2,
            },
          ]}
        >
          <UserSelect
            mode="multiple"
            value={
              !isEdit
                ? [currentUser]
                : initialData.owners?.split(',').map(owner => ({
                    empId: owner,
                  }))
            }
          />
        </Form.Item>

        <Form.Item
          label="测试负责人"
          name="testers"
          required
          rules={[
            {
              required: true,
              message: '至少填写 1 名测试负责人',
              type: 'array',
              min: 1,
            },
          ]}
        >
          <UserSelect
            mode="multiple"
            value={
              !isEdit
                ? [currentUser]
                : initialData.testers?.split(',').map(tester => ({
                    empId: tester,
                  }))
            }
          />
        </Form.Item>

        <Form.Item
          name="detail"
          label="描述"
          rules={[
            {
              required: true,
              message: '请输入描述',
            },
          ]}
        >
          <TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TextNamespaceEditor;

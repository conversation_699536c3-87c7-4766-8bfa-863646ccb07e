import React, { useEffect, useState } from 'react';
import Editor, { Editor<PERSON><PERSON>, loader, useMonaco } from '@monaco-editor/react';
import { message, Modal, Typography } from 'antd';
import { isValidJSON } from '@/lib/utils';

loader.config({
  paths: {
    vs: 'https://g.alicdn.com/code/lib/monaco-editor/0.43.0/min/vs',
  },
});

const { Text } = Typography;

interface PropertiesEditorProps extends EditorProps {
  allowedKeys?: string[];
  jsonValueKeys?: string[];
}

export default function PropertiesEditor({
  allowedKeys,
  jsonValueKeys,
  ...props
}: PropertiesEditorProps) {
  const monaco = useMonaco();
  const [jsonValue, setJsonValue] = useState<string>('');
  const [isModalOpen, setModalOpen] = useState(false);
  const [editorRef, setEditorRef] = useState<any>(null);
  const [currentKey, setCurrentKey] = useState<string>('');
  const [currentLineNumber, setCurrentLineNumber] = useState<number>(0);

  const handleEditorChange = (value: string | undefined | null) => {
    if (value && editorRef) {
      const lines = value.split('\n');
      const markers: any[] = [];
      lines.forEach((line, index) => {
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          const _currentKey = match[1].trim();

          if (allowedKeys?.length && !allowedKeys.includes(_currentKey)) {
            markers.push({
              severity: monaco?.MarkerSeverity.Error,
              startLineNumber: index + 1,
              endLineNumber: index + 1,
              startColumn: 1,
              endColumn: line.length + 1,
              message: `KEY[${_currentKey}]不在默认配置中`,
            });
          }

          if (jsonValueKeys?.includes(_currentKey)) {
            const currentValue = match[2].trim();
            if (!isValidJSON(currentValue)) {
              markers.push({
                severity: monaco?.MarkerSeverity.Warning,
                startLineNumber: index + 1,
                endLineNumber: index + 1,
                startColumn: 1,
                endColumn: line.length + 1,
                message: '值从合法 JSON 改为非法 JSON',
              });
            }
          }
        }
      });

      monaco?.editor.setModelMarkers(
        editorRef.getModel(),
        'keyValidation',
        markers?.length ? markers : [],
      );
    }
  };

  useEffect(() => {
    if (props.value && monaco != null) {
      handleEditorChange(props.value);
    }
  }, [editorRef, props.value, allowedKeys, monaco, handleEditorChange]);

  if (!monaco) return null;

  const handleEditorWillMount = (editor: any) => {
    monaco.languages.register({ id: 'properties' });

    monaco.languages.setMonarchTokensProvider('properties', {
      tokenizer: {
        root: [
          [/([^\s=]+)(?=\s*=)/, { token: 'key' }],
          [/=/, { token: 'delimiter' }],
          [/([^\n]*)/, { token: 'value' }],
        ],
      },
    });

    monaco.languages.setLanguageConfiguration('properties', {
      comments: {
        lineComment: '#',
      },
    });

    monaco.editor.defineTheme('propertiesTheme', {
      base: 'vs',
      inherit: false,
      rules: [
        {
          token: 'key',
          foreground: '0000FF',
        },
        {
          token: 'delimiter',
          foreground: '8B4513',
        },
        {
          token: 'value',
          foreground: '009900',
        },
        {
          token: 'comment',
          foreground: '808080',
        },
      ],
      colors: {},
    });

    monaco.editor.setTheme('propertiesTheme');

    editor.addAction({
      id: 'format-value',
      label: '编辑 JSON 配置值',
      contextMenuGroupId: '1_modification',
      contextMenuOrder: 1.5,
      run: (_editor: any) => {
        const position = _editor.getPosition();
        const lineContent = _editor.getValue().split('\n')[position.lineNumber - 1];
        const match = lineContent.match(/^([^=]+)=(.*)$/);
        if (match) {
          setCurrentKey(match[1].trim());
          setCurrentLineNumber(position.lineNumber);
          setJsonValue(match[2].trim());
          setModalOpen(true);
        }
      },
    });
  };

  const updateOriginalEditor = () => {
    if (editorRef && currentKey) {
      const model = editorRef.getModel();
      const lines = model.getValue().split('\n');
      lines[currentLineNumber - 1] = `${currentKey}=${JSON.stringify(JSON.parse(jsonValue))}`;
      const newContent = lines.join('\n');
      model.setValue(newContent);
      handleEditorChange(newContent);
    }
  };

  let showJsonValue = jsonValue;
  let isValidJson = isValidJSON(jsonValue);
  try {
    showJsonValue = JSON.stringify(JSON.parse(jsonValue), null, 2);
  } catch (error) {
    // If parsing fails, keep the original value
  }

  return (
    <>
      <Editor
        {...props}
        language="properties"
        onMount={editor => {
          handleEditorWillMount(editor);
          setEditorRef(editor);
        }}
        onChange={(value, ev) => {
          props.onChange && props.onChange(value, ev);
          handleEditorChange(value);
        }}
      />

      <Modal
        width={1000}
        title={
          <div>
            {`编辑配置「${currentKey}」值`}
            <span className={'ml-8'}>
              {isValidJson ? (
                <Text type="success">合法 JSON</Text>
              ) : (
                <Text type="danger">非法 JSON</Text>
              )}
            </span>
          </div>
        }
        open={isModalOpen}
        onCancel={() => setModalOpen(false)}
        onOk={() => {
          if (isValidJson) {
            updateOriginalEditor();
            setModalOpen(false);
          } else {
            message.error('配置内容为非法 JSON');
          }
        }}
        afterClose={() => {
          monaco.editor?.setTheme('propertiesTheme');
        }}
      >
        <Editor
          language="json"
          height={'600px'}
          value={showJsonValue}
          onChange={value => {
            setJsonValue(value || '');
          }}
        />
      </Modal>
    </>
  );
}

import React, { useMemo, useState } from 'react';
import { Space, Tabs, Tooltip } from 'antd';
import { EditOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { DiffEditor, loader } from '@monaco-editor/react';
import { SCENE_MAP } from '@/constants/scene';
import { SceneContent } from '@/types/version';

loader.config({
  paths: {
    vs: 'https://g.alicdn.com/code/lib/monaco-editor/0.43.0/min/vs',
  },
});

interface StrategyDiffProps {
  originalScenesContents: SceneContent[];
  diffScenesContents: SceneContent[];
}

export default function PropertiesDiffViewer({
  originalScenesContents,
  diffScenesContents,
}: StrategyDiffProps) {
  const [activeDiffScene, setActiveDiffScene] = useState<string | null>(null);

  const scenesChanges = useMemo(() => {
    const originalSceneNames = new Set(originalScenesContents.map(i => i.name));
    const modifiedSceneNames = new Set(diffScenesContents.map(i => i.name));

    return {
      added: diffScenesContents.filter(scene => !originalSceneNames.has(scene.name)),
      removed: originalScenesContents.filter(scene => !modifiedSceneNames.has(scene.name)),
      modified: diffScenesContents.filter(
        scene =>
          originalSceneNames.has(scene.name) &&
          originalScenesContents.find(s => s.name === scene.name)?.content !== scene.content,
      ),
    };
  }, [originalScenesContents, diffScenesContents]);

  const allScenes = useMemo(() => {
    const scenes = new Set([
      ...originalScenesContents.map(s => s.name),
      ...diffScenesContents.map(s => s.name),
    ]);
    return Array.from(scenes);
  }, [originalScenesContents, diffScenesContents]);

  const getSceneStatus = (sceneName: string) => {
    if (scenesChanges.added.some(s => s.name === sceneName)) return 'added';
    if (scenesChanges.removed.some(s => s.name === sceneName)) return 'removed';
    if (scenesChanges.modified.some(s => s.name === sceneName)) return 'modified';
    return 'unchanged';
  };

  const getSceneIcon = (status: string) => {
    switch (status) {
      case 'added':
        return <PlusOutlined />;
      case 'removed':
        return <MinusOutlined />;
      case 'modified':
        return <EditOutlined />;
      default:
        return null;
    }
  };

  return (
    <Tabs
      activeKey={activeDiffScene || undefined}
      onChange={setActiveDiffScene}
      items={allScenes.map(sceneName => {
        const status = getSceneStatus(sceneName);
        return {
          key: sceneName,
          label: (
            <Tooltip title={status}>
              <Space>
                {getSceneIcon(status)}
                <span>{SCENE_MAP[sceneName] || sceneName}</span>
              </Space>
            </Tooltip>
          ),
          children: (
            <DiffEditor
              height="400px"
              language="json"
              original={originalScenesContents.find(s => s.name === sceneName)?.content || ''}
              modified={diffScenesContents.find(s => s.name === sceneName)?.content || ''}
              options={{
                readOnly: true,
                renderSideBySide: true,
              }}
            />
          ),
        };
      })}
    />
  );
}

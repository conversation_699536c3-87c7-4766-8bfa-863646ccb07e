import React, { useState } from 'react';
import { Tabs } from 'antd'; // 假设 antd 是使用的 UI 库
import PropertiesEditor from '@/components/properties/PropertiesEditor'; // 假设这是编辑器组件的位置
import { EditOutlined } from '@ant-design/icons';
import { getJSONValueKeysFromProperties } from '@/lib/utils';
import { SCENE_MAP } from '@/constants/scene';

const { TabPane } = Tabs;

interface ScenePropertiesEditorProps {
  originalScenesContents: any[];
  namespaceType: number;
  readonly: boolean;
  activeSceneName?: string;
  editorHeight?: string;
  modifiedScenesContents?: any[];
  setActiveSceneName?: (sceneName: string) => void;
  setSceneModalVisible?: (visible: boolean) => void;
  setIsEditingSceneName?: (isEditing: boolean) => void;
  handleEditorChange?: (value: any, sceneName: string) => void;
  onSceneTabEdit?: (
    targetKey: string | React.MouseEvent<Element, MouseEvent>,
    action: 'add' | 'remove' | 'edit',
  ) => void;
  editorTip?: string;
  allowedKeys?: string[];
}

const ScenePropertiesEditor: React.FC<ScenePropertiesEditorProps> = ({
  editorTip,
  modifiedScenesContents,
  originalScenesContents,
  activeSceneName,
  setActiveSceneName,
  setSceneModalVisible,
  setIsEditingSceneName,
  handleEditorChange,
  onSceneTabEdit,
  namespaceType,
  allowedKeys,
  readonly = false,
  editorHeight = '400px',
}) => {
  modifiedScenesContents = modifiedScenesContents || originalScenesContents;
  if (!activeSceneName || !setActiveSceneName) {
    const [_activeSceneName, _setActiveSceneName] = useState('Default');
    activeSceneName = _activeSceneName;
    setActiveSceneName = _setActiveSceneName;
  }

  return (
    <Tabs
      tabBarExtraContent={editorTip || ''}
      activeKey={activeSceneName}
      onChange={key => {
        setActiveSceneName && setActiveSceneName(key);
      }}
      type={readonly ? 'card' : 'editable-card'}
      onEdit={onSceneTabEdit}
    >
      {modifiedScenesContents.map(scenario => {
        const jsonValueKeys =
          namespaceType === 1
            ? getJSONValueKeysFromProperties(
                originalScenesContents.find(i => i.name === scenario.name)?.content,
              )
            : [];

        return (
          <TabPane
            tab={
              <span>
                {SCENE_MAP[scenario.name]}
                {scenario.name !== 'Default' && !readonly && (
                  <EditOutlined
                    onClick={e => {
                      e.stopPropagation();
                      setActiveSceneName && setActiveSceneName(scenario.name);
                      setIsEditingSceneName && setIsEditingSceneName(true);
                      setSceneModalVisible && setSceneModalVisible(true);
                    }}
                    style={{ marginLeft: 8 }}
                  />
                )}
              </span>
            }
            key={scenario.name}
            closable={scenario.name !== 'Default' && !readonly}
          >
            <PropertiesEditor
              options={{
                readOnly: readonly,
              }}
              jsonValueKeys={jsonValueKeys}
              allowedKeys={scenario.name !== 'Default' && allowedKeys ? allowedKeys : undefined}
              height={editorHeight}
              value={scenario.content}
              onChange={value => handleEditorChange && handleEditorChange(value, scenario.name)}
            />
          </TabPane>
        );
      })}
    </Tabs>
  );
};

export default ScenePropertiesEditor;

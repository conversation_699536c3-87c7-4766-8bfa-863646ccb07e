'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Select, Space, Typography } from 'antd';
import { CloseOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { FaWandMagicSparkles } from 'react-icons/fa6';
import services from '@/services/orange-be';
import {
  COLORS,
  CONDITION_KEYS,
  ConditionKey,
  KEY_OPERATORS,
  Operator,
  OPERATORS,
  VALIDATION_PATTERNS,
} from '@/constants/condition';
import { getTitle } from '@/utils/view';
import { getColorName } from '@/utils/condition';

const { Option } = Select;
const { Text } = Typography;
const { ConditionController, NamespaceController, AIController } = services;

interface ExpressionItem {
  id: string;
  key?: ConditionKey;
  operator?: Operator;
  value?: string;
  error?: string;
}

export interface ConditionData {
  id: string;
  name: string;
  color: string;
  expressionList: ExpressionItem[];
}

interface ConditionUpdateProps extends Omit<ConditionData, 'id'> {
  id?: string;
}

interface ConditionEditorProps {
  visible: boolean;
  onClose: () => void;
  onSave?: (condition: ConditionUpdateProps) => void;
  mode: 'create' | 'edit' | 'view';
  initialData?: ConditionData;
  existingConditions?: ConditionData[];
  namespaceId?: string;
}

const ConditionEditor: React.FC<ConditionEditorProps> = ({
  visible,
  onClose,
  onSave,
  mode = 'create',
  initialData,
  existingConditions = [],
  namespaceId,
}) => {
  const [form] = Form.useForm(); // 使用 Form 组件进行表单验证
  const [name, setName] = useState('');
  const [nameError, setNameError] = useState(''); // 新增：名称错误状态
  const [color, setColor] = useState('');
  const [expressionList, setExpressionList] = useState<ExpressionItem[]>([
    {
      key: 'app_ver',
      operator: '>=',
      value: '',
      id: `criteria-${Date.now()}`,
    },
  ]);
  const [deviceCount, setDeviceCount] = useState<number | null>(null);
  const [deviceCountLoading, setDeviceCountLoading] = useState(false);
  const [appKey, setAppKey] = useState<string>('');
  const [nameGenerating, setNameGenerating] = useState(false);

  // 将数字转换为相对模糊的表示
  const formatFuzzyNumber = (num: number): string => {
    if (num >= 100000000) {
      // 亿级别
      const yi = Math.floor(num / 100000000);
      return `约 ${yi} 亿+`;
    } else if (num >= 10000) {
      // 万级别
      const wan = Math.floor(num / 10000);
      return `约 ${wan} 万+`;
    } else if (num >= 1000) {
      // 千级别
      const qian = Math.floor(num / 1000);
      return `约 ${qian} 千+`;
    } else {
      return num.toString();
    }
  };

  // 检查所有条件输入框是否都有值
  const areAllConditionsFilled = (): boolean => {
    return expressionList.length > 0 && expressionList.every(item => item.value?.trim());
  };

  // 检查是否包含 did_hash 条件
  const hasDidHashCondition = (): boolean => {
    return expressionList.some(item => item.key === 'did_hash');
  };

  const handleGenerateName = async () => {
    if (expressionList.length === 0) {
      message.error('请先添加条件');
      return;
    }

    if (nameGenerating) {
      return; // 防止重复调用
    }

    setNameGenerating(true);
    try {
      // 构建表达式
      const expression = {
        operator: 'AND',
        children: expressionList.map(item => ({
          key: item.key,
          operator: item.operator,
          value: item.value,
        })),
      };

      const response = await AIController.idealabPredict({
        appCode: 'dtkSMyYAqiL',
        variableMap: {
          expression: JSON.stringify(expression),
        },
      });

      if (response.success && response.data) {
        setName(response.data);
        message.success('生成名称成功');
      } else {
        message.error(response.message || '生成名称失败');
      }
    } catch (error) {
      message.error('生成条件名称失败');
    } finally {
      setNameGenerating(false);
    }
  };

  // 获取 appKey
  useEffect(() => {
    if (namespaceId) {
      NamespaceController.getByNamespaceId({ namespaceId })
        .then(response => {
          setAppKey(response.data?.appKey || '');
        })
        .catch(error => {
          console.error('获取 appKey 失败:', error);
        });
    }
  }, [namespaceId]);

  useEffect(() => {
    if (initialData && (mode === 'edit' || mode === 'view')) {
      setName(initialData.name);
      setColor(initialData.color);

      if (initialData.expressionList && initialData.expressionList.length > 0) {
        setExpressionList(initialData.expressionList);
      }
    } else if (mode === 'create') {
      setName('');
      setNameError('');
      setColor('');
      setExpressionList([
        {
          key: 'app_ver',
          operator: '>=',
          value: '',
          id: `criteria-${Date.now()}`,
        },
      ]);
    }
  }, [initialData, mode, visible]);

  const onClickAddNewCriteria = () => {
    setExpressionList([
      ...expressionList,
      {
        key: 'app_ver',
        operator: '>=',
        value: '',
        id: `criteria-${Date.now()}-${expressionList.length}`,
      },
    ]);
  };

  const handleRemoveCriteria = (id: string) => {
    if (expressionList.length > 1) {
      setExpressionList(expressionList.filter(item => item.id !== id));
    }
  };

  const handleCriteriaChange = (id: string, field: keyof ExpressionItem, value: any) => {
    setExpressionList(
      expressionList.map(item => {
        if (item.id === id) {
          if (field === 'key') {
            return {
              ...item,
              [field]: value,
              operator: KEY_OPERATORS[value as ConditionKey][0],
              value: '',
              error: undefined,
            };
          }
          if (field === 'value') {
            return {
              ...item,
              [field]: value,
              error: value.trim() ? undefined : item.error,
            };
          }
          return {
            ...item,
            [field]: value,
          };
        }
        return item;
      }),
    );
  };

  // 验证表单
  const validateForm = () => {
    let valid = true;
    let updatedExpressionList = [...expressionList];

    // 验证名称
    if (!name.trim()) {
      setNameError('请输入条件名称');
      valid = false;
    } else if (name.trim() === '默认') {
      setNameError('条件名不能是【默认】');
      valid = false;
    } else if (
      mode === 'create' &&
      existingConditions.some(condition => condition.name === name.trim())
    ) {
      setNameError('已存在相同条件名的条件');
      valid = false;
    } else if (
      mode === 'edit' &&
      existingConditions.some(
        condition => condition.name === name.trim() && condition.id !== initialData?.id,
      )
    ) {
      setNameError('已存在相同条件名的条件');
      valid = false;
    } else {
      setNameError('');
    }

    // 验证表达式列表中的值
    updatedExpressionList = expressionList.map(item => {
      if (!item.value?.trim()) {
        valid = false;
        return {
          ...item,
          error: '请输入值',
        };
      }

      // 验证值长度
      if (item.value.trim().length > 1000) {
        valid = false;
        return {
          ...item,
          error: '值长度不能超过1000个字符',
        };
      }

      // 验证格式
      if (
        (item.key === 'app_ver' || item.key === 'os_ver') &&
        !VALIDATION_PATTERNS.version.test(item.value?.trim())
      ) {
        valid = false;
        return {
          ...item,
          error: '请输入合法的版本号',
        };
      } else if (item.key === 'did_hash' && !VALIDATION_PATTERNS.didHash.test(item.value?.trim())) {
        valid = false;
        return {
          ...item,
          error: '请输入合法的设备哈希规则，例如: 100_0-10',
        };
      } else if (item.key === 'm_brand' || item.key === 'm_model') {
        // 厂商和机型只允许字母、数字、下划线、逗号和中文
        if (!/^[\u4e00-\u9fa5a-zA-Z0-9_,]+$/.test(item.value?.trim())) {
          valid = false;
          return {
            ...item,
            error: '只能包含字母、数字、下划线、逗号和中文',
          };
        }
      }

      return {
        ...item,
        error: undefined,
      };
    });

    setExpressionList(updatedExpressionList);
    return valid;
  };

  const handleSaveCondition = () => {
    // 验证表单
    if (!validateForm()) {
      message.error('请修正不合法的内容');
      return;
    }

    onSave &&
      onSave({
        name,
        color,
        expressionList,
        id: initialData?.id,
      });
  };

  // 检查名称变更
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setName(value);
    if (value.trim()) {
      setNameError('');
    }
  };

  // 查询设备量
  const handleQueryDeviceCount = async () => {
    if (!appKey || expressionList.length === 0) {
      message.error('缺少必要参数');
      return;
    }

    setDeviceCountLoading(true);
    try {
      // 构建条件表达式
      const expression = {
        operator: 'AND',
        children: expressionList.map(item => ({
          key: item.key,
          operator: item.operator,
          value: item.value,
        })),
      };

      const response = await ConditionController.countDevices({
        appKey,
        expression,
      });

      setDeviceCount(response.data);
    } catch (error) {
      message.error('查询设备量失败');
    } finally {
      setDeviceCountLoading(false);
    }
  };

  const renderFooterButtons = () => {
    switch (mode) {
      case 'create':
      case 'edit':
        return [
          <Button key="cancel" onClick={onClose}>
            取消
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveCondition}>
            保存
          </Button>,
        ];
      case 'view':
        return [
          <Button key="close" onClick={onClose}>
            关闭
          </Button>,
        ];
      default:
        return [];
    }
  };

  return (
    <Modal
      title={
        <Space>
          <span>{getTitle(mode)}条件</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={renderFooterButtons()}
      closeIcon={<CloseOutlined />}
      width={700}
    >
      <Form form={form} layout="vertical">
        <div className="flex items-start mb-6">
          <div className="flex-1 mr-4">
            <Form.Item
              label="展示名"
              validateStatus={nameError ? 'error' : ''}
              help={nameError}
              required
            >
              <Input
                placeholder="请输入名称"
                value={name}
                onChange={handleNameChange}
                disabled={mode === 'view'}
                status={nameError ? 'error' : ''}
                addonAfter={
                  mode !== 'view' && (
                    // @ts-ignore
                    <FaWandMagicSparkles
                      onClick={handleGenerateName}
                      style={{
                        cursor: nameGenerating ? 'not-allowed' : 'pointer',
                        color: nameGenerating ? '#d9d9d9' : '#1677ff',
                      }}
                      title={nameGenerating ? '生成中...' : '生成名称'}
                    />
                  )
                }
              />
            </Form.Item>
          </div>
          <div className="w-48">
            <Form.Item label="颜色">
              <Select
                value={color}
                onChange={setColor}
                style={{ width: '100%' }}
                disabled={mode === 'view'}
              >
                {Object.entries(COLORS).map(([key, { value }]) => (
                  <Option key={key} value={value}>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: value }} />
                      <span>{getColorName(key)}</span>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        </div>

        <div className="mb-6">
          <div className="mb-2 font-medium">条件</div>

          {mode !== 'view' && (
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={onClickAddNewCriteria}
              className="mb-3 w-full"
            >
              添加条件
            </Button>
          )}

          {expressionList.map((criteria, index) => (
            <div key={criteria.id} className="mb-3">
              <div className="flex gap-2 items-center">
                <div
                  className={`w-12 text-center font-medium ${
                    index === 0 ? 'text-gray-300' : 'text-gray-500'
                  }`}
                >
                  AND
                </div>
                <Select
                  className={'flex-1'}
                  value={criteria.key}
                  onChange={value => handleCriteriaChange(criteria.id, 'key', value)}
                  disabled={mode === 'view'}
                >
                  {Object.entries(CONDITION_KEYS).map(([key, label]) => (
                    <Option key={key} value={key}>
                      {label}
                    </Option>
                  ))}
                </Select>

                <Select
                  className={'flex-1'}
                  value={criteria.operator}
                  onChange={value => handleCriteriaChange(criteria.id, 'operator', value)}
                  disabled={mode === 'view'}
                >
                  {criteria.key &&
                    KEY_OPERATORS[criteria.key].map(operator => (
                      <Option key={operator} value={operator}>
                        {OPERATORS[operator]}
                      </Option>
                    ))}
                </Select>

                <Form.Item
                  className="flex-1 mb-0"
                  validateStatus={criteria.error ? 'error' : ''}
                  help={criteria.error}
                >
                  <Input
                    placeholder={`${criteria.key === 'did_hash' ? '例如: 100_0-10' : '请输入值'}`}
                    value={criteria.value as string}
                    onChange={e => handleCriteriaChange(criteria.id, 'value', e.target.value)}
                    disabled={mode === 'view'}
                    status={criteria.error ? 'error' : ''}
                  />
                </Form.Item>

                {mode !== 'view' && (
                  <Button
                    type="text"
                    disabled={expressionList.length === 1}
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveCriteria(criteria.id)}
                    size="small"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        {mode !== 'view' && (
          <div className="bg-gray-50 p-3 rounded">
            <div className="flex items-center justify-between">
              <Text>
                符合所选条件近期活跃的设备量{' '}
                {deviceCount !== null ? formatFuzzyNumber(deviceCount) : ''}
              </Text>
              <Button
                type="primary"
                size="small"
                loading={deviceCountLoading}
                onClick={handleQueryDeviceCount}
                disabled={!appKey || !areAllConditionsFilled() || hasDidHashCondition()}
              >
                查询设备量
              </Button>
            </div>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default ConditionEditor;

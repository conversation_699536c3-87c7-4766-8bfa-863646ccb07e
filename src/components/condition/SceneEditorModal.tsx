'use client';
import { useCallback, useEffect } from 'react';
import { Alert, Form, message, Modal, Select, Tag } from 'antd';
import { SCENE_VALUES } from '@/constants/scene';

interface SceneFormValue {
  locale: string;
}

interface SceneFormModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (locale: string) => void;
  initialValue?: string;
  namespaceType: number;
}

export default function SceneEditorModal({
  visible,
  onCancel,
  onOk,
  initialValue,
  namespaceType,
}: SceneFormModalProps) {
  const [form] = Form.useForm<SceneFormValue>();

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        locale: initialValue ?? undefined,
      });
    }
  }, [visible, initialValue, form]);

  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      onOk(values.locale);
      form.resetFields();
    } catch (e) {
      message.error(e?.message || '表单验证失败');
    }
  }, [form, onOk]);

  const handleCancel = useCallback(() => {
    onCancel();
    form.resetFields();
  }, [form, onCancel]);

  const i18nOptions =
    SCENE_VALUES.i18n?.map(i => ({
      value: i.value,
      label: i.label,
    })) ?? [];

  return (
    <Modal
      title={`${initialValue ? '编辑' : '添加'}场景`}
      open={visible}
      onCancel={handleCancel}
      onOk={handleOk}
      okText="确定"
      cancelText="取消"
      destroyOnClose
    >
      <Alert
        message={
          <p>
            {`客户端将根据端上设置的语言自动返回该语言下的配置内容，未配置多语言的${
              namespaceType === 1 ? ' KEY ' : '配置'
            }返回`}
            <Tag className="mx-1">默认</Tag>
            场景中的值
          </p>
        }
        type="warning"
        className="mb-4"
      />
      <Form form={form} preserve={false}>
        <Form.Item
          name="locale"
          label="语言地区"
          rules={[
            {
              required: true,
              message: '请选择语言地区',
            },
          ]}
        >
          <Select options={i18nOptions} placeholder="请选择语言地区" />
        </Form.Item>
      </Form>
    </Modal>
  );
}

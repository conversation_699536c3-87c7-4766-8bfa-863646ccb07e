import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Form, Button, Select, Input, Row, Col } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

export interface Expression {
  key: string;
  operator: string;
  value: string;
}

interface StrategyEditorProps {
  expressions: Expression[];
  isEditing: boolean;
  onExpressionsChange: (expressions: Expression[]) => void;
  onEditingChange: (isEditing: boolean) => void;
}

const expressionKeys = ['app_ver', 'os_ver', 'did_hash', 'm_brand', 'm_model'];
const comparisonKey2Operators: Record<string, string[]> = {
  app_ver: ['=', '!=', '~=', '!~', '>', '>=', '<', '<='],
  os_ver: ['=', '!=', '~=', '!~', '>', '>=', '<', '<='],
  did_hash: ['='],
  m_brand: ['=', '!=', '~=', '!~'],
  m_model: ['=', '!=', '~=', '!~'],
};

export default function StrategyEditor({
                                         expressions,
                                         isEditing,
                                         onExpressionsChange,
                                         onEditingChange,
                                       }: StrategyEditorProps) {
  const [form] = Form.useForm();
  const [localExpressions, setLocalExpressions] = useState<Expression[]>(expressions);

  useEffect(() => {
    setLocalExpressions(expressions);
  }, [expressions]);

  const handleAddExpression = useCallback(() => {
    const newExpressions = [...localExpressions, {
      key: 'app_ver',
      operator: '=',
      value: '',
    }];
    setLocalExpressions(newExpressions);
    onExpressionsChange(newExpressions);
  }, [localExpressions, onExpressionsChange]);

  const handleExpressionChange = useCallback((index: number, field: keyof Expression, fieldValue: string) => {
    const updatedExpressions = localExpressions.map((exp, i) =>
      (i === index ? {
        ...exp,
        [field]: fieldValue,
      } : exp),
    );
    if (field === 'key') {
      updatedExpressions[index].operator = comparisonKey2Operators[fieldValue][0];
      updatedExpressions[index].value = '';
    }
    setLocalExpressions(updatedExpressions);
    onExpressionsChange(updatedExpressions);
  }, [localExpressions, onExpressionsChange]);

  const handleRemoveExpression = useCallback((index: number) => {
    const updatedExpressions = localExpressions.filter((_, i) => i !== index);
    setLocalExpressions(updatedExpressions);
    onExpressionsChange(updatedExpressions);
  }, [localExpressions, onExpressionsChange]);

  const validateStrategy = useCallback(() => {
    for (const expr of localExpressions) {
      if (!expr.key || !expr.operator || !expr.value.trim()) {
        return false;
      }
      if (expr.key === 'app_ver' || expr.key === 'os_ver') {
        if (!/^\d+(\.\d+)*$/.test(expr.value)) {
          return false;
        }
      }
      if (expr.key === 'did_hash') {
        if (!/^\d+_\d+-\d+$/.test(expr.value)) {
          return false;
        }
      }
    }
    return true;
  }, [localExpressions]);

  const handleSave = useCallback(() => {
    if (validateStrategy()) {
      onEditingChange(false);
    }
  }, [validateStrategy, onEditingChange]);

  const expressionString = useMemo(() => {
    return localExpressions
      .map((expr) => `${expr.key}${expr.operator}${expr.value}`)
      .join('&');
  }, [localExpressions]);

  const isValid = useMemo(() => validateStrategy(), [validateStrategy]);

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h2 className="font-bold">策略表达式</h2>
          <p className="">{expressionString || ''}</p>
        </div>
        <Button
          type="link"
          onClick={() => (isEditing ? handleSave() : onEditingChange(true))}
          disabled={isEditing && !isValid}
        >
          {isEditing ? '保存' : '编辑'}
        </Button>
      </div>
      {isEditing && (
        <Form form={form} layout="vertical" className="mt-2">
          <Form.List name="expressions">
            {() => (
              <>
                {localExpressions.map((expr, index) => (
                  <Row key={index} gutter={8} className="mb-2">
                    <Col span={7}>
                      <Form.Item className="mb-0">
                        <Select
                          value={expr.key}
                          onChange={(val) => handleExpressionChange(index, 'key', val)}
                          options={expressionKeys.map((key) => ({
                            label: key,
                            value: key,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item className="mb-0">
                        <Select
                          value={expr.operator}
                          onChange={(val) => handleExpressionChange(index, 'operator', val)}
                          options={comparisonKey2Operators[expr.key].map((op) => ({
                            label: op,
                            value: op,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={11}>
                      <Form.Item className="mb-0">
                        <Input
                          value={expr.value}
                          onChange={(e) => handleExpressionChange(index, 'value', e.target.value)}
                          placeholder="Expression value"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={2}>
                      <Form.Item className="mb-0">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleRemoveExpression(index)}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                ))}
                <Form.Item className="mb-2">
                  <Button
                    type="dashed"
                    onClick={handleAddExpression}
                    icon={<PlusOutlined />}
                    className="w-full"
                  >
                    添加条件
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      )}
    </div>
  );
}

import { But<PERSON>, Card } from 'antd';

interface AnnouncementCardProps {
    className?: string;
}

export default function AnnouncementCard({ className }: AnnouncementCardProps) {
    // 获取公告数据
    // _DATA_.announcement 示例结构:
    // {
    //   title: "Orange 2.0",
    //   content: "单参数管理功能隆重登场",
    //   items: [
    //     "单参数管理功能隆重登场",
    //     "灰度发布全新升级",
    //     "性能优化大幅提升"
    //   ],
    //   linkUrl: "https://example.com/details",
    //   linkText: "查看详细"
    // }
    const announcement = JSON.parse((window as any)?._DATA_?.announcement || '{}');

    return (
        <Card
            className={className}
            style={{
                background: 'linear-gradient(135deg, #ffb366 0%, #ff9966 100%)',
                color: 'white',
                border: 'none',
            }}
        >
            <div className="flex justify-between items-start mb-4">
                <h3 className="text-white text-lg font-semibold">
                    {announcement?.title || 'Orange 2.0'}
                </h3>
            </div>
            {announcement && announcement.content ? (
                <>
                    <div className="space-y-2 text-sm mb-4">
                        {announcement.items?.map((item: string, index: number) => (
                            <div key={index}>{item}</div>
                        )) || <div>{announcement.content}</div>}
                    </div>
                    {announcement.linkUrl && (
                        <Button
                            type="primary"
                            className="w-full bg-white/20 border-white/30 hover:bg-white/30 text-white"
                            style={{
                                boxShadow: 'none',
                                background: 'rgba(255, 255, 255, 0.2)',
                                borderColor: 'rgba(255, 255, 255, 0.3)',
                            }}
                            onClick={() => window.open(announcement.linkUrl)}
                        >
                            {announcement.linkText || '查看详细'}
                        </Button>
                    )}
                </>
            ) : (
                <div className="text-sm">欢迎使用 Orange2.0 控制台</div>
            )}
        </Card>
    );
} 
import { useEffect, useState } from 'react';
import { Card, Tabs } from 'antd';
import {
  AppstoreOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { Link } from 'ice';
import * as NamespaceController from '@/services/orange-be/NamespaceController';
import { getSwitchNamespaceDetailUrl } from '@/utils/link';
import { getTBAppName } from '@/utils/common';

interface NamespaceSidebarProps {
  className?: string;
}

// 展示的命名空间数量
const NS_LIMIT = 7;

export default function NamespaceSidebar({ className }: NamespaceSidebarProps) {
  const [frequentNamespaces, setFrequentNamespaces] = useState<API.NamespaceDTO[]>([]);
  const [recentNamespaces, setRecentNamespaces] = useState<API.NamespaceDTO[]>([]);
  const [frequentLoading, setFrequentLoading] = useState(false);
  const [recentLoading, setRecentLoading] = useState(false);

  useEffect(() => {
    const fetchFrequentNamespaces = async () => {
      setFrequentLoading(true);
      try {
        const res = await NamespaceController.getFrequentNamespaces({ limit: NS_LIMIT });
        if (res.success && res.data) {
          setFrequentNamespaces(res.data);
        }
      } finally {
        setFrequentLoading(false);
      }
    };
    fetchFrequentNamespaces();
  }, []);

  useEffect(() => {
    const fetchRecentNamespaces = async () => {
      setRecentLoading(true);
      try {
        const res = await NamespaceController.getRecentNamespaces({ limit: NS_LIMIT });
        if (res.success && res.data) {
          setRecentNamespaces(res.data);
        }
      } finally {
        setRecentLoading(false);
      }
    };
    fetchRecentNamespaces();
  }, []);

  const renderNamespaceList = (
    namespaces: API.NamespaceDTO[],
    isLoading: boolean,
    emptyText: string,
  ) => {
    if (isLoading) {
      return <div className="text-gray-400">加载中...</div>;
    }

    namespaces = namespaces.filter(ns => !!ns);

    if (namespaces.length === 0) {
      return <div className="text-gray-400">{emptyText}</div>;
    }

    return (
      <div className="space-y-3">
        {namespaces.map(ns => (
          <div key={ns.namespaceId} className="mb-3">
            <div className="flex items-center mb-2">
              <FolderOpenOutlined className="mr-1 text-base text-gray-500" />
              <Link
                className="font-medium text-sm"
                to={getSwitchNamespaceDetailUrl(ns.namespaceId!)}
              >
                {ns.name}
              </Link>
            </div>
            <div className="flex items-center text-xs text-gray-500 mb-0.5">
              <AppstoreOutlined className="mr-1" />
              <span>{getTBAppName(ns.appKey || '') || ns.appKey}</span>
            </div>
            {ns.description && (
              <div className="flex items-center text-xs text-gray-500">
                <FileTextOutlined className="mr-1" />
                <span>{ns.description}</span>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const tabItems = [
    {
      key: 'frequent',
      label: (
        <span className="flex items-center">
          <StarOutlined className="mr-1" />
          常用
        </span>
      ),
      children: renderNamespaceList(frequentNamespaces, frequentLoading, '暂无常用命名空间'),
    },
    {
      key: 'recent',
      label: (
        <span className="flex items-center">
          <ClockCircleOutlined className="mr-1" />
          最近
        </span>
      ),
      children: renderNamespaceList(recentNamespaces, recentLoading, '暂无最近访问命名空间'),
    },
  ];

  return (
    <Card className={className}>
      <div className="space-y-4">
        <Tabs
          tabBarExtraContent={{
            left: <h4 className="font-medium text-lg text-gray-800 mr-12">命名空间</h4>,
          }}
          defaultActiveKey="frequent"
          items={tabItems}
          size="small"
          className="namespace-tabs"
          style={
            {
              '--ant-tabs-content-padding': '0',
            } as React.CSSProperties
          }
        />
      </div>
    </Card>
  );
}

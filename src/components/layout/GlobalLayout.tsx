import React, { useEffect, useState } from 'react';
import { useUserStore } from '@/store';
import { history, useLocation } from 'ice';
import { BasicLayout, BasicLayoutProps, LayoutProvider } from '@ali/mc-uikit';
import type { MenuProps } from 'antd';
import { Button, Drawer, Layout, Menu, Select, Space, Tooltip } from 'antd';
import { useAuth } from '@ice/plugin-auth/runtime';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  QuestionCircleFilled,
  RobotOutlined,
} from '@ant-design/icons';
import { ENV_GROUP_HOSTS, getConfig, getEnv, isOrangePro, PRO_ENV_GROUP_HOSTS } from '@/utils/env';
import { handleEnvChange } from '@/utils/envSwitch';
import { NEW_PATH_2_OLD_PATH } from '@/constants/router';
import { ChatWidget, NotificationList } from '@/components/messaging';
import { Breadcrumb } from 'antd/lib';
import { MenuDataItem } from '@ant-design/pro-layout';
import { BreadcrumbItem, getBreadcrumbItems } from '@/utils/breadcrumb';
import theme from '@ali/mtl-themes/Cloud';

// 将 CSS 样式直接嵌入到组件中
const GlobalLayoutStyles = `
  .global-menu-drawer .mc-drawer-body {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .ant-breadcrumb a {
    text-decoration: none !important;
    color: inherit !important;
  }
`;

const { Content } = Layout;

interface GlobalLayoutProps extends BasicLayoutProps {
  children: React.ReactNode;
  menuItems: MenuDataItem[];
}

// 将 MenuDataItem 转换为 antd Menu 的 ItemType
const convertToAntdMenuItems = (items: MenuDataItem[]): NonNullable<MenuProps['items']> => {
  return items
    .map(item => {
      if (!item.key) {
        return null;
      }

      if (item.children) {
        return {
          key: item.key,
          label: item.label,
          icon: item.icon,
          source: item.source,
          children: convertToAntdMenuItems(item.children),
        };
      }
      return {
        key: item.key,
        label: item.label,
        icon: item.icon,
        source: item.source,
      };
    })
    .filter((item): item is NonNullable<typeof item> => item !== null);
};

export default function GlobalLayout(props: GlobalLayoutProps) {
  const { children, menuItems, ...rest } = props;

  const [menuCollapsed, setMenuCollapsed] = useState(true);
  const [chatOpen, setChatOpen] = useState(false);
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([]);
  const currentUser = useUserStore((state: { currentUser: any }) => state.currentUser);
  const [auth] = useAuth();
  const currentConfig = getConfig();

  const location = useLocation();
  const currentPath = location.pathname.split('/').filter(Boolean).join('/');

  // 处理面包屑
  useEffect(() => {
    const updateBreadcrumb = async () => {
      const items = await getBreadcrumbItems(currentPath);
      setBreadcrumbItems(items);
    };

    updateBreadcrumb();
  }, [currentPath]);

  return (
    <>
      <style>{GlobalLayoutStyles}</style>
      <LayoutProvider themeConfig={theme}>
        <BasicLayout
          platformKey="orange"
          noPadding
          headerLogoAddonBefore={
            <>
              <Button onClick={() => setMenuCollapsed(!menuCollapsed)}>
                {menuCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              </Button>
            </>
          }
          headerBreadcrumbAddonAfter={
            <Breadcrumb>
              {breadcrumbItems.map(item => (
                <Breadcrumb.Item key={item.key}>
                  {item.path ? (
                    <a onClick={() => history?.push(item.path!)}>{item.label}</a>
                  ) : (
                    item.label
                  )}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          }
          headerUserAddonBefore={
            <Space>
              <Button
                onClick={() => {
                  if (isOrangePro()) {
                    const config = ENV_GROUP_HOSTS.find(item => item.key === getEnv());
                    window.location.href = `https://${config.host}`;
                    return;
                  }

                  const { hash } = window.location;
                  const newPath = hash.split('?')[0].replace(/^\/+|\/+$/g, '');
                  const urlParams = new URLSearchParams(hash.split('?')[1] || '');
                  if (newPath === '#/workspace/namespace/detail') {
                    window.location.href = `/#/namespace/namespace/detail/${urlParams.get(
                      'namespaceId',
                    )}`;
                    return;
                  } else if (newPath === '#/workspace/version/detail') {
                    window.location.href = `/#/namespace/version/detail/${urlParams.get(
                      'namespaceId',
                    )}-${urlParams.get('version')}`;
                    return;
                  }
                  const oldPath = NEW_PATH_2_OLD_PATH[newPath];
                  window.location.href = oldPath ? `/${oldPath}` : '/';
                }}
              >
                回到旧版
              </Button>

              <Select value={currentConfig.key} onChange={handleEnvChange}>
                {ENV_GROUP_HOSTS.map(env => (
                  <Select.Option key={env.key} value={env.key}>
                    {env.label}
                  </Select.Option>
                ))}
              </Select>

              <NotificationList />

              <Tooltip title="点击查看文档">
                <QuestionCircleFilled
                  key="QuestionCircleFilled"
                  onClick={() => {
                    const documentUrl = isOrangePro()
                      ? 'https://alidocs.dingtalk.com/i/nodes/ZgpG2NdyVXRmQ0jgCqmp776Z8MwvDqPk'
                      : 'https://alidocs.dingtalk.com/i/spaces/nb9XJJ8QMa1w8XyA/overview';
                    window.open(documentUrl);
                  }}
                />
              </Tooltip>
            </Space>
          }
          user={currentUser}
          floatButton={{
            items: [
              {
                key: 'chat',
                icon: <RobotOutlined />,
                onClick: () => setChatOpen(!chatOpen),
              },
            ],
          }}
          userMenu={{
            items: auth?.admin
              ? [
                  {
                    key: 'admin',
                    label: '管理员面板',
                  },
                ]
              : [],
            onClick: ({ key }) => {
              history?.push(`/${key}`);
            },
          }}
          {...rest}
        >
          <Layout>
            <Drawer
              className="global-menu-drawer"
              closable={false}
              placement="left"
              onClose={() => setMenuCollapsed(true)}
              open={!menuCollapsed}
              width={250}
            >
              <Menu
                style={{
                  height: '100%',
                  padding: 0,
                }}
                selectedKeys={[location.pathname]}
                mode="inline"
                defaultOpenKeys={menuItems
                  .map(item => item.key)
                  .filter((key): key is string => !!key)}
                items={[
                  {
                    type: 'group',
                    label: '新版配置',
                    children: convertToAntdMenuItems(
                      menuItems.filter(item => item.source === 'pro'),
                    ),
                  },
                  {
                    type: 'group',
                    label: '基础配置',
                    children: convertToAntdMenuItems(
                      menuItems.filter(item => item.source !== 'pro'),
                    ),
                  },
                ]}
                onSelect={(info: any) => {
                  // info.key 是菜单 key，info.item.props.source 是 source 字段
                  const isPro = info.item?.props?.source === 'pro';
                  const envKey = currentConfig.key;
                  const targetHost = isPro
                    ? PRO_ENV_GROUP_HOSTS.find(e => e.key === envKey)?.host
                    : ENV_GROUP_HOSTS.find(e => e.key === envKey)?.host;
                  const currentHost = window.location.hostname;
                  const targetPath = `/${isPro ? '' : 'v5'}#${info.key}`;
                  if (currentHost !== targetHost && targetHost) {
                    window.location.href = `https://${targetHost}${targetPath}`;
                  } else {
                    history?.push(info.key);
                  }
                }}
              />
            </Drawer>
            <Content>
              {children}
              <div style={{ display: chatOpen ? 'block' : 'none' }}>
                <ChatWidget
                  user={{
                    userid: currentUser.empId,
                    avatar: currentUser.avatar,
                  }}
                  setChatOpen={setChatOpen}
                />
              </div>
            </Content>
          </Layout>
        </BasicLayout>
      </LayoutProvider>
    </>
  );
}

const SCENE_TYPES = [
  {
    value: 'i18n',
    label: '多语言',
  },
];

export const SCENE_VALUES = {
  i18n: [
    {
      value: 'zh_CN',
      label: '中文 (简体)',
    },
    {
      value: 'en_US',
      label: '英语 (美式)',
    },
    {
      value: 'th_TH',
      label: '泰语 (泰国)',
    },
    {
      value: 'vi_VN',
      label: '越南文 (越南)',
    },
    {
      value: 'ms_MY',
      label: '马来文 (马来西亚)',
    },
    {
      value: 'ru_RU',
      label: '俄文 (俄罗斯)',
    },
    {
      value: 'km_KH',
      label: '高棉语 (柬埔寨)',
    },
    {
      value: 'fr_FR',
      label: '法文 (法国)',
    },
    {
      value: 'de_DE',
      label: '德文 (德国)',
    },
    {
      value: 'it_IT',
      label: '意大利文 (意大利)',
    },
    {
      value: 'es_ES',
      label: '西班牙文 (西班牙)',
    },
    {
      value: 'pt_PT',
      label: '葡萄牙文 (葡萄牙)',
    },
    {
      value: 'pt_BR',
      label: '葡萄牙文 (巴西)',
    },
    {
      value: 'zh_TW',
      label: '中文 (繁体)',
    },
    {
      value: 'ja_JP',
      label: '日文 (日本)',
    },
    {
      value: 'ko_KR',
      label: '韩语 (韩国)',
    },
    {
      value: 'hi_IN',
      label: '印地文 (印度)',
    },
  ],
};

const mapping = { Default: '默认' };
SCENE_TYPES.forEach(type => {
  const values = SCENE_VALUES[type.value];
  if (values) {
    values.forEach(value => {
      mapping[value.value] = value.label;
    });
  }
});

export const SCENE_MAP = mapping;

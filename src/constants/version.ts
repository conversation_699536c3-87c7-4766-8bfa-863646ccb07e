export const VERSION_STATUS_CONFIG: Record<number, { text: string; color: string }> = {
  0: {
    color: 'default',
    text: '新创建',
  },
  2: {
    color: 'processing',
    text: '灰度已验证',
  },
  4: {
    color: 'warning',
    text: '已审核',
  },
  50: {
    color: 'processing',
    text: '灰度中',
  },
  200: {
    color: 'success',
    text: '发布成功',
  },
  250: {
    color: 'error',
    text: '取消发布',
  },
  255: {
    color: 'default',
    text: '已删除',
  },
};

export const SOURCE_MAP: Record<number, string> = {
  0: 'ORANGE 控制台',
  1: '预案',
  2: 'API',
};

export const YES = 'y';
export const NO = 'n';

export const AVAILABLEL_MAP = {
  [YES]: '是',
  [NO]: '否',
};

export const EMERGENT_MAP = {
  [YES]: '立即生效',
  [NO]: '普通生效',
};

// 灰度比例单位
export const GRAY_RATIO_UNIT = 100000;

export const VERSION_STATUS_SUCCESS = 200;
export const VERSION_STATUS_CANCEL = 250;
export const VERSION_STATUS_DELETE = 255;
export const VERSION_STATUS_CREATED = 0;
export const VERSION_STATUS_GRAY_DONE = 2;
export const VERSION_STATUS_REVIEWD = 4;

export const FINISHED_VERSION_STATUS_LIST = [VERSION_STATUS_SUCCESS, VERSION_STATUS_CANCEL, VERSION_STATUS_DELETE];
export const OFFLINE_REASONS = [{
  value: '1',
  label: '常规版本只能有一个生效，旧的被覆盖',
}, {
  value: '2',
  label: '策略版本发布超过限制版本，删除最旧的策略版本',
}, {
  value: '3',
  label: '发布出现相同策略,覆盖旧版本',
}, {
  value: '4',
  label: '发布[*]指定要覆盖的版本',
}, {
  value: '5',
  label: '删除配置项下线所有生效的发布单',
}, {
  value: '6',
  label: '回滚引发的版本覆盖',
}];

export const OFFLINE_REASON_MAP = OFFLINE_REASONS.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
}, {});

export const VERSION_STAGE_APPLY = 'APPLY';
export const VERSION_STAGE_BETA = 'BETA';
export const VERSION_STAGE_GRAY = 'GRAY';
export const VERSION_STAGE_RATIO_GRAY = 'RATIO_GRAY';
export const VERSION_STAGE_REVIEW = 'REVIEW';
export const VERSION_STAGE_SKIP = 'SKIP';
export const VERSION_STAGE_CLOSE = 'CLOSE';
export const VERSION_STAGE_PUBLISH = 'PUBLISH';

export const GRAY_TYPE_MASS_CIRCLE = 'massCircle';
export const GRAY_CIRCLE_DIMENSIONS = ['app_ver', 'os_ver', 'm_brand', 'm_model'];

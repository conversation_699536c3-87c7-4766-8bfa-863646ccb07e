export const TYPE_CONFIG: Record<number, { label: string; desc: string; color: string }> = {
  1: {
    label: 'STANDARD',
    desc: 'KV配置',
    color: 'volcano',
  },
  2: {
    label: 'SWITCH',
    desc: '开关配置',
    color: 'geekblue',
  },
  3: {
    label: 'CUSTOM',
    desc: '自定义配置',
    color: 'cyan',
  },
  4: {
    label: 'MERGED',
    desc: '合并配置',
    color: 'purple',
  },
};


export const LOAD_LEVEL_CONFIG: Record<number, { label: string; desc: string; color: string }> = {
  10: {
    label: 'HighInit',
    desc: '启动加载',
    color: 'orange',
  },
  0: {
    label: 'Default',
    desc: '默认加载',
    color: 'blue',
  },
  5: {
    label: 'HighLazy',
    desc: '闲时加载',
    color: 'green',
  },
};

export const NS_DEFAULT_VALUE = '# 以下是一个配置例子,content是一个标准的.properties格式文件,UTF-8编码\n' +
  '# 这是一行注释\n' +
  'key1=value1\n' +
  '\n' +
  '# 值为空的配置\n' +
  'key2=\n' +
  '\n' +
  '\n' +
  '# 有值的配置\n' +
  'key3=value3\n' +
  '\n' +
  '# 中文的key 中文的value\n' +
  '中文key=中文value\n' +
  '\n' +
  '# 值是一个json串的例子\n' +
  'key10={"a":"a1","中文":"中文"}';

export const NS_DEFAULT_CUSTOM_VALUE = '{' +
  '"label":"这是个自定义类型配置示例，可以清除后重新配置。可以是JSON类型，也可以是其它任意文本。"' +
  '}';

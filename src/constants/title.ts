import { MenuDataItem } from '@ant-design/pro-layout';
import { asideMenuConfig } from '@/menuConfig';

// 从菜单配置中提取路径到标题的映射
const extractPathToTitle = (menuItems: MenuDataItem[], parentPath = ''): Record<string, string> => {
  const pathToTitle: Record<string, string> = {};

  menuItems.forEach(item => {
    const currentPath = parentPath + item.key;
    if (item.label) {
      pathToTitle[currentPath] = item.label as string;
    }

    if (item.children) {
      Object.assign(pathToTitle, extractPathToTitle(item.children, currentPath));
    }
  });

  return pathToTitle;
};

// 基础路径到标题的映射
export const BASE_PATH_TO_TITLE: Record<string, string> = {
  '/': '首页',
  '/workspace': '工作台',
  ...extractPathToTitle(asideMenuConfig),
};

// 获取页面标题的函数
export const getPageTitle = (pathname: string): string => {
  // 移除查询参数
  const path = pathname.split('?')[0];

  // 尝试直接匹配完整路径
  if (BASE_PATH_TO_TITLE[path]) {
    return `Orange-${BASE_PATH_TO_TITLE[path]}`;
  }

  // 尝试匹配最长的前缀路径
  const matchedPath = Object.keys(BASE_PATH_TO_TITLE)
    .filter(key => path.startsWith(key))
    .sort((a, b) => b.length - a.length)[0];

  return `Orange-${matchedPath ? BASE_PATH_TO_TITLE[matchedPath] : '配置发布平台'}`;
};

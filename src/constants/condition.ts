// Condition key types and constants
export type ConditionKey = 'app_ver' | 'did_hash' | 'os_ver' | 'm_brand' | 'm_model';

export const CONDITION_KEYS: Record<ConditionKey, string> = {
  app_ver: '客户端版本',
  did_hash: '设备哈希',
  os_ver: '系统版本',
  m_brand: '厂商',
  m_model: '机型',
};

// Operator types and constants
export type Operator = '==' | '!=' | '~=' | '!~' | '>' | '>=' | '<' | '<=';

export const OPERATORS: Record<Operator, string> = {
  '>=': '大于等于',
  '>': '大于',
  '==': '等于',
  '<=': '小于等于',
  '<': '小于',
  '!=': '不等于',
  '~=': '开始于',
  '!~': '不开始于',
};

// Available operators for each condition key
export const KEY_OPERATORS: Record<ConditionKey, Operator[]> = {
  app_ver: ['>=', '>', '==', '<=', '<', '!=', '~=', '!~'],
  os_ver: ['>=', '>', '==', '<=', '<', '!=', '~=', '!~'],
  did_hash: ['=='],
  m_brand: ['==', '!=', '~=', '!~'],
  m_model: ['==', '!=', '~=', '!~'],
};

// Validation patterns
export const VALIDATION_PATTERNS = {
  version: /^\d+(\.\d+)*$/,
  didHash: /^\d+_\d+-\d+$/,
};

export const DEFAULT_CONDITION_COLOR = 'default';

// Color definitions with both name and value
export const COLORS: Record<string, { name: string; value: string }> = {
  blue: {
    name: '蓝色',
    value: '#108ee9',
  },
  green: {
    name: '绿色',
    value: '#52c41a',
  },
  orange: {
    name: '橙色',
    value: '#fa8c16',
  },
  red: {
    name: '红色',
    value: '#ff4d4f',
  },
  purple: {
    name: '紫色',
    value: '#722ed1',
  },
  magenta: {
    name: '品红',
    value: '#ee4d64',
  },
  volcano: {
    name: '火山色',
    value: '#fa541c',
  },
  gold: {
    name: '金色',
    value: '#faad14',
  },
  lime: {
    name: '亮绿色',
    value: '#a0d911',
  },
  cyan: {
    name: '青色',
    value: '#13c2c2',
  },
  geekblue: {
    name: '技术蓝',
    value: '#2f54eb',
  },
};

export const COLOR2KEY = Object.fromEntries(
  Object.entries(COLORS).map(([key, value]) => [value.value, key]),
);

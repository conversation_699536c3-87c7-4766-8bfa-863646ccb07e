export const parameterChangeType2Color = {
  CREATE: 'green',
  UPDATE: 'yellow',
  DELETE: 'red',
};

export const parameterChangeType2Text = {
  CREATE: '新增',
  UPDATE: '编辑',
  DELETE: '删除',
};

export const PARAMETER_TYPES = [
  {
    value: 'BOOLEAN',
    label: '布尔值',
  },
  {
    value: 'STRING',
    label: '字符串',
  },
  {
    value: 'JSON',
    label: 'JSON',
  },
  {
    value: 'DOUBLE',
    label: '浮点数',
  },
  {
    value: 'LONG',
    label: '长整型',
  },
] as const;

export const PARAMETER_TYPE_MAP = PARAMETER_TYPES.reduce((acc, type) => {
  acc[type.value] = type;
  return acc;
}, {} as Record<string, (typeof PARAMETER_TYPES)[number]>);

export const DEFAULT_PARAMETER = {
  parameterKey: '',
  valueType: 'BOOLEAN',
  conditionsOrder: '',
  description: '',
  parameterConditions: [
    {
      conditionName: '默认',
      conditionId: '*',
      changeType: 'CREATE',
      value: '',
    },
  ],
  changeType: 'CREATE',
};

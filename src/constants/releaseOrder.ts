export const STAGE_TYPE_CONFIG = (
  status: API.ReleaseOrderStatus,
  currentStageType: API.StageType,
): string => {
  if (status === 'CANCELED') {
    return '已取消';
  } else if (status === 'RELEASED') {
    return '已发布';
  }

  switch (currentStageType) {
    case 'APPLY_RELEASE':
      return '申请发布';
    case 'SMALLFLOW_GRAY':
      return '小流量灰度';
    case 'VERIFY':
      return '人工验证';
    case 'RATIO_GRAY':
      return '百分比发布';
    case 'RELEASE':
      return '正式发布';
    default:
      return '进行中';
  }
};

export const ORDER_STATUS_2_TAG_COLOR = (status: API.ReleaseOrderStatus): string => {
  switch (status) {
    case 'RELEASED':
      return 'green';
    case 'CANCELED':
      return 'gray';
    default:
      return 'blue';
  }
};

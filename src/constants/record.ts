export const RECORD_TYPE_APPLY = 1;
export const RECORD_TYPE_BETA = 2;
export const RECORD_TYPE_GRAY = 3;
export const RECORD_TYPE_REVIEW = 4;
export const RECORD_TYPE_SKIP = 5;
export const RECORD_TYPE_RATIO_GRAY = 6;

export const RECORD_STATUS_SUCCESS = 200;
export const RECORD_STATUS_FAIL = 400;

export const RECORD_STATUS_INIT = 11;
export const RECORD_STATUS_CHECKING = 21;
export const RECORD_STATUS_PREPARING = 21;
export const RECORD_STATUS_PROCESSING = 41;
export const RECORD_STATUS_CANCEL = 300;

export const RECORD_STATUS_CONFIG = {
  [RECORD_STATUS_SUCCESS]: '成功',
  [RECORD_STATUS_FAIL]: '失败',
  [RECORD_STATUS_INIT]: '初始化',
  [RECORD_STATUS_CHECKING]: '检测中',
  [RECORD_STATUS_PREPARING]: '准备中',
  [RECORD_STATUS_PROCESSING]: '流程中',
  [RECORD_STATUS_CANCEL]: '取消',
};
export const RECORD_RESULT_SHOWS = [
  {
    value: 'changefreeStatus',
    label: 'CF状态',
  },
  {
    value: 'massStatus',
    label: '任务状态',
  },
  {
    value: 'massTaskId',
    label: '任务',
  },
  {
    value: 'ackCnt',
    label: 'ACK设备数',
  },
  {
    value: 'grayIndex',
    label: '序号',
  },
  {
    value: 'approvalStatus',
    label: '审批状态',
  },
  {
    value: 'message',
    label: '提示',
  },
  {
    value: 'skipped',
    label: '跳过',
  },
];

export const RECORD_PARAMS_SHOWS = [
  {
    value: 'emergent',
    label: '立即生效',
  },
  {
    value: 'deviceCnt',
    label: '目标设备数',
  },
  {
    value: 'attachedGrayStrategy',
    label: '追加策略',
  },
  {
    value: 'specifiedGrayStrategy',
    label: '指定策略',
  },
  {
    value: 'skipStage',
    label: '跳过流程',
  },
];

export const RECORD_TYPE_CONFIG = {
  [RECORD_TYPE_APPLY]: '申请发布',
  [RECORD_TYPE_BETA]: 'BETA灰度',
  [RECORD_TYPE_GRAY]: '定量灰度',
  [RECORD_TYPE_RATIO_GRAY]: '百分比灰度',
  [RECORD_TYPE_REVIEW]: '审核',
  [RECORD_TYPE_SKIP]: '跳过',
  10: '发布',
  20: '下线',
  30: '关闭',
};

export const CHECK_STATUS_HOLD = 'HOLD';
export const CHECK_STATUS_PASS = 'PASSED';
export const CHECK_STATUS_CONFIG = {
  [CHECK_STATUS_HOLD]: '阻断',
  [CHECK_STATUS_PASS]: '通过',
  EXCEPTION: '异常',
  UNKNOWN: '未知',
  INIT: '初始化',
  CHECKING: '检测中',
};

export const SKIP_PROCESS_BETA = 'BETA';
export const SKIP_PROCESS_APPLY = 'APPLY';
export const SKIP_PROCESS_GRAY = 'GRAY';
export const SKIP_PROCESS_CHECK = 'CHECK';
export const SKIP_PROCESS_ALL = 'ALL';

export const SKIP_PROCESS_ARR = [
  {
    value: SKIP_PROCESS_BETA,
    label: 'BETA',
  },
  {
    value: SKIP_PROCESS_APPLY,
    label: '审批',
    params: ['noApproval', 'emergent'],
  },
  {
    value: SKIP_PROCESS_GRAY,
    label: '灰度',
    params: ['noApproval'],
  },
  {
    value: SKIP_PROCESS_CHECK,
    label: '阻断卡口',
    params: ['noApproval'],
  },
  {
    value: SKIP_PROCESS_ALL,
    label: '全部',
  },
];

export const STEPS = ['初始', 'BETA灰度', '申请发布', '灰度发布', '验证通过', '版本发布'];
export const PROGRESS_NAMES = [
  '待 BETA 灰度',
  '待申请发布',
  '待灰度',
  '待验证',
  '待通过',
  '发布成功',
];

export const CHECK_PROVIDER_WOP = 'WOP';
export const CHECK_PROVIDER_CF_STEP = 'CF_STEP';

export const CHECK_PROVIDER_MAP = {
  [CHECK_PROVIDER_WOP]: 'WOP',
  [CHECK_PROVIDER_CF_STEP]: 'Changefree 管控',
};

export const SKIP_SUPPORT_NONE = 'NONE';
export const SKIP_SUPPORT_ANY = 'ANY';
export const SKIP_SUPPORT_BPMS = 'BPMS';
export const SKIP_SUPPORT_MAP = {
  [SKIP_SUPPORT_NONE]: '不可跳过',
  [SKIP_SUPPORT_ANY]: '可跳过',
  [SKIP_SUPPORT_BPMS]: '需审批',
};

export const TEXT_NS_GRAY_RATIO_LIST = [0, 1, 10, 50, 100, 500, 1000, 5000, 10000, 20000, 50000];
export const SWITCH_NS_GRAY_RATIO_LIST = [0, 1000, 5000, 10000, 20000, 50000];
export const TAO_TE_TEXT_NS_GRAY_RATIO_LIST = [
  0, 50, 100, 500, 1000, 5000, 10000, 20000, 50000, 60000, 70000, 80000, 90000,
];
export const GRAY_RATIO_UNIT = 100000;
export const MIN_DEVICE_CNT = GRAY_RATIO_UNIT;
/**
 * 百分比灰度发布第一批最少观测时间（分钟）
 */
export const MIN_GRAY_MINUTES = 30;

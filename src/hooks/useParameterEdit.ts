import { useEffect, useState } from 'react';
import { Form } from 'antd';
import type {
  Parameter,
  ParameterCondition,
  ParameterConditionChangeType,
} from '@/types/parameter';
import { DEFAULT_PARAMETER } from '@/constants/parameter';

interface UseParameterEditProps {
  editingParameter?: Parameter | null;
  visible: boolean;
}

export const useParameterEdit = ({ editingParameter, visible }: UseParameterEditProps) => {
  const [form] = Form.useForm();
  const [parameter, setParameter] = useState<Parameter>(DEFAULT_PARAMETER);
  const [jsonEditorVisible, setJsonEditorVisible] = useState(false);
  const [currentJsonValue, setCurrentJsonValue] = useState('');
  const [currentJSONEditingConditionName, setCurrentJSONEditingConditionName] = useState<string>();
  const [conditionSelectorVisible, setConditionSelectorVisible] = useState(false);
  const [searchCondition, setSearchCondition] = useState('');
  const [newConditionModalVisible, setNewConditionModalVisible] = useState(false);

  useEffect(() => {
    if (editingParameter) {
      setParameter(editingParameter);
      form.resetFields();
      form.setFieldsValue({
        parameterKey: editingParameter.parameterKey,
        valueType: editingParameter.valueType,
        description: editingParameter.description,
      });
      editingParameter.parameterConditions?.forEach(c => {
        form.setFieldsValue({
          [c.conditionName!]: c.value,
        });
      });
    } else {
      setParameter(DEFAULT_PARAMETER);
      form.resetFields();
      form.setFieldsValue({
        ...DEFAULT_PARAMETER,
        默认: '',
      });
    }
  }, [editingParameter, form, visible]);

  const handleConditionValueChange = (conditionName: string, value: string) => {
    setParameter({
      ...parameter,
      parameterConditions: parameter.parameterConditions.map(c => {
        if (c.conditionName === conditionName) {
          return {
            ...c,
            value,
            changeType: c.changeType || ('UPDATE' as ParameterConditionChangeType),
          };
        }
        return c;
      }),
    });
  };

  const handleConditionDeleted = (condition: ParameterCondition) => {
    if (condition.changeType !== 'CREATE') {
      setParameter({
        ...parameter,
        parameterConditions: parameter.parameterConditions.map(c => {
          if (c.conditionId === condition.conditionId) {
            return {
              ...c,
              changeType: 'DELETE' as ParameterConditionChangeType,
            };
          }
          return c;
        }),
      });
    } else {
      setParameter({
        ...parameter,
        parameterConditions: parameter.parameterConditions.filter(
          c => c.conditionName !== condition.conditionName,
        ),
      });
    }
  };

  const handleRestoreCondition = (conditionId: string) => {
    setParameter({
      ...parameter,
      parameterConditions: parameter.parameterConditions.map(c => {
        if (c.conditionId === conditionId) {
          return {
            ...c,
            changeType: undefined,
          };
        }
        return c;
      }),
    });
  };

  const handleJsonChange = (value: string) => {
    setCurrentJsonValue(value);
    form.setFieldsValue({
      [currentJSONEditingConditionName!]: value,
    });
    setParameter({
      ...parameter,
      parameterConditions: parameter.parameterConditions.map(c => {
        if (c.conditionName === currentJSONEditingConditionName) {
          return {
            ...c,
            value,
          };
        }
        return c;
      }),
    });
  };

  return {
    form,
    parameter,
    setParameter,
    jsonEditorVisible,
    setJsonEditorVisible,
    currentJsonValue,
    setCurrentJsonValue,
    currentJSONEditingConditionName,
    setCurrentJSONEditingConditionName,
    conditionSelectorVisible,
    setConditionSelectorVisible,
    searchCondition,
    setSearchCondition,
    newConditionModalVisible,
    setNewConditionModalVisible,
    handleConditionValueChange,
    handleConditionDeleted,
    handleRestoreCondition,
    handleJsonChange,
  };
};

'use client';

import { useCallback, useState } from 'react';
import { message, Modal } from 'antd';
import type { PaginationState, Parameter, ParameterCondition } from '@/types/parameter';
import moment from 'moment';
import services from '@/services/orange-be';
import ParameterDirectUpdateDTO = API.ParameterDirectUpdateDTO;

const { ParameterController } = services;
const { confirm } = Modal;

interface UseNamespaceParametersProps {
  namespaceId: string;
}

export function useNamespaceParameters({ namespaceId }: UseNamespaceParametersProps) {
  // 状态管理
  const [parameterListData, setParameterListData] = useState<Parameter[]>([]);
  const [originalParameters, setOriginalParameters] = useState<Parameter[]>([]);
  const [selectedParameterKeys, setSelectedParameterKeys] = useState<string[]>([]);
  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [strongAlertMessage, setStrongAlertMessage] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationState>({
    current: 1,
    size: 10,
    total: 0,
  });

  // 本地未发布修改，按 parameterKey 聚合，分页切换时用于合并
  const [modifiedParameterMap, setModifiedParameterMap] = useState<Record<string, Parameter>>({});
  // 本地新增参数（后端尚不存在），需要在任意分页中展示在顶部
  const [createdParameters, setCreatedParameters] = useState<Parameter[]>([]);

  // 获取参数列表
  const fetchParameters = useCallback(
    async (page = 1, pageSize = 10, searchKey?: string) => {
      try {
        setLoading(true);
        const result = await ParameterController.query({
          page,
          size: pageSize,
          namespaceId,
          keyword: searchKey,
        });

        // 转换API返回的数据为组件所需格式
        const parameters = result.data.map(param => ({
          ...param,
          latestPublishTime: moment(param.gmtModified).format('YYYY/MM/DD HH:mm:ss'),
          latestPublishUser: param.modifier,
          changeType: null,
          parameterConditions: (param.parameterConditions || []).map(i => ({
            ...i,
            latestPublishTime: moment(i.gmtModified).format('YYYY/MM/DD HH:mm:ss'),
            latestPublishUser: i.modifier,
            changeType: null,
          })),
        }));

        // 用本地修改覆盖服务端同 key 的数据
        const mergedServerParameters = parameters.map(serverParam => {
          const key = serverParam.parameterKey!;
          const modified = modifiedParameterMap[key];
          if (!modified) return serverParam;
          return {
            ...serverParam,
            ...modified,
            // 条件以本地为准（若未修改则回退到服务端）
            parameterConditions: modified.parameterConditions || serverParam.parameterConditions,
          } as Parameter;
        });

        // 顶部合并本地新增参数
        const fullList = [...createdParameters, ...mergedServerParameters];

        setParameterListData(fullList);
        // 记录当前页的“原始”服务端数据，供对比使用
        setOriginalParameters(JSON.parse(JSON.stringify(parameters)));
        setPagination({
          current: result.current!,
          size: result.size!,
          total: result.total!,
        });
      } catch (error) {
        const errMsg = (error as Error)?.message || '未知错误';
        message.error(`获取参数列表失败: ${errMsg}`);
      } finally {
        setLoading(false);
      }
    },
    [namespaceId, createdParameters, modifiedParameterMap],
  );

  const updateParameter = useCallback(
    async (parameter: ParameterDirectUpdateDTO) => {
      try {
        await ParameterController.update(parameter);
        message.success('更新参数成功');
        // 找到被更新参数的 key，用于清理本地修改缓存
        const updated = parameterListData.find(p => p.parameterId === parameter.parameterId);
        // 刷新当前页
        await fetchParameters(pagination.current, pagination.size, searchText);
        // 清除本地该参数的修改缓存
        if (updated?.parameterKey) {
          setModifiedParameterMap(prev => {
            const next = { ...prev };
            delete next[updated.parameterKey!];
            return next;
          });
        }
        // 刷新后清空选中项
        setSelectedParameterKeys([]);
      } catch (error) {
        const errMsg = (error as Error)?.message || '未知错误';
        message.error(`更新参数失败: ${errMsg}`);
      }
    },
    [fetchParameters, pagination.current, pagination.size, searchText, parameterListData],
  );

  // 搜索参数
  const handleSearch = useCallback(() => {
    // 重置到第 1 页
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
    fetchParameters(1, pagination.size, searchText);
  }, [fetchParameters, pagination.size, searchText]);

  // 分页变化
  const handlePageChange = useCallback(
    (page: number, pageSize?: number) => {
      // 先更新分页状态，随后拉取对应页数据
      setPagination(prev => ({
        ...prev,
        current: page,
        size: pageSize ?? prev.size,
      }));
      fetchParameters(page, pageSize, searchText);
    },
    [fetchParameters, searchText],
  );

  // 展开/折叠行
  const toggleExpand = useCallback((key: string) => {
    setExpandedRows(prev => {
      if (prev.includes(key)) {
        return prev.filter(k => k !== key);
      } else {
        return [...prev, key];
      }
    });
  }, []);

  // 展开/折叠所有行
  const toggleExpandAll = useCallback(() => {
    setExpandedRows(prev => {
      const parametersWithConditions = parameterListData.filter(
        p => p.parameterConditions && p.parameterConditions.length > 0,
      );
      if (prev.length === parametersWithConditions.length) {
        return [];
      } else {
        return parametersWithConditions.map(p => p.parameterKey!);
      }
    });
  }, [parameterListData]);

  // 保存参数
  const handleSaveParameter = useCallback(
    (parameter: Parameter, mode: 'create' | 'edit') => {
      const latestModifyTime = moment().format('YYYY/MM/DD HH:mm:ss');

      // 新增参数
      if (!parameter.parameterId) {
        const originalParameter = parameterListData.find(
          p => p.parameterKey === parameter.parameterKey,
        );

        if (originalParameter && mode === 'create') {
          message.error('已存在同名参数，请勿重复添加');
          return;
        }
        if (!originalParameter && mode === 'edit') {
          message.error('参数不存在，无法更新');
          return;
        }

        if (!originalParameter) {
          const newParameter: Parameter = {
            ...parameter,
            changeType: 'CREATE',
            latestModifyTime,
            parameterConditions: parameter.parameterConditions!.map(i => ({
              ...i,
              changeType: 'CREATE',
              latestModifyTime,
            })),
          };

          setParameterListData(prev => [newParameter, ...prev]);
          setCreatedParameters(prev => [newParameter, ...prev]);
          setSelectedParameterKeys(prev => [newParameter.parameterKey!, ...prev]);
          setStrongAlertMessage('参数添加成功，请点击发布变更以保存');
          return;
        }
      }

      // 更新现有参数
      const originalParameter = parameterListData.find(
        p =>
          p.parameterId === parameter.parameterId ||
          (!p.parameterId && !parameter.parameterId && p.parameterKey === parameter.parameterKey),
      )!;

      // 检查是否有变更
      const originalParameterConditions = originalParameter.parameterConditions!.map(i => ({
        conditionName: i.conditionName,
        changeType: i.changeType,
        value: i.value,
      }));
      const newParameterConditions = parameter.parameterConditions!.map(i => ({
        conditionName: i.conditionName,
        changeType: i.changeType,
        value: i.value,
      }));

      if (
        originalParameter.description === parameter.description &&
        JSON.stringify(originalParameterConditions) === JSON.stringify(newParameterConditions)
      ) {
        message.info('参数未发生变更');
        return;
      }

      let updatedParam: Parameter | undefined;
      const isNewlyCreated = !originalParameter.parameterId ||
        createdParameters.some(p => p.parameterKey === parameter.parameterKey && !p.parameterId);
      setParameterListData(prev =>
        prev.map(p => {
          if (p.parameterKey === parameter.parameterKey) {
            updatedParam = {
              ...p,
              latestModifyTime,
              description: parameter.description,
              changeType: isNewlyCreated ? 'CREATE' : (parameter.changeType || 'UPDATE'),
              parameterConditions: parameter.parameterConditions!.map((c: ParameterCondition) => ({
                ...c,
                latestModifyTime: c.changeType ? latestModifyTime : undefined,
              })),
            } as Parameter;
            return updatedParam;
          }
          return p;
        }),
      );

      // 记录本地修改缓存
      if (updatedParam) {
        const key = updatedParam.parameterKey!;
        if (isNewlyCreated) {
          // 同步更新本地新增列表中的该参数
          setCreatedParameters(prev => prev.map(p => (p.parameterKey === key ? (updatedParam as Parameter) : p)));
        } else {
          setModifiedParameterMap(prev => ({
            ...prev,
            [key]: updatedParam!,
          }));
        }
      }

      if (!selectedParameterKeys.includes(parameter.parameterKey!)) {
        setSelectedParameterKeys(prev => [...prev, parameter.parameterKey!]);
      }
      setStrongAlertMessage('参数修改成功，请点击发布变更以保存');
    },
    [parameterListData, selectedParameterKeys, createdParameters],
  );

  // 删除参数
  const handleDeleteParameter = useCallback(
    (parameterKey: string) => {
      confirm({
        title: '确认删除',
        content: '确定要删除这个参数吗？删除参数需要发布后才真实生效',
        onOk() {
          const isNewlyCreated = createdParameters.some(p => p.parameterKey === parameterKey && !p.parameterId);
          if (isNewlyCreated) {
            // 直接移除本地新增的参数，不生成删除记录
            setParameterListData(prev => prev.filter(p => p.parameterKey !== parameterKey));
            setCreatedParameters(prev => prev.filter(p => p.parameterKey !== parameterKey));
            setModifiedParameterMap(prev => {
              const next = { ...prev };
              delete next[parameterKey];
              return next;
            });
            if (selectedParameterKeys.includes(parameterKey)) {
              setSelectedParameterKeys(prev => prev.filter(k => k !== parameterKey));
            }
            setStrongAlertMessage('已移除新增参数');
            return;
          }
          let deletedParam: Parameter | undefined;
          setParameterListData(prev =>
            prev
              .filter(p => !!p.parameterId || p.parameterKey !== parameterKey)
              .map(p => {
                if (p.parameterKey === parameterKey) {
                  deletedParam = {
                    ...p,
                    changeType: 'DELETE',
                    latestModifyTime: moment().format('YYYY/MM/DD HH:mm:ss'),
                  } as Parameter;
                  return deletedParam as Parameter;
                }
                return p;
              }),
          );
          // 若是本地新建的参数，被删除时同步移出本地新增列表
          setCreatedParameters(prev => prev.filter(p => p.parameterKey !== parameterKey));
          // 记录/覆盖本地修改为删除
          if (deletedParam) {
            const key = deletedParam.parameterKey!;
            setModifiedParameterMap(prev => ({
              ...prev,
              [key]: deletedParam!,
            }));
          } else {
            // 页面不在当前页时，至少写入一个占位删除记录
            setModifiedParameterMap(prev => ({
              ...prev,
              [parameterKey]: {
                parameterKey: parameterKey,
                changeType: 'DELETE',
                latestModifyTime: moment().format('YYYY/MM/DD HH:mm:ss'),
              } as unknown as Parameter,
            }));
          }
          if (!selectedParameterKeys.includes(parameterKey)) {
            setSelectedParameterKeys(prev => [...prev, parameterKey]);
          }
          setStrongAlertMessage('参数已标记为删除，请点击发布变更以保存');
        },
      });
    },
    [selectedParameterKeys, createdParameters],
  );

  // 选择参数
  const handleSelectParameter = useCallback((key: string, checked: boolean) => {
    if (checked) {
      setSelectedParameterKeys(prev => [...prev, key]);
    } else {
      setSelectedParameterKeys(prev => prev.filter(k => k !== key));
    }
  }, []);

  // 全选/取消全选
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const changedKeys = parameterListData.filter(p => !!p.changeType).map(p => p.parameterKey!);
        setSelectedParameterKeys(changedKeys);
      } else {
        setSelectedParameterKeys([]);
      }
    },
    [parameterListData],
  );

  return {
    // 状态
    parameterListData,
    originalParameters,
    selectedParameterKeys,
    expandedRows,
    loading,
    searchText,
    pagination,
    strongAlertMessage,

    // 方法
    setSearchText,
    setStrongAlertMessage,
    fetchParameters,
    updateParameter,
    handleSearch,
    handlePageChange,
    toggleExpand,
    toggleExpandAll,
    handleSaveParameter,
    handleDeleteParameter,
    handleSelectParameter,
    handleSelectAll,
  };
}

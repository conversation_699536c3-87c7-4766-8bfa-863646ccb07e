'use client';

import { useState, useEffect, useMemo } from 'react';
import { getConditionMapByIds } from '@/services/orange-be/ConditionController';

interface UseConditionNamesProps {
  conditionIds?: string[];
}

interface ConditionNamesResult {
  conditionNames: Record<string, string>;
  loading: boolean;
  error: string | null;
}

export const useConditionNames = ({ conditionIds }: UseConditionNamesProps): ConditionNamesResult => {
  const [conditionNames, setConditionNames] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 将 conditionIds 数组转换为逗号分隔的字符串
  const conditionIdsString = useMemo(() => {
    if (!conditionIds || conditionIds.length === 0) {
      return undefined;
    }
    return conditionIds.join(',');
  }, [conditionIds]);

  useEffect(() => {
    const fetchConditionNames = async () => {
      if (!conditionIdsString) {
        setConditionNames({});
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const response = await getConditionMapByIds({
          conditionIds: conditionIdsString,
        });

        if (response.success && response.data) {
          // 将返回的数据转换为 conditionId -> conditionName 的映射
          const namesMap: Record<string, string> = {};
          Object.entries(response.data).forEach(([conditionId, conditionData]: [string, any]) => {
            namesMap[conditionId] = conditionData?.name || conditionId;
          });
          setConditionNames(namesMap);
        } else {
          setError(response.message || '获取条件名称失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取条件名称时发生错误');
      } finally {
        setLoading(false);
      }
    };

    fetchConditionNames();
  }, [conditionIdsString]);

  return {
    conditionNames,
    loading,
    error,
  };
}; 
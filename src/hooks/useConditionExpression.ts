'use client';

import { useState, useEffect } from 'react';
import { getByConditionId } from '@/services/orange-be/ConditionController';

interface UseConditionExpressionProps {
  conditionId: string;
}

interface UseConditionExpressionResult {
  expression: any;
  loading: boolean;
  error: string | null;
}

export const useConditionExpression = ({ conditionId }: UseConditionExpressionProps): UseConditionExpressionResult => {
  const [expression, setExpression] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchExpression = async () => {
      if (!conditionId || conditionId === '*') {
        setExpression(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const response = await getByConditionId({ conditionId });
        if (response.success && response.data) {
          setExpression(response.data.expression);
        } else {
          setError(response.message || '获取条件表达式失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取条件表达式时发生错误');
      } finally {
        setLoading(false);
      }
    };

    fetchExpression();
  }, [conditionId]);

  return {
    expression,
    loading,
    error,
  };
}; 
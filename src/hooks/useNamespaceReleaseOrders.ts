'use client';

import { useCallback, useState } from 'react';
import { message } from 'antd';
import { history } from 'ice';
import services from '@/services/orange-be';
import type { Parameter } from '@/types/parameter';
import ParameterChangeDTO = API.ParameterChangeDTO;
import ConditionChangeDTO = API.ConditionChangeDTO;
import ConditionDTO = API.ConditionDTO;

const { ReleaseOrderController } = services;

interface UseNamespaceReleaseOrdersProps {
  namespaceId: string;
}

export function useNamespaceReleaseOrders({ namespaceId }: UseNamespaceReleaseOrdersProps) {
  const [releaseOrders, setReleaseOrders] = useState<API.ReleaseOrderDTO[]>([]);
  const [statusCounts, setStatusCounts] = useState<Record<API.ReleaseOrderStatus, number>>(
    {} as Record<API.ReleaseOrderStatus, number>,
  );
  const [activeStatusTab, setActiveStatusTab] = useState<'all' | 'inProgress' | 'completed'>('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取发布单列表
  const fetchReleaseOrders = useCallback(async () => {
    try {
      const ordersResponse = await ReleaseOrderController.query({
        namespaceId,
        statuses:
          activeStatusTab === 'all'
            ? undefined
            : activeStatusTab === 'completed'
            ? ['RELEASED', 'CANCELED']
            : ['IN_PROGRESS'],
        page: pagination.current,
        size: pagination.pageSize,
      });
      setReleaseOrders(ordersResponse.data);
      setPagination(prev => ({
        ...prev,
        total: ordersResponse.total || 0,
      }));
    } catch (error) {
      console.error('Failed to fetch release orders:', error);
      message.error(`获取发布单列表失败: ${error.message}`);
    }
  }, [namespaceId, activeStatusTab, pagination.current, pagination.pageSize]);

  // 获取状态统计
  const fetchStatusCounts = useCallback(async () => {
    try {
      const countsResponse = await ReleaseOrderController.countByStatus({ namespaceId });
      setStatusCounts(countsResponse.data);
    } catch (error) {
      console.error('Failed to fetch status counts:', error);
    }
  }, [namespaceId]);

  // 处理分页变化
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  }, []);

  // 处理状态Tab变化
  const handleStatusTabChange = useCallback((tab: 'all' | 'inProgress' | 'completed') => {
    setActiveStatusTab(tab);
    setPagination(prev => ({
      ...prev,
      current: 1,
    })); // 重置到第一页
  }, []);

  // 计算发布单状态数量
  const getReleaseOrderCounts = useCallback(() => {
    const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
    const completed = (statusCounts['RELEASED'] || 0) + (statusCounts['CANCELED'] || 0);
    const inProgress = total - completed;

    return {
      total,
      completed,
      inProgress,
    };
  }, [statusCounts]);

  // 创建发布单
  const handleCreateReleaseOrder = useCallback(
    async (description: string, selectedParameters: Parameter[], newConditions: ConditionDTO[]) => {
      try {
        const parameterChanges: ParameterChangeDTO[] = selectedParameters.map(p => ({
          parameterId: p.parameterId,
          parameterKey: p.parameterKey,
          description: p.description,
          changeType: p.changeType,
          previousReleaseVersion: p.releaseVersion || undefined,
          valueType: p.valueType,
          conditionNamesOrder: p
            .parameterConditions!.filter(i => i.conditionId !== '*' && i.changeType !== 'DELETE')
            .map(c => c.conditionName)
            .filter((name): name is string => name !== undefined),
          parameterConditionChanges: p
            .parameterConditions!.filter(i => i.changeType)
            .map(c => ({
              conditionId: c.conditionId,
              conditionName: c.conditionName,
              changeType: c.changeType,
              value: c.value,
              previousReleaseVersion: c.releaseVersion,
            })),
        }));

        const conditionChanges: ConditionChangeDTO[] = newConditions.map(c => ({
          name: c.name,
          expression: c.expression,
          changeType: 'CREATE',
          color: c.color,
        }));

        const { data: releaseVersion } = await ReleaseOrderController.create({
          namespaceId,
          bizId: namespaceId,
          description,
          parameterChanges,
          conditionChanges,
          releaseType: parameterChanges?.length ? 'IMPACT_RELEASE' : 'NO_IMPACT_RELEASE',
          bizType: 'NAMESPACE',
        });

        history?.push(`/workspace/switch/release-orders/${releaseVersion}`);
      } catch (error) {
        console.error('创建发布单失败:', error);
        message.error(`创建发布单失败: ${error.message}`);
        throw error;
      }
    },
    [namespaceId],
  );

  return {
    // 状态
    releaseOrders,
    statusCounts,
    activeStatusTab,
    pagination,

    // 方法
    fetchReleaseOrders,
    fetchStatusCounts,
    handlePageChange,
    handleStatusTabChange,
    getReleaseOrderCounts,
    handleCreateReleaseOrder,
  };
}

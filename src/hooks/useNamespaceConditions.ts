'use client';

import { useCallback, useState } from 'react';
import { message } from 'antd';
import services from '@/services/orange-be';
import ConditionDTO = API.ConditionDTO;
import ConditionDirectUpdateDTO = API.ConditionDirectUpdateDTO;
import { update } from 'lodash';

const { ConditionController } = services;

interface UseNamespaceConditionsProps {
    namespaceId: string;
}

export function useNamespaceConditions({ namespaceId }: UseNamespaceConditionsProps) {
    const [conditions, setConditions] = useState<API.ConditionDetailDTO[]>([]);
    const [newConditions, setNewConditions] = useState<ConditionDTO[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    // 获取条件列表
    const fetchConditions = useCallback(async (name?: string) => {
        try {
            setLoading(true);
            const response = await ConditionController.query({
                namespaceId,
                name,
                page: pagination.current,
                size: pagination.pageSize,
            });
            setConditions(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total || 0,
            }));
        } catch (error) {
            console.error('Failed to fetch conditions:', error);
            message.error(`获取条件列表失败: ${error.message}`);
        } finally {
            setLoading(false);
        }
    }, [namespaceId, pagination.current, pagination.pageSize]);

    // 获取所有条件（用于参数条件选择）
    const fetchAllConditions = useCallback(async () => {
        try {
            const { data } = await ConditionController.getAll({ namespaceId });
            setConditions(data);
        } catch (error) {
            message.error(`获取条件列表失败: ${error.message}`);
        }
    }, [namespaceId]);

    // 添加新条件
    const handleAddNewCondition = useCallback((condition: ConditionDTO) => {
        // 检查条件是否已存在
        setNewConditions((prev) => {
            const exists = prev.some((c) => c.name === condition.name);
            if (!exists) {
                return [...prev, condition];
            }
            return prev;
        });
        setConditions((prev) => {
            const exists = prev.some((c) => c.name === condition.name);
            if (!exists) {
                return [...prev, condition];
            }
            return prev;
        });
    }, []);

    const updateCondition = useCallback(async (condition: ConditionDirectUpdateDTO) => {
        try {
            await ConditionController.update(condition);
            // 更新本地状态
            setConditions(prev => prev.map(c => 
                c.conditionId === condition.conditionId 
                    ? { ...c, ...condition }
                    : c
            ));
            message.success('更新条件成功');
        } catch (error) {
            message.error(`更新条件失败: ${error.message}`);
        }
    }, []);

    // 处理条件搜索
    const handleSearch = useCallback(() => {
        fetchConditions(searchText);
    }, [fetchConditions, searchText]);

    // 处理分页变化
    const handlePageChange = useCallback((page: number, pageSize: number) => {
        setPagination(prev => ({
            ...prev,
            current: page,
            pageSize,
        }));
        fetchConditions(searchText);
    }, [fetchConditions, searchText]);

    return {
        // 状态
        conditions,
        newConditions,
        loading,
        searchText,
        pagination,

        // 方法
        setSearchText,
        fetchConditions,
        fetchAllConditions,
        handleAddNewCondition,
        handleSearch,
        handlePageChange,
        updateCondition,
    };
}
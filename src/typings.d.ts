/// <reference types="@ice/app/types" />

// SVG 模块声明 - 支持 SVG 作为 React 组件
declare module '*.svg' {
  import React from 'react';
  const SVG: React.FC<
    React.SVGProps<SVGSVGElement> & { width?: number | string; height?: number | string }
  >;
  export default SVG;
}

// SVG 作为 URL 的声明（如果需要）
declare module '*.svg?url' {
  const content: string;
  export default content;
}

declare module 'lodash-es';

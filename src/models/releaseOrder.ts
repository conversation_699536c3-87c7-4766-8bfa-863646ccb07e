import { create } from 'zustand';
import services from '@/services/orange-be';
import { hasNsEditPermission, hasNsTestPermission } from '@/utils/permission';
import ReleaseOrderDetailDTO = API.ReleaseOrderDetailDTO;

const { ReleaseOrderController, NamespaceController } = services;

export interface ReleaseOrderModel extends ReleaseOrderDetailDTO {
  changes?: API.ReleaseOrderChangesDTO[];
}

interface ReleaseOrderState {
  namespace: API.NamespaceDTO;
  releaseOrder: ReleaseOrderModel;

  hasNsEditPermission: boolean;
  hasNsTestPermission: boolean;

  mainWorkflowStage: string;
  subWorkflowStage: string;

  templateId: number;

  // BETA 扫码记录是否存在的标志
  hasBetaScanLogs: boolean;
  // 触发小流量灰度工作流刷新的标志
  smallFlowGrayRefreshTrigger: number;

  fetchAndUpdateReleaseOrder: (releaseVersion: string) => Promise<void>;
  fetchNamespace: () => Promise<void>;
  updateWorkflowStage: ({
    mainWorkflowStage,
    subWorkflowStage,
  }: {
    mainWorkflowStage?: string;
    subWorkflowStage?: string;
  }) => void;
  update: (payload: Partial<ReleaseOrderState>) => void;
  // 检查 BETA 扫码记录状态
  checkBetaScanLogsStatus: (releaseVersion: string) => Promise<void>;
  // 触发小流量灰度工作流刷新
  triggerSmallFlowGrayRefresh: () => void;
}

const useReleaseOrderStore = create<ReleaseOrderState>((set, get) => ({
  namespace: {} as API.NamespaceDTO,
  releaseOrder: {},
  hasNsEditPermission: false,
  hasNsTestPermission: false,
  templateId: 0,
  mainWorkflowStage: 'beta',
  subWorkflowStage: 'beta',
  hasBetaScanLogs: false,
  smallFlowGrayRefreshTrigger: 0,
  update: (payload: Partial<ReleaseOrderState>) => set({ ...payload }),
  fetchNamespace: async () => {
    const namespaceId = get().releaseOrder.namespaceId!;
    if (!namespaceId) {
      return;
    }
    const { data } = await NamespaceController.getByNamespaceId({ namespaceId });
    set({ 
      namespace: data,
      hasNsEditPermission: hasNsEditPermission(data),
      hasNsTestPermission: hasNsTestPermission(data),
     });
  },
  updateWorkflowStage: ({
    mainWorkflowStage,
    subWorkflowStage,
  }: {
    mainWorkflowStage: string;
    subWorkflowStage: string;
  }) => {
    set(state => ({
      mainWorkflowStage: mainWorkflowStage || state.mainWorkflowStage,
      subWorkflowStage: subWorkflowStage || state.subWorkflowStage,
    }));
    // 当进入 beta 阶段时，检查申请发布记录状态和 BETA 扫码记录状态
    if (mainWorkflowStage === 'beta') {
      const releaseVersion = get().releaseOrder.releaseVersion!;
      get().checkBetaScanLogsStatus(releaseVersion);
    }
  },
  fetchAndUpdateReleaseOrder: async (releaseVersion: string) => {
    const { data } = await ReleaseOrderController.getDetail({ releaseVersion });

    set({ releaseOrder: data });
  },
  // 检查 BETA 扫码记录状态
  checkBetaScanLogsStatus: async (releaseVersion: string) => {
    if (!releaseVersion) return;

    try {
      const { data } = await ReleaseOrderController.getScanBetaLogs({
        releaseVersion,
        type: 'FORCE_BETA' as any,
        page: 1,
        size: 1, // 只需要检查是否有记录，获取1条即可
      });
      const hasLogs = (data || []).length > 0;
      set({ hasBetaScanLogs: hasLogs });
    } catch (error) {
      console.error('Failed to check beta scan logs status:', error);
      set({ hasBetaScanLogs: false });
    }
  },
  // 触发小流量灰度工作流刷新
  triggerSmallFlowGrayRefresh: () => {
    set(state => ({
      smallFlowGrayRefreshTrigger: state.smallFlowGrayRefreshTrigger + 1,
    }));
  },
}));

export default useReleaseOrderStore;

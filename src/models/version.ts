import { create } from 'zustand';
import { VersionDetail } from '@/types/version';
import { getTigaTaskDetail, getVersionDeviceCnt, queryVersionDetail } from '@/services/version';
import { hasNsEditPermission, hasNsTestPermission } from '@/utils/permission';

export interface VersionModel extends VersionDetail {
  versionDeviceCnt: number;
  hasEditPermission: boolean;
  hasTestPermission: boolean;
  tigaTaskDetail: any;
}

interface VersionState extends VersionModel {
  fetchAndUpdateVersion: (namespaceId: string, version: string) => Promise<void>;
  fetchAndUpdateVersionDeviceCnt: (namespaceId: string, version: string) => Promise<void>;
  fetchAndUpdateTigaTaskDetail: (namespaceId: string, version: string) => Promise<void>;
}

// @ts-ignore
const useVersionStore = create<VersionState>(set => ({
  fetchAndUpdateVersion: async (namespaceId: string, version: string) => {
    const res = await queryVersionDetail(namespaceId, version);
    const hasEditPermission = hasNsEditPermission(res.namespaceBO);
    const hasTestPermission = hasNsTestPermission(res.namespaceBO);
    set({
      ...res,
      hasEditPermission,
      hasTestPermission,
    });
  },
  fetchAndUpdateVersionDeviceCnt: async (namespaceId: string, version: string) => {
    let versionDeviceCnt = 0;
    try {
      versionDeviceCnt = await getVersionDeviceCnt(namespaceId, version);
    } catch (e) {
      // 静默失败，避免频繁报错干扰
    }
    set({ versionDeviceCnt });
  },
  fetchAndUpdateTigaTaskDetail: async (namespaceId: string, version: string) => {
    const tigaTaskDetail = await getTigaTaskDetail(namespaceId, version);
    set({ tigaTaskDetail });
  },
}));

export default useVersionStore;

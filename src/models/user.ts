import { User } from '@/types/user';
import { create } from 'zustand';

interface UserState {
  currentUser: User;
  updateCurrentUser: (payload: Partial<User>) => void;
}

const useUserStore = create<UserState>(set => ({
  currentUser: {} as User,
  updateCurrentUser: (payload: Partial<User>) =>
    set({
      currentUser: {
        ...useUserStore.getState().currentUser,
        ...payload,
      },
    }),
}));

export default useUserStore;

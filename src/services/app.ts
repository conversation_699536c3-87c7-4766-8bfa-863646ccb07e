import { request } from 'ice';
import { AppBO } from '@/types/namespace';
import { BusinessError } from '@/types/error';

export async function queryAppList() {
  const res = await request({
    url: '/api/search/queryAppList.json',
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getAppDetail(appKey: string): Promise<{ appBO: AppBO }> {
  const res = await request({
    url: `/api/setting/queryAppDetail.json?appKey=${appKey}`,
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

import { request } from 'ice';
import { BusinessError } from '@/types/error';

interface BootNamespaceQuery {
  appKey?: string;
  namespaceName?: string;
  pageSize?: number;
  pageNo?: number;
}

export async function getBootNamespaceList(query: BootNamespaceQuery) {
  const res = await request({
    url: '/api/namespace/bootNamespaceList.json',
    method: 'get',
    params: query,
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function enableBootNamespace(
  appKey: string,
  name: string,
  expireTime?: number,
) {
  const params: any = {
    appKey,
    name,
  };
  if (expireTime) {
    params.expireTime = expireTime;
  }
  const res = await request({
    url: '/api/namespace/enableBootNamespace.json',
    method: 'get',
    params,
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function renewalBootNamespace(
  appKey: string,
  name: string,
  expireTime?: number,
) {
  const params: any = {
    appKey,
    name,
  };
  if (expireTime) {
    params.expireTime = expireTime;
  }
  const res = await request({
    url: '/api/namespace/renewalBootNamespace.json',
    method: 'get',
    params,
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}


import { request } from 'ice';
import qs from 'qs';
import { BusinessError } from '@/types/error';
import { KnockoutVersion } from '@/types/version';

export async function pushToActiveDevices(namespaceId: string, version: string) {
  const res = await request({
    url: '/api/version/pushToActiveDevices.json?runningMode=fg&runningMode=bg',
    method: 'get',
    params: {
      namespaceId,
      version,
    },
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function createVersion(
  namespaceId: string,
  content: string,
  scenesContents: {
    [key: string]: string;
  } | null,
  strategy: string | null,
  deleteVersions?: string[] | null,
  orders?: string | null,
) {
  const res = await request({
    url: '/api/version/createVersion.json',
    method: 'post',
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
    data: qs.stringify({
      namespaceId,
      content,
      scenesContents: scenesContents ? JSON.stringify(scenesContents) : null,
      strategy,
      overwriteVersions: deleteVersions ? deleteVersions.join(',') : null,
      orders,
    }),
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function queryKnockoutVersions(
  namespaceId: string,
  strategy: string | null,
  deleteVersions?: string[] | null,
): Promise<KnockoutVersion[]> {
  const res = await request({
    url: '/api/version/queryKnockoutVersions.json',
    method: 'get',
    params: {
      namespaceId,
      strategy,
      overwriteVersions: deleteVersions ? deleteVersions.join(',') : null,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function queryVersionList(params) {
  const res = await request({
    url: '/api/version/queryVersionList.json',
    method: 'get',
    params,
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function queryVersionDetail(namespaceId: string, version: string) {
  const res = await request({
    url: '/api/version/queryVersionDetail.json',
    method: 'get',
    params: {
      namespaceId,
      version,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getVersionDeviceCnt(namespaceId: string, version: string) {
  const res = await request({
    url: '/api/version/getVersionDeviceCnt.json',
    method: 'get',
    params: {
      namespaceId,
      version,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getCheckHoldResult(
  namespaceId: string,
  version: string,
  publishStage: string,
  recordId: number | null,
) {
  const res = await request({
    url: '/api/version/getCheckHoldResult.json',
    method: 'get',
    params: {
      namespaceId,
      version,
      publishStage,
      recordId,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function versionStage(
  namespaceId: string,
  version: string,
  stageName: string,
  extParams?: any,
) {
  const res = await request({
    url: '/api/version/versionStage.json',
    method: 'get',
    params: {
      version,
      namespaceId,
      stageName,
      ...extParams,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getLatestGrayRecord(namespaceId: string, version: string) {
  const res = await request({
    url: '/api/version/getLatestGrayRecord.json',
    method: 'get',
    params: {
      namespaceId,
      version,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function recordOperate(
  namespaceId: string,
  version: string,
  recordId: number,
  type: number,
  oper: string,
) {
  const res = await request({
    url: '/api/version/recordOperate.json',
    method: 'get',
    params: {
      namespaceId,
      version,
      recordId,
      type,
      oper,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getMassPushResult(
  namespaceId: string,
  version: string,
  recordId: number,
  massTaskId: string,
) {
  const res = await request({
    url: '/api/version/getMassPushResult.json',
    method: 'get',
    params: {
      namespaceId,
      version,
      recordId,
      massTaskId,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getTigaTaskDetail(namespaceId: string, version: string) {
  const res = await request({
    url: '/api/version/getTigaTaskDetail.json',
    method: 'get',
    params: {
      namespaceId,
      version,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getTigaSuggestTemplateList(
  namespaceId: string,
  version: string,
  keywords?: string,
) {
  const res = await request({
    url: '/api/version/GetTigaSuggestTemplateList.json',
    method: 'get',
    params: {
      namespaceId,
      version,
      ...(keywords && { keywords }),
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function createTigaTask(namespaceId: string, version: string, templateId: number) {
  const res = await request({
    url: '/api/version/createTigaTask.json',
    method: 'post',
    params: {
      namespaceId,
      version,
      templateId,
    },
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

import { request } from 'ice';
import { Business } from '@/types/service';
import { MtlModule } from '@/types/namespace';
import { BusinessError } from '@/types/error';
import { isOrangePro } from '@/utils/env';

import services from '@/services/orange-be';

const { <PERSON>r<PERSON>ontroller, FuzzySearchController } = services;

export async function queryUserByEmpIds({
  empIds,
  batchSize = 200,
}: {
  empIds: string[];
  batchSize?: number;
}) {
  const uniqueEmpIds = Array.from(new Set(empIds)).filter(
    empId => empId && (/^\d+$/.test(empId) || /^WB\d+$/.test(empId.toUpperCase())),
  );
  const results = {};

  for (let i = 0; i < uniqueEmpIds.length; i += batchSize) {
    const batch = uniqueEmpIds.slice(i, i + batchSize);

    if (isOrangePro()) {
      const res = await UserController.query({ workNoList: batch.join(',') });
      if (!res.success) throw new Error(res.message);
      Object.assign(results, res.data || {});
    } else {
      const res = await request({
        url: '/api/search/queryUserByEmpIds.json',
        method: 'get',
        params: { empIds: batch.join(',') },
      });

      if (!res.success) throw new Error(res.message);
      Object.assign(results, res.model || {});
    }
  }

  return results;
}

export async function queryModuleByModuleIds(
  moduleIds: string[],
): Promise<Record<string, MtlModule>> {
  if (!moduleIds?.length) {
    return {};
  }

  const res = await request({
    url: '/api/search/queryModuleByModuleIds.json',
    method: 'get',
    params: { moduleIds: Array.from(new Set(moduleIds)).join(',') },
  });
  if (!res.success) throw new Error(res.message);
  return res.model;
}

export async function fuzzySearchModule(
  appKey: string,
  keyword: string,
  pageNum = 1,
  pageSize = 10,
) {
  if (isOrangePro()) {
    const res = await FuzzySearchController.modules({
      appKey,
      keyword,
      page: pageNum,
      size: pageSize,
    });
    if (!res.success) throw new Error(res.message);
    return res.data;
  }

  const res = await request({
    url: `/api/search/fuzzySearchModule.json?appKey=${appKey}&keyword=${keyword}&pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model?.content;
}

export async function queryBusinessList(params: {
  name?: string;
  pageNo?: number;
  pageSize?: number;
}): Promise<{
  total: number;
  curPage: number;
  pageSize: number;
  content: Business[];
}> {
  const res = await request({
    url: '/api/business/queryBusinessList.json',
    method: 'get',
    params,
  });
  if (!res.success) throw new Error(res.message);
  return res.model;
}

export async function fuzzySearchUsers({
  keyword,
  pageNum = 1,
  pageSize = 10,
}: {
  keyword: string;
  pageNum?: number;
  pageSize?: number;
}) {
  if (isOrangePro()) {
    const res = await FuzzySearchController.users({
      keyword,
    });
    if (!res.success) throw new Error(res.message);
    return res.data;
  }

  const res = await request({
    url: `/api/search/fuzzySearchUser.json?keyword=${keyword}&pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model?.content;
}

export async function queryMessages(params: {
  appCode: string;
  sessionId: string;
  pageNo: number;
  pageSize: number;
}) {
  // todo: fixme
  return [];
  const response: any = await fetch('https://aistudio.alibaba-inc.com/api/aiapp/run/findLogs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      accept: 'application/json',
      'X-AK': 'fc3b6bfad18a1f3b5ecfac8aa3c1248f',
    },
    body: JSON.stringify({ ...params }),
  });
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }
  const data = await response.json();
  if (!data.success) {
    throw new Error(data.errorMessage || '请求消息列表失败');
  }
  const chatList: any[] = [];
  data.data?.list?.[0]?.messageList?.forEach(item => {
    chatList.push({
      content: item.question,
      createAt: item.runLog.startAt,
      id: `${item.messageId}-2`,
      role: 'user',
      updateAt: item.runLog.endAt,
    });
    chatList.push({
      content: item.answer,
      createAt: item.runLog.startAt,
      id: `${item.messageId}-1`,
      role: 'assistant',
      updateAt: item.runLog.endAt,
    });
  });
  return chatList;
}

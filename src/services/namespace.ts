import { request } from 'ice';
import { BusinessError } from '@/types/error';

export async function getAppNamespaceList(appKey: string) {
  const res = await request({
    url: `/api/namespace/appNamespaceList.json?appKey=${appKey}`,
    method: 'get',
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getNamespaceDetail(namespaceId: string) {
  const res = await request({
    url: `/api/namespace/getNamespaceDetail.json?namespaceId=${namespaceId}`,
    method: 'get',
    headers: {
      'Cache-Control': 'no-cache',
    },
  });

  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function getNamespaceListForMe() {
  const res = await request({
    url: '/api/namespace/namespaceListForMe.json',
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function permissionApply(namespaceId: string, permissionRole: string) {
  const res = await request({
    url: `/api/namespace/permissionApply.json?objectKey=${namespaceId}&permissionRole=${permissionRole}`,
    method: 'get',
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function updateNamespace(params: any) {
  const res = await request({
    url: '/api/namespace/updateNamespace.json',
    method: 'post',
    params,
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

export async function addNamespace(params: any) {
  const res = await request({
    url: '/api/namespace/addNamespace.json',
    method: 'post',
    params,
  });
  if (!res.success) throw new BusinessError(res.errorCode, res.errorMsg);
  return res.model;
}

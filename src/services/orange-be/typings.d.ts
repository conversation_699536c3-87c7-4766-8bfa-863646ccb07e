/* eslint-disable */

declare namespace API {
  interface ConditionExpressionDTO {
    key?: string;
    operator?: string;
    value?: string;
    children?: ConditionExpressionDTO[];
  }

  type ConditionStatus = 'INIT' | 'ONLINE' | 'INVALID';

  type VersionStatus = 'INIT' | 'RELEASED' | 'OUTDATED' | 'CANCELED';

  type ChangeType = 'CREATE' | 'UPDATE' | 'DELETE';

  interface ParameterConditionDTO {
    /** 参数ID */
    parameterId?: string;
    /** 参数名 */
    parameterKey?: string;
    /** 条件ID */
    conditionId?: string;
    /** 条件名称 */
    conditionName?: string;
    /** 条件颜色 */
    conditionColor?: string;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    /** 参数条件值 */
    value?: string;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    status?: VersionStatus;
    changeType?: ChangeType;
  }

  type ChangeType1 = 'CREATE' | 'UPDATE' | 'DELETE';

  interface ConditionDetailDTO {
    /** 主键 */
    id?: number;
    /** 条件ID */
    conditionId?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 条件名称 */
    name?: string;
    /** 条件颜色 */
    color?: string;
    expression?: ConditionExpressionDTO;
    status?: ConditionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    /** 在使用该条件的参数列表 */
    parameterConditions?: ParameterConditionDTO[];
    changeType?: ChangeType1;
  }

  interface PaginationResult_ConditionDetailDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: ConditionDetailDTO[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface ConditionDirectUpdateDTO {
    conditionId?: string;
    name?: string;
    color?: string;
  }

  interface Result_void_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  interface ConditionDTO {
    /** 主键 */
    id?: number;
    /** 条件ID */
    conditionId?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 条件名称 */
    name?: string;
    /** 条件颜色 */
    color?: string;
    expression?: ConditionExpressionDTO;
    status?: ConditionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
  }

  interface Result_List_ConditionDTO__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: ConditionDTO[];
  }

  type OneApiResult_string_ = Record<string, any>;

  type NamespaceBizType = 'MODULE';

  interface NamespaceCreateDTO {
    /** 应用KEY */
    appKey?: string;
    bizType: NamespaceBizType;
    /** 命名空间类型对应的实体ID */
    bizId?: string;
    /** 负责人列表 */
    owners?: string[];
    /** 测试人员列表 */
    testers?: string[];
    /** 名称 */
    name?: string;
    /** 描述 */
    description?: string;
  }

  interface Result_string_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: string;
  }

  type NamespaceStatus = 'VALID' | 'DELETE';

  interface NamespaceDTO {
    /** 主键 */
    id?: number;
    /** 命名空间ID */
    namespaceId?: string;
    /** 应用KEY */
    appKey?: string;
    bizType?: NamespaceBizType;
    /** 命名空间类型对应的实体ID */
    bizId?: string;
    /** 命名空间类型对应的实体名称 */
    bizName?: string;
    /** 参数个数 */
    parameterCount?: number;
    /** 负责人列表 */
    owners?: string[];
    /** 测试人员列表 */
    testers?: string[];
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    status?: NamespaceStatus;
    /** 名称 */
    name?: string;
    /** 描述 */
    description?: string;
  }

  interface PaginationResult_NamespaceDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: NamespaceDTO[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface Result_NamespaceDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: NamespaceDTO;
  }

  interface NamespaceUpdateDTO {
    /** 命名空间ID */
    namespaceId?: string;
    /** 负责人列表 */
    owners?: string[];
    /** 测试人员列表 */
    testers?: string[];
    status?: NamespaceStatus;
    /** 描述 */
    description?: string;
  }

  type ParameterValueType = 'JSON' | 'STRING' | 'BOOLEAN' | 'DOUBLE' | 'LONG';

  type ParameterStatus = 'INIT' | 'ONLINE' | 'INVALID';

  type ReleaseOrderBizType = 'NAMESPACE' | 'EXPERIMENT';

  type ReleaseType = 'IMPACT_RELEASE' | 'NO_IMPACT_RELEASE' | 'ROLLBACK';

  type ReleaseOrderStatus = 'IN_PROGRESS' | 'RELEASED' | 'CANCELED';

  type StageType = 'APPLY_RELEASE' | 'SMALLFLOW_GRAY' | 'VERIFY' | 'RATIO_GRAY' | 'RELEASE';

  interface ReleaseOrderDTO {
    /** 主键 */
    id?: number;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    bizType?: ReleaseOrderBizType;
    /** 发布对象ID */
    bizId?: string;
    releaseType?: ReleaseType;
    /** 描述 */
    description?: string;
    status?: ReleaseOrderStatus;
    /** 发布百分比（十万分之一为单位） */
    grayRatio?: number;
    /** Tiga 灰度任务ID */
    tigaTaskId?: number;
    currentStageType?: StageType;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
  }

  interface ParameterDetailDTO {
    /** 主键 */
    id?: number;
    /** 参数ID */
    parameterId?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 参数键名 */
    parameterKey?: string;
    valueType?: ParameterValueType;
    /** 描述 */
    description?: string;
    status?: ParameterStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    /** 条件顺序 */
    conditionsOrder?: string;
    /** 最新发布版本号 */
    releaseVersion?: string;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    /** 参数条件值 */
    parameterConditions?: ParameterConditionDTO[];
    inPublishReleaseOrder?: ReleaseOrderDTO;
    changeType?: ChangeType1;
  }

  interface PaginationResult_ParameterDetailDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: ParameterDetailDTO[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface ParameterConditionChangeDTO {
    /** 参数键名(当本次引用的非新增参数时使用) */
    conditionId?: string;
    /** 条件名(当本次引用的为新增参数时使用) */
    conditionName?: string;
    /** 参数条件值 */
    value?: string;
    changeType?: ChangeType;
  }

  interface ParameterChangeDTO {
    /** 参数id */
    parameterId?: string;
    /** 参数键名 */
    parameterKey?: string;
    valueType?: ParameterValueType;
    /** 条件顺序(不包含默认值) */
    conditionNamesOrder?: string[];
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    changeType?: ChangeType;
    /** 描述 */
    description?: string;
    /** 本次发布单涉及的参数条件变更 */
    parameterConditionChanges?: ParameterConditionChangeDTO[];
  }

  interface ConditionChangeDTO {
    /** 条件id(修改和删除条件时使用) */
    conditionId?: string;
    /** 条件名称（新增条件时必填） */
    name?: string;
    changeType?: ChangeType;
    expression?: ConditionExpressionDTO;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
  }

  interface ReleaseOrderCreateDTO {
    /** 命名空间ID */
    namespaceId?: string;
    bizType: ReleaseOrderBizType;
    /** 发布对象ID */
    bizId?: string;
    releaseType: ReleaseType;
    /** 描述 */
    description?: string;
    /** 本次发布单涉及的参数变更 */
    parameterChanges?: ParameterChangeDTO[];
    /** 本次发布单涉及的条件变更 */
    conditionChanges?: ConditionChangeDTO[];
  }

  interface ReleaseOrderDTO1 {
    /** 主键 */
    id?: number;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    bizType?: ReleaseOrderBizType;
    /** 发布对象ID */
    bizId?: string;
    releaseType?: ReleaseType;
    /** 描述 */
    description?: string;
    status?: ReleaseOrderStatus;
    /** 发布百分比（十万分之一为单位） */
    grayRatio?: number;
    /** Tiga 灰度任务ID */
    tigaTaskId?: number;
    currentStageType?: StageType;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
  }

  interface PaginationResult_ReleaseOrderDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: ReleaseOrderDTO1[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  type OperationType =
    | 'APPLY_RELEASE'
    | 'SMALLFLOW_GRAY'
    | 'RATIO_GRAY'
    | 'START_VERIFY'
    | 'VERIFY_REPLY'
    | 'RELEASE'
    | 'CANCEL'
    | 'SKIP';

  type OperationType1 =
    | 'APPLY_RELEASE'
    | 'SMALLFLOW_GRAY'
    | 'RATIO_GRAY'
    | 'START_VERIFY'
    | 'VERIFY_REPLY'
    | 'RELEASE'
    | 'CANCEL'
    | 'SKIP';

  type OperationStatus = 'INIT' | 'SUCCESS' | 'FAILED';

  interface ReleaseOrderOperationDTO {
    id?: number;
    type?: OperationType1;
    /** 输入参数(JSON) */
    params?: string;
    /** 结果信息(JSON) */
    result?: string;
    status?: OperationStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
  }

  interface Result_List_ReleaseOrderOperationDTO__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: ReleaseOrderOperationDTO[];
  }

  interface ConditionDetailDTO1 {
    /** 主键 */
    id?: number;
    /** 条件ID */
    conditionId?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 条件名称 */
    name?: string;
    /** 条件颜色 */
    color?: string;
    expression?: ConditionExpressionDTO;
    status?: ConditionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    /** 在使用该条件的参数列表 */
    parameterConditions?: ParameterConditionDTO[];
    changeType?: ChangeType1;
  }

  interface Result_ConditionDetailDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: ConditionDetailDTO1;
  }

  type BriefTaskStatus = Record<string, any>;

  type StageType1 = 'APPLY_RELEASE' | 'SMALLFLOW_GRAY' | 'VERIFY' | 'RATIO_GRAY' | 'RELEASE';

  type StageStatus = 'INIT' | 'IN_PROGRESS' | 'SKIPPED' | 'SUCCESS' | 'FAILED' | 'CANCELED';

  interface ReleaseOrderStageDTO {
    type?: StageType1;
    status?: StageStatus;
  }

  interface ReleaseOrderDetailDTO {
    /** 主键 */
    id?: number;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    bizType?: ReleaseOrderBizType;
    /** 发布对象ID */
    bizId?: string;
    releaseType?: ReleaseType;
    /** 描述 */
    description?: string;
    status?: ReleaseOrderStatus;
    /** 发布百分比（十万分之一为单位） */
    grayRatio?: number;
    /** Tiga 灰度任务ID */
    tigaTaskId?: number;
    currentStageType?: StageType;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    namespaceName?: string;
    /** 本次发布单涉及变更的参数 */
    parameterKeys?: string[];
    /** 本次发布单涉及变更的条件 */
    conditionIds?: string[];
    /** WMCC 任务ID */
    agatewareTaskId?: string;
    agatewareTaskInfo?: BriefTaskStatus;
    /** 发布单阶段列表 */
    releaseOrderStages?: ReleaseOrderStageDTO[];
  }

  interface Result_ReleaseOrderDetailDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: ReleaseOrderDetailDTO;
  }

  interface NamespaceSnapshotParameterConditionSnapshot {
    /** 主键 */
    id?: number;
    /** 参数ID */
    parameterId?: string;
    /** 条件ID */
    conditionId?: string;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    /** 参数条件值 */
    value?: string;
    changeType?: ChangeType;
    status?: VersionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
  }

  interface NamespaceSnapshotParameterSnapshot {
    /** 主键 */
    id?: number;
    /** 参数ID */
    parameterId?: string;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 参数键名 */
    parameterKey?: string;
    valueType?: ParameterValueType;
    /** 条件顺序 */
    conditionsOrder?: string;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    changeType?: ChangeType;
    status?: VersionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    parameterConditions?: NamespaceSnapshotParameterConditionSnapshot[];
  }

  interface NamespaceSnapshotConditionSnapshot {
    /** 主键 */
    id?: number;
    /** 条件ID */
    conditionId?: string;
    /** 发布版本号 */
    releaseVersion?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 条件表达式 */
    expression?: string;
    /** 修改前发布版本号 */
    previousReleaseVersion?: string;
    changeType?: ChangeType;
    status?: VersionStatus;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    name?: string;
    color?: string;
  }

  interface NamespaceSnapshot {
    releaseVersion?: string;
    namespaceVersion?: string;
    gmtCreate?: string;
    parameterSnapshots?: NamespaceSnapshotParameterSnapshot[];
    conditionSnapshots?: NamespaceSnapshotConditionSnapshot[];
  }

  interface ReleaseOrderChangesDTO {
    /** 本次发布单涉及的参数变更 */
    parameterChanges?: ParameterDetailDTO[];
    /** 本次发布单涉及的条件变更 */
    conditionChanges?: ConditionDetailDTO[];
    previousNamespace?: NamespaceSnapshot;
  }

  interface Result_ReleaseOrderChangesDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: ReleaseOrderChangesDTO;
  }

  interface RatioGrayDTO {
    /** 灰度比例 */
    grayRatio?: number;
  }

  interface Result_Map_String_User__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  interface MtlModule {
    /** 模块名称 */
    name?: string;
    /** 模块ID */
    moduleId?: number;
    /** 模块标识 */
    identifier?: string;
    /** 模块描述 */
    description?: string;
    /** 模块类型 */
    moduleType?: string;
    /** 平台类型 */
    platformType?: string;
    /** 归属责任人 */
    owner?: string;
    /** 工程标准化 */
    hasStandard?: boolean;
    /** 是否集成到淘宝 */
    hasIntegrateToTB?: boolean;
    /** 是否动态化 */
    hasDynamic?: boolean;
    /** 数据时间 */
    ds?: string;
    /** 模块分层 */
    tags?: string[];
    /** 代码仓库地址 */
    codeLibraryAddress?: string;
    /** 单测是否通过 */
    hasLatestUtRecordPass?: boolean;
    /** 最新标准化版本 */
    latestStandardVersion?: string;
    /** 最新正式版本 */
    latestVersion?: string;
    /** 是否有单测记录 */
    hasUtRecord?: boolean;
    /** 最新正式版本commit */
    latestVersionCommitNumber?: string;
    /** 集成分支名 */
    integrationBranchName?: string;
    /** 活跃度 */
    activeLevel?: string;
    /** 包名 */
    bundleName?: string;
    /** DepKey */
    depKey?: string;
    /** 管理员 */
    admins?: string[];
    /** 开发人员 */
    developers?: string[];
    /** 测试人员 */
    testers?: string[];
    /** MTL地址 */
    mtlAddress?: string;
  }

  interface PaginationResult_MtlModule_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: MtlModule[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface User {
    /** bucId */
    id?: number;
    /** 工号 */
    empId?: string;
    /** 中文姓名 */
    name?: string;
    /** 邮箱 */
    emailAddr?: string;
    /** 花名 */
    nickNameCn?: string;
    /** 域账号 */
    emailPrefix?: string;
    /** 主管工号 */
    supervisorEmpId?: string;
    /** 离职状态 A：在职 I：离职 */
    hrStatus?: string;
  }

  interface Result_List_User__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: User[];
  }

  interface Result_Map_ReleaseOrderStatus_Long__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  type VerifyStatus = 'PASS' | 'REFUSE';

  interface VerifyReplyDTO {
    verifyStatus: VerifyStatus;
    /** 验证信息 */
    verifyMessage?: string;
  }

  type TaskType = 'APPROVE' | 'VERIFY' | 'RELEASE';

  type TaskStatus = 'PENDING' | 'COMPLETED' | 'CANCELED';

  type TaskBizType = 'RELEASE_ORDER' | 'NAMESPACE' | 'PARAMETER' | 'CONDITION' | 'EXPERIMENT';

  type TaskHandlerStatus = 'PENDING' | 'COMPLETED' | 'NO_NEED';

  interface TaskDetailDTO {
    /** 任务唯一ID */
    taskId?: string;
    type?: TaskType;
    status?: TaskStatus;
    /** 关联业务ID */
    bizId?: string;
    bizType?: TaskBizType;
    /** 任务描述 */
    description?: string;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 任务创建时间 */
    gmtCreate?: string;
    /** 任务修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    myHandlerStatus?: TaskHandlerStatus;
  }

  interface PaginationResult_TaskDetailDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: TaskDetailDTO[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface Result_Map_TaskHandlerStatus_Long__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  type NamespaceVersionChangeType =
    | 'NEW_IMPACT_RELEASE'
    | 'RATIO_GRAY'
    | 'FINISH_IMPACT_RELEASE'
    | 'CANCEL_RELEASE'
    | 'FINISH_NO_IMPACT_RELEASE'
    | 'DELETE_NAMESPACE'
    | 'NEW_EXPERIMENT'
    | 'CANCEL_EXPERIMENT';

  type Available = 'y' | 'n';

  interface NamespaceVersionDTO {
    /** 主键 */
    id?: number;
    /** 应用KEY */
    appKey?: string;
    /** 命名空间ID */
    namespaceId?: string;
    /** 命名空间正式版本号 */
    namespaceVersion?: string;
    /** 命名空间变更版本号 */
    namespaceChangeVersion?: string;
    changeType?: NamespaceVersionChangeType;
    /** 发起变更的发布单版本号 */
    releaseVersion?: string;
    isAvailable?: Available;
    /** 第一次应用该变更的索引版本号 */
    indexVersion?: string;
    /** 创建时间 */
    gmtCreate?: string;
    /** 修改时间 */
    gmtModified?: string;
    /** 创建者 */
    creator?: string;
    /** 修改者 */
    modifier?: string;
    /** 索引资源ID */
    indexResourceId?: string;
    /** 索引文件生成时间 */
    indexGmtCreate?: string;
  }

  interface PaginationResult_NamespaceVersionDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 结果数据列表 */
    data?: NamespaceVersionDTO[];
    /** 总个数 */
    total?: number;
    /** 每页个数 */
    size?: number;
    /** 当前页 */
    current?: number;
  }

  interface Result_object_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  type ReleaseLevel = 'EMERGENT' | 'NORMAL';

  interface ApplyReleaseDTO {
    releaseLevel: ReleaseLevel;
    reason?: string;
  }

  interface ParameterDirectUpdateDTO {
    parameterId?: string;
    description?: string;
  }

  interface DebugInfoDTO {
    namespaceName?: string;
    indexVersion?: string;
    changeVersion?: string;
  }

  interface Result_DebugInfoDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: DebugInfoDTO;
  }

  interface Result_Map_String_ConditionDTO__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>;
  }

  type TaskStageListDTO = Record<string, any>;

  interface Result_TaskStageListDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: TaskStageListDTO;
  }

  type TemplateInstanceDTO = Record<string, any>;

  interface Result_List_TemplateInstanceDTO__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: TemplateInstanceDTO[];
  }

  type TaskCmd = Record<string, any>;

  interface SmallFlowGrayCommand {
    /** 模板ID */
    templateId?: number;
    taskCmd?: TaskCmd;
    /** 跳过阶段ID(仅命令为 SKIP_STAGE 时有值) */
    skipStageId?: number;
  }

  interface Result_List_NamespaceDTO__ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: NamespaceDTO[];
  }

  type SkipType = 'BETA' | 'APPLY_RELEASE' | 'SMALLFLOW_GRAY' | 'VERIFY' | 'RATIO_GRAY' | 'ALL';

  interface SkipCommand {
    skipType: SkipType;
    reason?: string;
  }

  type ScanBetaLogType = Record<string, any>;

  interface Result_List_Map_String_String___ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: Record<string, any>[];
  }

  interface IdealabPredictCommand {
    appCode?: string;
    variableMap?: Record<string, any>;
  }

  interface ConditionExpressionDTO1 {
    key?: string;
    operator?: string;
    value?: string;
    children?: ConditionExpressionDTO[];
  }

  interface ExpressionDeviceCountQuery {
    appKey?: string;
    expression: ConditionExpressionDTO1;
  }

  interface Result_long_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: number;
  }

  type PublishTaskInfo = Record<string, any>;

  interface RatioGrayProgressDTOReleaseProgressNode {
    /** 开始时间（用户发起灰度时间） */
    startTime?: string;
    /** 探针生成时间(定时任务调度时间) */
    scheduleTime?: string;
    /** WMCC 任务ID */
    agatewareTaskId?: string;
    agatewareTaskInfo?: PublishTaskInfo;
  }

  interface RatioGrayProgressDTORatioGrayProgressNode {
    /** 开始时间（用户发起灰度时间） */
    startTime?: string;
    /** 探针生成时间(定时任务调度时间) */
    scheduleTime?: string;
    /** WMCC 任务ID */
    agatewareTaskId?: string;
    agatewareTaskInfo?: PublishTaskInfo;
    /** 灰度比例 */
    grayRatio?: number;
  }

  interface RatioGrayProgressDTO {
    releaseProgressNode?: RatioGrayProgressDTOReleaseProgressNode;
    /** 灰度发布进度列表 */
    ratioGrayProgressNodes?: RatioGrayProgressDTORatioGrayProgressNode[];
  }

  interface Result_RatioGrayProgressDTO_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    data?: RatioGrayProgressDTO;
  }

  interface Result_boolean_ {
    /** 是否成功 */
    success?: boolean;
    /** 响应码(Axxxx: 业务异常；Bxxxx: 当前系统异常；Cxxxx: 三方系统异常) */
    code?: string;
    /** 响应信息 */
    message?: string;
    /** 返回值 */
    data?: boolean;
  }
}

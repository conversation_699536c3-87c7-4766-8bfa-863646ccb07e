/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 GET /api/tasks/my */
export async function queryMyTasks(
  params: {
    // query
    /** 任务类型枚举 */
    taskType?: string;
    /** 任务处理人状态枚举 */
    status?: string;
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_TaskDetailDTO_>('/api/tasks/my', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/tasks/${param0}/ignore */
export async function ignore(
  params: {
    // path
    taskId?: string;
  },
  options?: { [key: string]: any },
) {
  const { taskId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/tasks/${param0}/ignore`, {
    method: 'PUT',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/tasks/${param0}/transfer */
export async function transfer(
  params: {
    // query
    workNo?: string;
    // path
    taskId?: string;
  },
  options?: { [key: string]: any },
) {
  const { taskId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/tasks/${param0}/transfer`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/tasks/my/count-by-status */
export async function countMyTaskByStatus(options?: { [key: string]: any }) {
  return request<API.Result_Map_TaskHandlerStatus_Long__>('/api/tasks/my/count-by-status', {
    method: 'GET',
    responseType: '*/*',
    ...(options || {}),
  });
}

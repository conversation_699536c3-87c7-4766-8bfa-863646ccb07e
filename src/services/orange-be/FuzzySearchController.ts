/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 GET /api/fuzzy-search/modules */
export async function modules(
  params: {
    // query
    appKey?: string;
    keyword?: string;
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_MtlModule_>('/api/fuzzy-search/modules', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/fuzzy-search/users */
export async function users(
  params: {
    // query
    keyword?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_List_User__>('/api/fuzzy-search/users', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

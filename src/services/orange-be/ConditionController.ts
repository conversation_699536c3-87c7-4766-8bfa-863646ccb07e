/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 GET /api/conditions */
export async function query(
  params: {
    // query
    namespaceId?: string;
    name?: string;
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_ConditionDetailDTO_>('/api/conditions', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/conditions/${param0} */
export async function getByConditionId(
  params: {
    // path
    conditionId?: string;
  },
  options?: { [key: string]: any },
) {
  const { conditionId: param0, ...queryParams } = params;
  return request<API.Result_ConditionDetailDTO_>(`/api/conditions/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/conditions/${param0} */
export async function update(
  params: {
    // path
    conditionId?: string;
  },
  body?: API.ConditionDirectUpdateDTO,
  options?: { [key: string]: any },
) {
  const { conditionId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/conditions/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/conditions/all */
export async function getAll(
  params: {
    // query
    namespaceId?: string;
    name?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_List_ConditionDTO__>('/api/conditions/all', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/conditions/map */
export async function getConditionMapByIds(
  params: {
    // query
    conditionIds?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_Map_String_ConditionDTO__>('/api/conditions/map', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/conditions/devices/count */
export async function countDevices(
  body?: API.ExpressionDeviceCountQuery,
  options?: { [key: string]: any },
) {
  return request<API.Result_long_>('/api/conditions/devices/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

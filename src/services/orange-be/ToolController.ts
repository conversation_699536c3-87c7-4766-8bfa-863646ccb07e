/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 GET /api/tools/resources */
export async function getResourceContent(
  params: {
    // query
    resourceId?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_object_>('/api/tools/resources', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/cancel */
export async function cancel(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/cancel`, {
    method: 'PUT',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders */
export async function query(
  params: {
    // query
    namespaceId?: string;
    statuses?: any[];
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_ReleaseOrderDTO_>('/api/release-orders', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/release-orders */
export async function create(body?: API.ReleaseOrderCreateDTO, options?: { [key: string]: any }) {
  return request<API.Result_string_>('/api/release-orders', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/operations */
export async function getOperations(
  params: {
    // query
    operationTypes?: API.OperationType[];
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_List_ReleaseOrderOperationDTO__>(
    `/api/release-orders/${param0}/operations`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      responseType: '*/*',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/publish */
export async function publish(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/publish`, {
    method: 'PUT',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0} */
export async function getDetail(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_ReleaseOrderDetailDTO_>(`/api/release-orders/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/changes */
export async function getChanges(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_ReleaseOrderChangesDTO_>(`/api/release-orders/${param0}/changes`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/ratio-gray */
export async function ratioGray(
  params: {
    // path
    releaseVersion?: string;
  },
  body?: API.RatioGrayDTO,
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/ratio-gray`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/count-by-status */
export async function countByStatus(
  params: {
    // query
    namespaceId?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_Map_ReleaseOrderStatus_Long__>('/api/release-orders/count-by-status', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/start-verify */
export async function startVerify(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/start-verify`, {
    method: 'PUT',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/verify-reply */
export async function verifyReply(
  params: {
    // path
    releaseVersion?: string;
  },
  body?: API.VerifyReplyDTO,
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/verify-reply`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/apply-release */
export async function applyRelease(
  params: {
    // path
    releaseVersion?: string;
  },
  body?: API.ApplyReleaseDTO,
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/apply-release`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/debug-info */
export async function getDebugInfo(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_DebugInfoDTO_>(`/api/release-orders/${param0}/debug-info`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/tiga-task-stage-list */
export async function getTigaTaskStageList(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_TaskStageListDTO_>(
    `/api/release-orders/${param0}/tiga-task-stage-list`,
    {
      method: 'PUT',
      params: { ...queryParams },
      responseType: '*/*',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/gray-templates */
export async function getGrayTemplates(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_List_TemplateInstanceDTO__>(
    `/api/release-orders/${param0}/gray-templates`,
    {
      method: 'GET',
      params: { ...queryParams },
      responseType: '*/*',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/tiga-gray */
export async function tigaGray(
  params: {
    // path
    releaseVersion?: string;
  },
  body?: API.SmallFlowGrayCommand,
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/tiga-gray`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/release-orders/${param0}/skip */
export async function skip(
  params: {
    // path
    releaseVersion?: string;
  },
  body?: API.SkipCommand,
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/skip`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/release-orders/${param0}/changefree/callback */
export async function changefreeCallback(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/release-orders/${param0}/changefree/callback`, {
    method: 'POST',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/scan-beta-logs/${param1} */
export async function getScanBetaLogs(
  params: {
    // query
    page?: number;
    size?: number;
    // path
    releaseVersion?: string;
    type?: API.ScanBetaLogType;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, type: param1, ...queryParams } = params;
  return request<API.Result_List_Map_String_String___>(
    `/api/release-orders/${param0}/scan-beta-logs/${param1}`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      responseType: '*/*',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/release-orders/${param0}/ratio-gray-progress */
export async function getRatioGrayProgress(
  params: {
    // path
    releaseVersion?: string;
  },
  options?: { [key: string]: any },
) {
  const { releaseVersion: param0, ...queryParams } = params;
  return request<API.Result_RatioGrayProgressDTO_>(
    `/api/release-orders/${param0}/ratio-gray-progress`,
    {
      method: 'GET',
      params: { ...queryParams },
      responseType: '*/*',
      ...(options || {}),
    },
  );
}

/* eslint-disable */
import request from '@/utils/apiRequest';

/** 获取所有命名空间列表
@return Result对象，包含命名空间列表数据
 GET /api/namespaces */
export async function query(
  params: {
    // query
    /** 应用 KEY */
    appKey?: string;
    /** 命名空间名称 */
    name?: string;
    /** 命名空间状态 */
    status?: string;
    /** 搜索关键字 */
    keyword?: string;
    /** 是否有权限 */
    hasPermission?: boolean;
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_NamespaceDTO_>('/api/namespaces', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 创建新的命名空间
@param namespace 需要创建的命名空间对象
@return Result对象，包含新创建的命名空间ID
 POST /api/namespaces */
export async function create(body?: API.NamespaceCreateDTO, options?: { [key: string]: any }) {
  return request<API.Result_string_>('/api/namespaces', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 根据ID获取指定命名空间
@param namespaceId 命名空间ID
@return Result对象，包含查询到的命名空间信息
 GET /api/namespaces/${param0} */
export async function getByNamespaceId(
  params: {
    // path
    /** 命名空间ID */
    namespaceId?: string;
  },
  options?: { [key: string]: any },
) {
  const { namespaceId: param0, ...queryParams } = params;
  return request<API.Result_NamespaceDTO_>(`/api/namespaces/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 更新指定ID的命名空间信息
@param namespaceId      待更新的命名空间ID
@param updatedNamespace 更新后的命名空间对象
 PUT /api/namespaces/${param0} */
export async function update(
  params: {
    // path
    /** 待更新的命名空间ID */
    namespaceId?: string;
  },
  body?: API.NamespaceUpdateDTO,
  options?: { [key: string]: any },
) {
  const { namespaceId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/namespaces/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** delete 删除指定ID的命名空间
@param namespaceId 命名空间ID
 DELETE /api/namespaces/${param0} */
export async function deleteUsingDELETE(
  params: {
    // path
    /** 命名空间ID */
    namespaceId?: string;
  },
  options?: { [key: string]: any },
) {
  const { namespaceId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/namespaces/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 获取指定命名空间的历史版本
@param namespaceId 命名空间ID
@param page        页码
@param size        每页大小
@return Result对象，包含历史版本列表数据
 GET /api/namespaces/${param0}/histories */
export async function histories(
  params: {
    // query
    /** 页码 */
    page?: number;
    /** 每页大小 */
    size?: number;
    // path
    /** 命名空间ID */
    namespaceId?: string;
  },
  options?: { [key: string]: any },
) {
  const { namespaceId: param0, ...queryParams } = params;
  return request<API.PaginationResult_NamespaceVersionDTO_>(`/api/namespaces/${param0}/histories`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 根据名称获取指定命名空间
@param namespaceName
@return 
 GET /api/namespaces/name/${param0} */
export async function getByNamespaceName(
  params: {
    // path
    namespaceName?: string;
  },
  options?: { [key: string]: any },
) {
  const { namespaceName: param0, ...queryParams } = params;
  return request<API.Result_NamespaceDTO_>(`/api/namespaces/name/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 获取当前用户常用的namespace列表
@param limit 返回数量限制，默认10，最大50
@return Result对象，包含常用namespace列表
 GET /api/namespaces/frequent */
export async function getFrequentNamespaces(
  params: {
    // query
    /** 返回数量限制，默认10，最大50 */
    limit?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_List_NamespaceDTO__>('/api/namespaces/frequent', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 获取当前用户最近访问的namespace列表
@param limit 返回数量限制，默认10，最大50
@return Result对象，包含最近访问namespace列表
 GET /api/namespaces/recent */
export async function getRecentNamespaces(
  params: {
    // query
    /** 返回数量限制，默认10，最大50 */
    limit?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_List_NamespaceDTO__>('/api/namespaces/recent', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

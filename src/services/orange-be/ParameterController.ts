/* eslint-disable */
import request from '@/utils/apiRequest';

/** 此处后端没有提供注释 GET /api/parameters */
export async function query(
  params: {
    // query
    namespaceId?: string;
    parameterKey?: string;
    /** 搜索关键字 */
    keyword?: string;
    page?: number;
    size?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.PaginationResult_ParameterDetailDTO_>('/api/parameters', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/parameters/${param0} */
export async function update(
  params: {
    // path
    parameterId?: string;
  },
  body?: API.ParameterDirectUpdateDTO,
  options?: { [key: string]: any },
) {
  const { parameterId: param0, ...queryParams } = params;
  return request<API.Result_void_>(`/api/parameters/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    responseType: '*/*',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/parameters/check-key-exists */
export async function checkParameterKeyExists(
  params: {
    // query
    namespaceId?: string;
    parameterKey?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_boolean_>('/api/parameters/check-key-exists', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: '*/*',
    ...(options || {}),
  });
}

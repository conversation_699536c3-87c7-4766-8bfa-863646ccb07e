import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export function getKeysFromProperties(value: string | undefined) {
  const lines = value?.split('\n') || [];
  const keys: string[] = [];
  lines.forEach((line) => {
    const match = line.match(/^([^=]+)=(.*)$/);
    if (match && match[1]) {
      keys.push(match[1]);
    }
  });
  return keys;
}

export function getJSONValueKeysFromProperties(value: string | undefined) {
  const map = propertiesStringToJson(value);
  return Object.keys(map).filter(key =>
    isValidJSON(map[key]),
  );
}

function propertiesStringToJson(propertiesString: string | undefined) {
  if (!propertiesString) {
    return {};
  }
  const jsonObj = {};

  // 以行分割每一对属性
  const lines = propertiesString.split('\n');

  lines.forEach(line => {
    // 忽略空行和注释
    line = line.trim();
    if (line === '' || line.startsWith('#')) {
      return;
    }

    // 找到第一个等号，以分割键和值
    const index = line.indexOf('=');
    if (index !== -1) {
      const key = line.substring(0, index).trim();
      const value = line.substring(index + 1).trim();
      jsonObj[key] = value;
    }
  });

  return jsonObj;
}

export function isValidJSON(value: string) {
  try {
    JSON.stringify(JSON.parse(value), null, 2);
    if (!value.trim().startsWith('{') && !value.trim().startsWith('[')) {
      return false;
    }
  } catch (error) {
    return false;
  }
  return true;
}

export function isSubstring(keywords, value) {
  const lowerKeywords = keywords.toLowerCase();
  const lowerValue = value.toLowerCase();

  if (lowerValue.includes(lowerKeywords)) {
    return true;
  }

  let i = 0;
  let j = 0;

  while (i < lowerKeywords.length && j < lowerValue.length) {
    if (lowerKeywords[i] === lowerValue[j]) {
      i++;
    }
    j++;
  }

  return i === lowerKeywords.length;
}

// 将指定 appKeys 提前
export function sortAppKeys(appKeys: string[], targetAppKeys: any[]) {
  if (!appKeys?.length || !targetAppKeys?.length) {
    return [];
  }

  const priorityApps = targetAppKeys.filter(app => appKeys.includes(app.appKey));
  const otherApps = targetAppKeys.filter(app => !appKeys.includes(app.appKey));

  return [...priorityApps, ...otherApps];
}

export function getApproximateNumberStr(num: number) {
  if (num >= 10000) {
    return `${Math.floor(num / 10000)}万+`;
  }
  return num.toString();
}


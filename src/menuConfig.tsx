import {
    AppstoreOutlined,
    DashboardOutlined,
    FileTextOutlined,
    HistoryOutlined,
    Pie<PERSON>hartOutlined,
    ToolOutlined,
} from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-layout';

const asideMenuConfig: MenuDataItem[] =
    [
        {
            key: '/workspace/dashboard',
            label: '工作台',
            icon: <DashboardOutlined />,
            source: 'pro',
        },
        {
            key: '/workspace/switch/namespaces',
            label: '命名空间',
            icon: <AppstoreOutlined />,
            source: 'pro',
        },
        {
            key: 'namespace',
            label: '配置管理',
            icon: <FileTextOutlined />,
            children: [
                {
                    label: '我的配置',
                    key: '/workspace/namespace/me',
                },
                {
                    label: '全部配置',
                    key: '/workspace/namespace/list',
                },
            ],
        },
        {
            key: 'version',
            label: '发布管理',
            icon: <HistoryOutlined />,
            children: [
                {
                    label: '发布列表',
                    key: '/workspace/version/list',
                },
            ],
        },
        {
            key: 'setting',
            label: '管理中心',
            icon: <PieChartOutlined />,
            children: [
                {
                    label: 'APP 列表',
                    key: '/workspace/setting/app',
                },
                {
                    label: 'Business 列表',
                    key: '/workspace/setting/business',
                },
            ],
        },
        {
            key: 'tool',
            label: '工具',
            icon: <ToolOutlined />,
            children: [
                {
                    label: '加速生效',
                    key: '/workspace/tool/fast',
                },
            ],
        },
    ];

export { asideMenuConfig };

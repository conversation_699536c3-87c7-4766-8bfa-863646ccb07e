module.exports = {
  apps: [
    {
      projectName: 'orange-be',
      groupName: 'orange',
      versionName: 'feature/20250824_26350718_iteration_one_leftover_issues_1',
      methodFilterType: 'exclude', // 可选值 include , exclude
      methodFilters: [
        // "Env.*",   // * 代表匹配该控制器下所有的方法
        'MainController.*',
      ],
    },
  ],
  typescript: true,
  namespace: 'API',
  servicesPath: './src/services',
  mockPath: './mock',
  mockScene: 'default',
  mockDelayTime: 1,
  mockSceneDetail: {},
  // 项目中所使用的 request 请求库，
  requestImportStatement: "import request from '@/utils/apiRequest'",
  // baseUrl: 'https://easy-api.alibaba-inc.com',
};
